.scroll-stack-scroller {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: visible;
  overscroll-behavior: contain;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  will-change: scroll-position;
}

.scroll-stack-inner {
  padding: 20vh 5rem 50rem;
  min-height: 100vh;
}

.scroll-stack-card-wrapper {
  position: relative;
}

.scroll-stack-card {
  transform-origin: top center;
  will-change: transform, filter;
  backface-visibility: hidden;
  transform-style: preserve-3d;
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.3);
  height: auto;
  min-height: 20rem;
  width: 100%;
  margin: 30px 0;
  padding: 3rem;
  border-radius: 40px;
  box-sizing: border-box;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  position: relative;
  background: rgba(20, 20, 20, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: visible;
}

.scroll-stack-end {
  width: 100%;
  height: 1px;
}

/* Ensure proper stacking context */
.scroll-stack-item > * {
  position: relative;
  z-index: 1;
}

/* Add subtle shadow for depth */
.scroll-stack-item::before {
  content: '';
  position: absolute;
  top: 10px;
  left: 10px;
  right: -10px;
  bottom: -10px;
  background: rgba(0, 0, 0, 0.4);
  border-radius: 25px;
  z-index: -1;
  opacity: 0.6;
  filter: blur(20px);
}

/* Different shadow intensities for stacking effect */
.scroll-stack-item:nth-child(1)::before {
  opacity: 0.8;
  filter: blur(25px);
}

.scroll-stack-item:nth-child(2)::before {
  opacity: 0.6;
  filter: blur(20px);
}

.scroll-stack-item:nth-child(3)::before {
  opacity: 0.4;
  filter: blur(15px);
}

.scroll-stack-item:nth-child(4)::before {
  opacity: 0.3;
  filter: blur(10px);
}

.scroll-stack-item:nth-child(5)::before {
  opacity: 0.2;
  filter: blur(8px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .scroll-stack {
    padding: 1rem 0;
  }

  .scroll-stack-container {
    perspective: 1000px;
    padding: 1rem 0;
  }

  .scroll-stack-item::before {
    top: 10px;
    left: 10px;
    right: -10px;
    bottom: -10px;
    filter: blur(5px);
  }
}
