.hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: #000;
  overflow: hidden;
  padding: 2rem;
}



.hero-content {
  text-align: center;
  z-index: 5;
  max-width: 800px;
}

.hero-title {
  margin-bottom: 2rem;
}

.main-title {
  color: #ffffff;
}

.hero-subtitle {
  margin-bottom: 2rem;
}

.subtitle {
  color: #ccc;
}

.hero-description {
  margin-top: 2rem;
}

.hero-description p {
  font-size: 1.1rem;
  color: #888;
  line-height: 1.6;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.hero-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(60px);
  opacity: 0.3;
  animation: float 6s ease-in-out infinite;
}

.orb-1 {
  width: 300px;
  height: 300px;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.orb-2 {
  width: 200px;
  height: 200px;
  background: linear-gradient(45deg, #a8e6cf, #ffd93d);
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.orb-3 {
  width: 250px;
  height: 250px;
  background: linear-gradient(45deg, #ff8a80, #82b1ff);
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero {
    padding: 1rem;
  }
  

  
  .main-title {
    font-size: 4rem !important;
  }
  
  .subtitle {
    font-size: 1.2rem !important;
  }
  
  .hero-description p {
    font-size: 1rem;
  }
  
  .gradient-orb {
    filter: blur(40px);
  }
  
  .orb-1 {
    width: 200px;
    height: 200px;
  }
  
  .orb-2 {
    width: 150px;
    height: 150px;
  }
  
  .orb-3 {
    width: 180px;
    height: 180px;
  }
}
