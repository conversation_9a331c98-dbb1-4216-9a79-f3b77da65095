import React, { useState, useEffect, useRef } from 'react';
import './ScrollFloat.css';

const ScrollFloat = ({ 
  children, 
  className = "", 
  style = {},
  threshold = 0.1,
  duration = 0.6,
  delay = 0,
  direction = 'up',
  distance = 50
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [hasAnimated, setHasAnimated] = useState(false);
  const elementRef = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setTimeout(() => {
            setIsVisible(true);
            setHasAnimated(true);
          }, delay * 1000);
        } else if (hasAnimated) {
          // Reset animation when scrolling back up
          setIsVisible(false);
          setTimeout(() => {
            setHasAnimated(false);
          }, 100);
        }
      },
      {
        threshold: threshold,
        rootMargin: '0px 0px -100px 0px'
      }
    );

    if (elementRef.current) {
      observer.observe(elementRef.current);
    }

    return () => {
      if (elementRef.current) {
        observer.unobserve(elementRef.current);
      }
    };
  }, [threshold, delay, hasAnimated]);

  const getTransform = () => {
    if (isVisible) return 'translate3d(0, 0, 0)';
    
    switch (direction) {
      case 'up':
        return `translate3d(0, ${distance}px, 0)`;
      case 'down':
        return `translate3d(0, -${distance}px, 0)`;
      case 'left':
        return `translate3d(${distance}px, 0, 0)`;
      case 'right':
        return `translate3d(-${distance}px, 0, 0)`;
      default:
        return `translate3d(0, ${distance}px, 0)`;
    }
  };

  return (
    <div
      ref={elementRef}
      className={`scroll-float ${className}`}
      style={{
        transform: getTransform(),
        opacity: isVisible ? 1 : 0,
        transition: `all ${duration}s cubic-bezier(0.25, 0.46, 0.45, 0.94)`,
        ...style
      }}
    >
      {children}
    </div>
  );
};

export default ScrollFloat;
