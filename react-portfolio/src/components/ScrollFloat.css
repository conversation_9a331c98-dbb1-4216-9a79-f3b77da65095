.scroll-float {
  will-change: transform, opacity;
}

/* Additional smooth animation classes */
.scroll-float.fade-in {
  transition: opacity 0.8s ease-out, transform 0.8s ease-out;
}

.scroll-float.slide-up {
  transition: opacity 0.6s ease-out, transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.scroll-float.slide-down {
  transition: opacity 0.6s ease-out, transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.scroll-float.slide-left {
  transition: opacity 0.6s ease-out, transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.scroll-float.slide-right {
  transition: opacity 0.6s ease-out, transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Stagger animation delays for multiple elements */
.scroll-float.delay-1 {
  transition-delay: 0.1s;
}

.scroll-float.delay-2 {
  transition-delay: 0.2s;
}

.scroll-float.delay-3 {
  transition-delay: 0.3s;
}

.scroll-float.delay-4 {
  transition-delay: 0.4s;
}

.scroll-float.delay-5 {
  transition-delay: 0.5s;
}

.scroll-float.delay-6 {
  transition-delay: 0.6s;
}
