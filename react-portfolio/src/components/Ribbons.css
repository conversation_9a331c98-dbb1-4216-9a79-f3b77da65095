.ribbons-container {
  position: relative;
  background: rgba(0, 0, 0, 0.4);
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.ribbons-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.2);
}

.ribbons-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
}

.ribbon {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: var(--ribbon-width, 2px);
  background: linear-gradient(
    90deg,
    transparent 0%,
    var(--ribbon-color, rgba(255, 255, 255, 0.1)) 50%,
    transparent 100%
  );
  transform-origin: center;
  animation: ribbonFlow var(--ribbon-speed, 2000ms) linear infinite;
  animation-delay: var(--ribbon-delay, 0s);
  opacity: 0.3;
  transition: all 0.3s ease;
}

.ribbon:nth-child(1) {
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 107, 107, 0.3) 50%,
    transparent 100%
  );
}

.ribbon:nth-child(2) {
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(78, 205, 196, 0.3) 50%,
    transparent 100%
  );
}

.ribbon:nth-child(3) {
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 217, 61, 0.3) 50%,
    transparent 100%
  );
}

.ribbon:nth-child(4) {
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(168, 230, 207, 0.3) 50%,
    transparent 100%
  );
}

.ribbon:nth-child(5) {
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 138, 128, 0.3) 50%,
    transparent 100%
  );
}

.ribbons-content {
  position: relative;
  z-index: 2;
  padding: 2rem;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
}

@keyframes ribbonFlow {
  0% {
    transform: translateX(-100%) rotate(var(--ribbon-rotation, 0deg));
    opacity: 0;
  }
  10% {
    opacity: 0.6;
  }
  90% {
    opacity: 0.6;
  }
  100% {
    transform: translateX(100%) rotate(var(--ribbon-rotation, 0deg));
    opacity: 0;
  }
}

/* Responsive */
@media (max-width: 768px) {
  .ribbons-content {
    padding: 1.5rem;
  }
  
  .ribbon {
    height: 1px;
  }
}
