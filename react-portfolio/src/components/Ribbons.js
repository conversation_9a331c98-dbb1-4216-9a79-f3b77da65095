import React, { useState, useRef, useEffect } from 'react';
import './Ribbons.css';

const Ribbons = ({ 
  children, 
  className = "", 
  style = {},
  ribbonColor = "rgba(255, 255, 255, 0.1)",
  ribbonWidth = 2,
  ribbonSpeed = 2000
}) => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);
  const containerRef = useRef(null);

  useEffect(() => {
    const handleMouseMove = (e) => {
      if (containerRef.current && isHovering) {
        const rect = containerRef.current.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        setMousePosition({ x, y });
      }
    };

    document.addEventListener('mousemove', handleMouseMove);
    return () => document.removeEventListener('mousemove', handleMouseMove);
  }, [isHovering]);

  const handleMouseEnter = () => {
    setIsHovering(true);
  };

  const handleMouseLeave = () => {
    setIsHovering(false);
    setMousePosition({ x: 0, y: 0 });
  };

  return (
    <div
      ref={containerRef}
      className={`ribbons-container ${className}`}
      style={style}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* Animated ribbons */}
      <div className="ribbons-wrapper">
        {[...Array(5)].map((_, index) => (
          <div
            key={index}
            className="ribbon"
            style={{
              '--ribbon-delay': `${index * 0.5}s`,
              '--ribbon-color': ribbonColor,
              '--ribbon-width': `${ribbonWidth}px`,
              '--ribbon-speed': `${ribbonSpeed}ms`,
              opacity: isHovering ? 0.8 : 0.3,
              transform: isHovering 
                ? `translate(${mousePosition.x * 0.1}px, ${mousePosition.y * 0.1}px) rotate(${index * 15}deg)`
                : `rotate(${index * 15}deg)`
            }}
          />
        ))}
      </div>

      {/* Content */}
      <div className="ribbons-content">
        {children}
      </div>
    </div>
  );
};

export default Ribbons;
