.projects {
  min-height: 100vh;
  padding: 5rem 2rem 0;
  background: #000;
  position: relative;
  overflow: visible;
}

.projects::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 80% 20%, rgba(78, 205, 196, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 20% 80%, rgba(255, 107, 107, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.projects-container {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.projects-header {
  text-align: center;
  margin-bottom: 4rem;
}

.projects-title {
  font-size: 3.5rem;
  font-weight: 700;
  color: #fff;
  margin-bottom: 1rem;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.projects-subtitle {
  font-size: 1.2rem;
  color: #888;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Projects Stack Wrapper */
.projects-stack-wrapper {
  position: relative;
  height: 100vh; /* Full viewport height for scroll detection */
  overflow: visible; /* No scrollbars */
}

/* Project Card Styles */
.project-card {
  width: 100%;
  min-height: 320px;
  position: relative;
}

.project-content {
  display: flex;
  gap: 2rem;
  align-items: flex-start;
  height: 100%;
  position: relative;
  z-index: 2;
}

.project-icon {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.project-emoji {
  font-size: 4rem;
  display: block;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

.project-status {
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.project-status.completed {
  background: rgba(78, 205, 196, 0.2);
  color: #4ecdc4;
  border: 1px solid rgba(78, 205, 196, 0.3);
}

.project-status.in-progress {
  background: rgba(255, 217, 61, 0.2);
  color: #ffd93d;
  border: 1px solid rgba(255, 217, 61, 0.3);
}

.project-status.in-development {
  background: rgba(255, 107, 107, 0.2);
  color: #ff6b6b;
  border: 1px solid rgba(255, 107, 107, 0.3);
}

.project-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.project-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: #fff;
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.project-description {
  font-size: 1rem;
  color: #ccc;
  line-height: 1.6;
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.project-technologies {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin: 0.5rem 0;
}

.tech-tag {
  padding: 0.4rem 0.8rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  font-size: 0.8rem;
  color: #fff;
  font-weight: 500;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  transition: all 0.3s ease;
}

.tech-tag:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px);
}

.project-actions {
  margin-top: auto;
  padding-top: 1rem;
}

.project-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.8rem 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 25px;
  color: #fff;
  text-decoration: none;
  font-weight: 500;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  transition: all 0.3s ease;
  position: relative;
  overflow: visible;
}

.project-link:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.link-arrow {
  transition: transform 0.3s ease;
}

.project-link:hover .link-arrow {
  transform: translateX(5px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .projects {
    padding: 3rem 1rem;
  }
  
  .projects-title {
    font-size: 2.5rem;
  }
  
  .projects-subtitle {
    font-size: 1rem;
  }
  
  .project-content {
    flex-direction: column;
    text-align: center;
    gap: 1.5rem;
  }
  
  .project-emoji {
    font-size: 3rem;
  }
  
  .project-title {
    font-size: 1.5rem;
  }
  
  .project-description {
    font-size: 0.9rem;
  }
  
  .tech-tag {
    font-size: 0.7rem;
    padding: 0.3rem 0.6rem;
  }
}

@media (max-width: 480px) {
  .projects {
    padding: 2rem 1rem;
  }
  
  .projects-title {
    font-size: 2rem;
  }
  
  .project-card {
    margin-bottom: 2rem;
  }
  
  .project-technologies {
    justify-content: center;
  }
}
