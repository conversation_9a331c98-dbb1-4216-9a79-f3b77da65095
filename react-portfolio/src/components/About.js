import React from 'react';
import MagicBento from './MagicBento';
import ScrollFloat from './ScrollFloat';
import './About.css';

const About = () => {
  const skills = [
    {
      icon: '💻',
      title: 'Frontend Development',
      description: 'React, Vue.js, TypeScript, Next.js and modern frontend technologies'
    },
    {
      icon: '🎨',
      title: 'UI/UX Design',
      description: 'User-centered design and interactive interfaces with modern aesthetics'
    },
    {
      icon: '⚡',
      title: 'Performance',
      description: 'Optimization, fast loading times and exceptional user experience'
    },
    {
      icon: '🔧',
      title: 'Backend Development',
      description: 'Node.js, Python, API development and database management'
    },
    {
      icon: '📱',
      title: 'Mobile Development',
      description: 'React Native, Flutter and cross-platform applications'
    },
    {
      icon: '☁️',
      title: 'Cloud & DevOps',
      description: 'AWS, Docker, CI/CD and modern deployment strategies'
    }
  ];

  return (
    <section className="about" id="about">
      <div className="about-container">
        <ScrollFloat direction="up" duration={0.8} delay={0.2}>
          <div className="about-header">
            <h2 className="about-title">About Me</h2>
            <p className="about-subtitle">
              I'm a Full-Stack Developer who creates modern and innovative web experiences
            </p>
          </div>
        </ScrollFloat>

        <div className="bento-grid">
          {/* Main About Card */}
          <ScrollFloat direction="up" duration={0.7} delay={0.4}>
            <MagicBento className="bento-main">
              <div className="about-main-content">
                <div className="profile-image">
                  <div className="profile-placeholder">👨‍💻</div>
                </div>
                <h3>DevDoses</h3>
                <p>
                  5+ years of experience in web development. I create high-quality,
                  user-centered applications that combine technical excellence
                  with creative design and modern best practices.
                </p>
              </div>
            </MagicBento>
          </ScrollFloat>

          {/* Skills Grid */}
          {skills.map((skill, index) => (
            <ScrollFloat
              key={index}
              direction="up"
              duration={0.6}
              delay={0.6 + (index * 0.1)}
            >
              <MagicBento className="bento-skill">
                <div className="skill-content">
                  <div className="icon">{skill.icon}</div>
                  <h3>{skill.title}</h3>
                  <p>{skill.description}</p>
                </div>
              </MagicBento>
            </ScrollFloat>
          ))}

          {/* Experience Card */}
          <ScrollFloat direction="up" duration={0.7} delay={1.2}>
            <MagicBento className="bento-experience">
              <div className="experience-content">
                <div className="icon">🏆</div>
                <h3>Experience</h3>
                <div className="experience-stats">
                  <div className="stat">
                    <span className="stat-number">50+</span>
                    <span className="stat-label">Projects</span>
                  </div>
                  <div className="stat">
                    <span className="stat-number">5+</span>
                    <span className="stat-label">Years</span>
                  </div>
                  <div className="stat">
                    <span className="stat-number">30+</span>
                    <span className="stat-label">Clients</span>
                  </div>
                </div>
              </div>
            </MagicBento>
          </ScrollFloat>
        </div>
      </div>
    </section>
  );
};

export default About;
