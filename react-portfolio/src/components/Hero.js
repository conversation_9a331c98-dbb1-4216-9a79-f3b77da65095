import React from 'react';
import TextPressure from './TextPressure';
import ShinyText from './ShinyText';
import Navigation from './Navigation';
import ScrollFloat from './ScrollFloat';
import './Hero.css';

const Hero = () => {
  return (
    <section className="hero" id="home">
      {/* Navigation Menu */}
      <Navigation />

      {/* Main Content */}
      <div className="hero-content">
        <ScrollFloat direction="up" duration={0.8} delay={0.3}>
          <div className="hero-title">
            <TextPressure
              text="DevDoses"
              fontSize="6rem"
              pressureIntensity={0.5}
              initialOpacity={1}
              className="main-title"
            />
          </div>
        </ScrollFloat>

        <ScrollFloat direction="up" duration={0.7} delay={0.6}>
          <div className="hero-subtitle">
            <ShinyText
              text="Creative Developer Portfolio"
              fontSize="1.5rem"
              shimmerDuration={3000}
              className="subtitle"
            />
          </div>
        </ScrollFloat>

        <ScrollFloat direction="up" duration={0.6} delay={0.9}>
          <div className="hero-description">
            <p>Building amazing digital experiences with modern web technologies</p>
          </div>
        </ScrollFloat>
      </div>

      {/* Background Elements */}
      <div className="hero-bg">
        <div className="gradient-orb orb-1"></div>
        <div className="gradient-orb orb-2"></div>
        <div className="gradient-orb orb-3"></div>
      </div>
    </section>
  );
};

export default Hero;
