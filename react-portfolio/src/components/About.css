.about {
  min-height: 100vh;
  padding: 5rem 2rem;
  background: #000;
  position: relative;
  overflow: visible;
}

.about::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.about-container {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.about-header {
  text-align: center;
  margin-bottom: 4rem;
}

.about-title {
  font-size: 3.5rem;
  font-weight: 700;
  color: #fff;
  margin-bottom: 1rem;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.about-subtitle {
  font-size: 1.2rem;
  color: #888;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.bento-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* Main About Card */
.bento-main {
  grid-column: span 3;
  min-height: 280px;
}

.about-main-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  height: 100%;
  justify-content: center;
}

.profile-image {
  margin-bottom: 1.5rem;
}

.profile-placeholder {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.about-main-content h3 {
  font-size: 2rem;
  color: #fff;
  margin-bottom: 1rem;
  font-weight: 600;
}

.about-main-content p {
  font-size: 1.1rem;
  color: #ccc;
  line-height: 1.7;
  max-width: 500px;
}

/* Skills Cards */
.bento-skill {
  min-height: 280px;
  grid-column: span 1;
}

.skill-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  height: 100%;
  justify-content: center;
}

/* Experience Card */
.bento-experience {
  grid-column: span 3;
  min-height: 280px;
}

.experience-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  height: 100%;
  justify-content: center;
}

.experience-stats {
  display: flex;
  gap: 3rem;
  margin-top: 1.5rem;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: #fff;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.stat-label {
  font-size: 0.9rem;
  color: #888;
  margin-top: 0.5rem;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .bento-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .bento-main {
    grid-column: span 2;
  }

  .bento-experience {
    grid-column: span 2;
  }

  .bento-skill {
    grid-column: span 1;
  }
}

@media (max-width: 768px) {
  .about {
    padding: 3rem 1rem;
  }

  .about-title {
    font-size: 2.5rem;
  }

  .about-subtitle {
    font-size: 1rem;
  }

  .bento-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .bento-main,
  .bento-experience,
  .bento-skill {
    grid-column: span 1;
    min-height: 250px;
  }

  .experience-stats {
    gap: 2rem;
    flex-wrap: wrap;
  }

  .stat-number {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .about {
    padding: 2rem 1rem;
  }

  .about-title {
    font-size: 2rem;
  }

  .bento-grid {
    gap: 1rem;
  }

  .bento-main,
  .bento-experience,
  .bento-skill {
    min-height: 220px;
  }

  .experience-stats {
    gap: 1.5rem;
  }

  .stat-number {
    font-size: 1.8rem;
  }
}
