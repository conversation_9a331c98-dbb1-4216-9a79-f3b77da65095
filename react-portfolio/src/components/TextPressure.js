import React, { useState, useRef, useEffect } from 'react';
import './TextPressure.css';

const TextPressure = ({
  text = "DevDoses",
  className = "",
  style = {},
  pressureIntensity = 0.4,
  fontSize = "4rem",
  initialOpacity = 0.1
}) => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);
  const textRef = useRef(null);

  useEffect(() => {
    const handleMouseMove = (e) => {
      if (textRef.current && isHovering) {
        const rect = textRef.current.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;

        setMousePosition({
          x: (e.clientX - centerX) / rect.width,
          y: (e.clientY - centerY) / rect.height
        });
      }
    };

    document.addEventListener('mousemove', handleMouseMove);
    return () => document.removeEventListener('mousemove', handleMouseMove);
  }, [isHovering]);

  const getLetterStyle = (index, totalLetters) => {
    const letterPosition = (index / totalLetters) - 0.5;
    const distanceFromMouse = Math.abs(letterPosition - mousePosition.x);
    const pressure = isHovering ? Math.max(0, 1 - distanceFromMouse * 2) : 0;

    // Base opacity when not hovering
    const baseOpacity = isHovering ? 1 : initialOpacity;
    const finalOpacity = baseOpacity + (pressure * (1 - baseOpacity));

    return {
      opacity: finalOpacity,
      transform: `
        translateY(${mousePosition.y * pressure * 20}px)
        scale(${1 + pressure * pressureIntensity})
      `,
      textShadow: isHovering ? `0 0 ${pressure * 30}px rgba(255, 255, 255, ${pressure * 0.8})` : 'none',
      transition: isHovering ? 'all 0.1s ease-out' : 'opacity 0.3s ease-out'
    };
  };

  return (
    <div
      ref={textRef}
      className={`text-pressure ${className}`}
      style={{ fontSize, ...style }}
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
    >
      {text.split('').map((letter, index) => (
        <span
          key={index}
          className="text-pressure-letter"
          style={getLetterStyle(index, text.length)}
        >
          {letter === ' ' ? '\u00A0' : letter}
        </span>
      ))}
    </div>
  );
};

export default TextPressure;
