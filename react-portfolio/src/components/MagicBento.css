.magic-bento {
  position: relative;
  background: rgba(0, 0, 0, 0.4);
  border-radius: 20px;
  padding: 2px;
  overflow: visible;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.magic-bento:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.magic-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 20px;
  pointer-events: none;
  transition: all 0.3s ease;
  z-index: 1;
}

.magic-border {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 20px;
  padding: 1px;
  pointer-events: none;
  z-index: 0;
}

.magic-border::before {
  content: '';
  position: absolute;
  top: 1px;
  left: 1px;
  right: 1px;
  bottom: 1px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 19px;
  z-index: -1;
}

.magic-content {
  position: relative;
  z-index: 2;
  padding: 1.5rem;
  border-radius: 18px;
  /* background: rgba(0, 0, 0, 0.6); */
  backdrop-filter: blur(20px);
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  min-height: 200px;
}

.magic-content h3 {
  color: #fff;
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 0.8rem;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.magic-content p {
  color: #ccc;
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: 0.5rem;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  max-width: 280px;
}

.magic-content .icon {
  font-size: 2.5rem;
  margin-bottom: 0.8rem;
  opacity: 0.8;
  transition: all 0.3s ease;
}

.magic-bento:hover .magic-content .icon {
  opacity: 1;
  transform: scale(1.1);
}

/* Responsive */
@media (max-width: 768px) {
  .magic-content {
    padding: 1.5rem;
  }
  
  .magic-content h3 {
    font-size: 1.3rem;
  }
  
  .magic-content p {
    font-size: 0.9rem;
  }
  
  .magic-content .icon {
    font-size: 2.5rem;
  }
}
