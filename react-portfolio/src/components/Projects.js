import React from 'react';
import ScrollFloat from './ScrollFloat';
import './Projects.css';

const Projects = () => {
  const projects = [
    {
      id: 1,
      title: 'E-Commerce Platform',
      description: 'Modern e-commerce solution built with React, Node.js, and MongoDB. Features include real-time inventory, payment processing, and admin dashboard.',
      technologies: ['React', 'Node.js', 'MongoDB', 'Stripe', 'Socket.io'],
      image: '🛒',
      status: 'Completed',
      link: '#'
    },
    {
      id: 2,
      title: 'Task Management App',
      description: 'Collaborative task management application with real-time updates, team collaboration, and advanced project tracking capabilities.',
      technologies: ['Vue.js', 'Express', 'PostgreSQL', 'WebSocket', 'Docker'],
      image: '📋',
      status: 'In Progress',
      link: '#'
    },
    {
      id: 3,
      title: 'AI Chat Assistant',
      description: 'Intelligent chatbot powered by machine learning algorithms. Provides customer support automation and natural language processing.',
      technologies: ['Python', 'TensorFlow', 'FastAPI', 'React', 'Redis'],
      image: '🤖',
      status: 'Completed',
      link: '#'
    },
    {
      id: 4,
      title: 'Social Media Dashboard',
      description: 'Comprehensive social media management platform with analytics, scheduling, and multi-platform integration capabilities.',
      technologies: ['Next.js', 'TypeScript', 'Prisma', 'GraphQL', 'AWS'],
      image: '📱',
      status: 'Completed',
      link: '#'
    },
    {
      id: 5,
      title: 'Crypto Trading Bot',
      description: 'Automated cryptocurrency trading bot with advanced algorithms, risk management, and real-time market analysis.',
      technologies: ['Python', 'Django', 'Celery', 'PostgreSQL', 'Docker'],
      image: '₿',
      status: 'In Development',
      link: '#'
    }
  ];

  return (
    <section className="projects" id="projects">
      <div className="projects-container">
        <ScrollFloat direction="up" duration={0.8} delay={0.2}>
          <div className="projects-header">
            <h2 className="projects-title">Featured Projects</h2>
            <p className="projects-subtitle">
              Showcasing innovative solutions and cutting-edge technologies
            </p>
          </div>
        </ScrollFloat>

        <div className="projects-grid">
            {projects.map((project, index) => (
              <div key={project.id} className="project-card">
                  <div className="project-content">
                    <div className="project-icon">
                      <span className="project-emoji">{project.image}</span>
                      <div className={`project-status ${project.status.toLowerCase().replace(' ', '-')}`}>
                        {project.status}
                      </div>
                    </div>

                    <div className="project-info">
                      <h3 className="project-title">{project.title}</h3>
                      <p className="project-description">{project.description}</p>

                      <div className="project-technologies">
                        {project.technologies.map((tech, techIndex) => (
                          <span key={techIndex} className="tech-tag">
                            {tech}
                          </span>
                        ))}
                      </div>

                      <div className="project-actions">
                        <a href={project.link} className="project-link">
                          View Project
                          <span className="link-arrow">→</span>
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
            ))}
        </div>
      </div>
    </section>
  );
};

export default Projects;
