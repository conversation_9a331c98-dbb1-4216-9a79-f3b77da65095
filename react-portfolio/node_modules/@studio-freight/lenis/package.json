{"name": "@studio-freight/lenis", "version": "1.0.42", "description": "Lenis is a smooth scroll library to normalize and smooth the scrolling experience across devices", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/darkroomengineering/lenis.git"}, "bugs": {"url": "https://github.com/darkroomengineering/lenis/issues"}, "files": ["dist"], "scripts": {"dev": "npm-run-all --parallel watch playground", "playground": "npm run dev --prefix ./playground", "build": "npm-run-all --parallel build:dist", "build:dist": "rollup --bundleConfigAsCjs -c", "watch:dist": "rollup --bundleConfigAsCjs -c -w", "watch": "npm-run-all --parallel watch:dist", "readme": "node update-readme.js", "git:commit": "git add -A && cross-var git commit -m \"$npm_package_name@$npm_package_version\"", "git:tag": "cross-var git tag -a $npm_package_name@$npm_package_version -m \"$npm_package_name@$npm_package_version\"", "git:push": "git push && git push --tags", "postversion": "npm-run-all build readme git:commit git:tag git:push", "patch:latest": "yarn version --no-git-tag-version --patch", "patch:dev": "yarn version --no-git-tag-version --prerelease --preid dev"}, "sideEffects": false, "source": "src/index.js", "main": "dist/lenis.umd.js", "unpkg": "dist/lenis.min.js", "module": "dist/lenis.mjs", "types": "dist/types/index.d.ts", "exports": {"./types": "./dist/types/index.d.ts", "./package.json": "./package.json", ".": {"import": {"types": "./dist/types/index.d.ts", "default": "./dist/lenis.mjs"}, "require": {"types": "./dist/types/index.d.ts", "default": "./dist/lenis.cjs.js"}}}}