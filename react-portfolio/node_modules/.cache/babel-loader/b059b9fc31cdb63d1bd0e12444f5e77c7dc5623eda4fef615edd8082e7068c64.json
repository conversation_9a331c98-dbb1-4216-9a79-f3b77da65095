{"ast": null, "code": "// package.json\nvar version = \"1.3.8\";\n\n// packages/core/src/maths.ts\nfunction clamp(min, input, max) {\n  return Math.max(min, Math.min(input, max));\n}\nfunction lerp(x, y, t) {\n  return (1 - t) * x + t * y;\n}\nfunction damp(x, y, lambda, deltaTime) {\n  return lerp(x, y, 1 - Math.exp(-lambda * deltaTime));\n}\nfunction modulo(n, d) {\n  return (n % d + d) % d;\n}\n\n// packages/core/src/animate.ts\nvar Animate = class {\n  isRunning = false;\n  value = 0;\n  from = 0;\n  to = 0;\n  currentTime = 0;\n  // These are instanciated in the fromTo method\n  lerp;\n  duration;\n  easing;\n  onUpdate;\n  /**\n   * Advance the animation by the given delta time\n   *\n   * @param deltaTime - The time in seconds to advance the animation\n   */\n  advance(deltaTime) {\n    if (!this.isRunning) return;\n    let completed = false;\n    if (this.duration && this.easing) {\n      this.currentTime += deltaTime;\n      const linearProgress = clamp(0, this.currentTime / this.duration, 1);\n      completed = linearProgress >= 1;\n      const easedProgress = completed ? 1 : this.easing(linearProgress);\n      this.value = this.from + (this.to - this.from) * easedProgress;\n    } else if (this.lerp) {\n      this.value = damp(this.value, this.to, this.lerp * 60, deltaTime);\n      if (Math.round(this.value) === this.to) {\n        this.value = this.to;\n        completed = true;\n      }\n    } else {\n      this.value = this.to;\n      completed = true;\n    }\n    if (completed) {\n      this.stop();\n    }\n    this.onUpdate?.(this.value, completed);\n  }\n  /** Stop the animation */\n  stop() {\n    this.isRunning = false;\n  }\n  /**\n   * Set up the animation from a starting value to an ending value\n   * with optional parameters for lerping, duration, easing, and onUpdate callback\n   *\n   * @param from - The starting value\n   * @param to - The ending value\n   * @param options - Options for the animation\n   */\n  fromTo(from, to, {\n    lerp: lerp2,\n    duration,\n    easing,\n    onStart,\n    onUpdate\n  }) {\n    this.from = this.value = from;\n    this.to = to;\n    this.lerp = lerp2;\n    this.duration = duration;\n    this.easing = easing;\n    this.currentTime = 0;\n    this.isRunning = true;\n    onStart?.();\n    this.onUpdate = onUpdate;\n  }\n};\n\n// packages/core/src/debounce.ts\nfunction debounce(callback, delay) {\n  let timer;\n  return function (...args) {\n    let context = this;\n    clearTimeout(timer);\n    timer = setTimeout(() => {\n      timer = void 0;\n      callback.apply(context, args);\n    }, delay);\n  };\n}\n\n// packages/core/src/dimensions.ts\nvar Dimensions = class {\n  constructor(wrapper, content, {\n    autoResize = true,\n    debounce: debounceValue = 250\n  } = {}) {\n    this.wrapper = wrapper;\n    this.content = content;\n    if (autoResize) {\n      this.debouncedResize = debounce(this.resize, debounceValue);\n      if (this.wrapper instanceof Window) {\n        window.addEventListener(\"resize\", this.debouncedResize, false);\n      } else {\n        this.wrapperResizeObserver = new ResizeObserver(this.debouncedResize);\n        this.wrapperResizeObserver.observe(this.wrapper);\n      }\n      this.contentResizeObserver = new ResizeObserver(this.debouncedResize);\n      this.contentResizeObserver.observe(this.content);\n    }\n    this.resize();\n  }\n  width = 0;\n  height = 0;\n  scrollHeight = 0;\n  scrollWidth = 0;\n  // These are instanciated in the constructor as they need information from the options\n  debouncedResize;\n  wrapperResizeObserver;\n  contentResizeObserver;\n  destroy() {\n    this.wrapperResizeObserver?.disconnect();\n    this.contentResizeObserver?.disconnect();\n    if (this.wrapper === window && this.debouncedResize) {\n      window.removeEventListener(\"resize\", this.debouncedResize, false);\n    }\n  }\n  resize = () => {\n    this.onWrapperResize();\n    this.onContentResize();\n  };\n  onWrapperResize = () => {\n    if (this.wrapper instanceof Window) {\n      this.width = window.innerWidth;\n      this.height = window.innerHeight;\n    } else {\n      this.width = this.wrapper.clientWidth;\n      this.height = this.wrapper.clientHeight;\n    }\n  };\n  onContentResize = () => {\n    if (this.wrapper instanceof Window) {\n      this.scrollHeight = this.content.scrollHeight;\n      this.scrollWidth = this.content.scrollWidth;\n    } else {\n      this.scrollHeight = this.wrapper.scrollHeight;\n      this.scrollWidth = this.wrapper.scrollWidth;\n    }\n  };\n  get limit() {\n    return {\n      x: this.scrollWidth - this.width,\n      y: this.scrollHeight - this.height\n    };\n  }\n};\n\n// packages/core/src/emitter.ts\nvar Emitter = class {\n  events = {};\n  /**\n   * Emit an event with the given data\n   * @param event Event name\n   * @param args Data to pass to the event handlers\n   */\n  emit(event, ...args) {\n    let callbacks = this.events[event] || [];\n    for (let i = 0, length = callbacks.length; i < length; i++) {\n      callbacks[i]?.(...args);\n    }\n  }\n  /**\n   * Add a callback to the event\n   * @param event Event name\n   * @param cb Callback function\n   * @returns Unsubscribe function\n   */\n  on(event, cb) {\n    this.events[event]?.push(cb) || (this.events[event] = [cb]);\n    return () => {\n      this.events[event] = this.events[event]?.filter(i => cb !== i);\n    };\n  }\n  /**\n   * Remove a callback from the event\n   * @param event Event name\n   * @param callback Callback function\n   */\n  off(event, callback) {\n    this.events[event] = this.events[event]?.filter(i => callback !== i);\n  }\n  /**\n   * Remove all event listeners and clean up\n   */\n  destroy() {\n    this.events = {};\n  }\n};\n\n// packages/core/src/virtual-scroll.ts\nvar LINE_HEIGHT = 100 / 6;\nvar listenerOptions = {\n  passive: false\n};\nvar VirtualScroll = class {\n  constructor(element, options = {\n    wheelMultiplier: 1,\n    touchMultiplier: 1\n  }) {\n    this.element = element;\n    this.options = options;\n    window.addEventListener(\"resize\", this.onWindowResize, false);\n    this.onWindowResize();\n    this.element.addEventListener(\"wheel\", this.onWheel, listenerOptions);\n    this.element.addEventListener(\"touchstart\", this.onTouchStart, listenerOptions);\n    this.element.addEventListener(\"touchmove\", this.onTouchMove, listenerOptions);\n    this.element.addEventListener(\"touchend\", this.onTouchEnd, listenerOptions);\n  }\n  touchStart = {\n    x: 0,\n    y: 0\n  };\n  lastDelta = {\n    x: 0,\n    y: 0\n  };\n  window = {\n    width: 0,\n    height: 0\n  };\n  emitter = new Emitter();\n  /**\n   * Add an event listener for the given event and callback\n   *\n   * @param event Event name\n   * @param callback Callback function\n   */\n  on(event, callback) {\n    return this.emitter.on(event, callback);\n  }\n  /** Remove all event listeners and clean up */\n  destroy() {\n    this.emitter.destroy();\n    window.removeEventListener(\"resize\", this.onWindowResize, false);\n    this.element.removeEventListener(\"wheel\", this.onWheel, listenerOptions);\n    this.element.removeEventListener(\"touchstart\", this.onTouchStart, listenerOptions);\n    this.element.removeEventListener(\"touchmove\", this.onTouchMove, listenerOptions);\n    this.element.removeEventListener(\"touchend\", this.onTouchEnd, listenerOptions);\n  }\n  /**\n   * Event handler for 'touchstart' event\n   *\n   * @param event Touch event\n   */\n  onTouchStart = event => {\n    const {\n      clientX,\n      clientY\n    } = event.targetTouches ? event.targetTouches[0] : event;\n    this.touchStart.x = clientX;\n    this.touchStart.y = clientY;\n    this.lastDelta = {\n      x: 0,\n      y: 0\n    };\n    this.emitter.emit(\"scroll\", {\n      deltaX: 0,\n      deltaY: 0,\n      event\n    });\n  };\n  /** Event handler for 'touchmove' event */\n  onTouchMove = event => {\n    const {\n      clientX,\n      clientY\n    } = event.targetTouches ? event.targetTouches[0] : event;\n    const deltaX = -(clientX - this.touchStart.x) * this.options.touchMultiplier;\n    const deltaY = -(clientY - this.touchStart.y) * this.options.touchMultiplier;\n    this.touchStart.x = clientX;\n    this.touchStart.y = clientY;\n    this.lastDelta = {\n      x: deltaX,\n      y: deltaY\n    };\n    this.emitter.emit(\"scroll\", {\n      deltaX,\n      deltaY,\n      event\n    });\n  };\n  onTouchEnd = event => {\n    this.emitter.emit(\"scroll\", {\n      deltaX: this.lastDelta.x,\n      deltaY: this.lastDelta.y,\n      event\n    });\n  };\n  /** Event handler for 'wheel' event */\n  onWheel = event => {\n    let {\n      deltaX,\n      deltaY,\n      deltaMode\n    } = event;\n    const multiplierX = deltaMode === 1 ? LINE_HEIGHT : deltaMode === 2 ? this.window.width : 1;\n    const multiplierY = deltaMode === 1 ? LINE_HEIGHT : deltaMode === 2 ? this.window.height : 1;\n    deltaX *= multiplierX;\n    deltaY *= multiplierY;\n    deltaX *= this.options.wheelMultiplier;\n    deltaY *= this.options.wheelMultiplier;\n    this.emitter.emit(\"scroll\", {\n      deltaX,\n      deltaY,\n      event\n    });\n  };\n  onWindowResize = () => {\n    this.window = {\n      width: window.innerWidth,\n      height: window.innerHeight\n    };\n  };\n};\n\n// packages/core/src/lenis.ts\nvar defaultEasing = t => Math.min(1, 1.001 - Math.pow(2, -10 * t));\nvar Lenis = class {\n  _isScrolling = false;\n  // true when scroll is animating\n  _isStopped = false;\n  // true if user should not be able to scroll - enable/disable programmatically\n  _isLocked = false;\n  // same as isStopped but enabled/disabled when scroll reaches target\n  _preventNextNativeScrollEvent = false;\n  _resetVelocityTimeout = null;\n  __rafID = null;\n  /**\n   * Whether or not the user is touching the screen\n   */\n  isTouching;\n  /**\n   * The time in ms since the lenis instance was created\n   */\n  time = 0;\n  /**\n   * User data that will be forwarded through the scroll event\n   *\n   * @example\n   * lenis.scrollTo(100, {\n   *   userData: {\n   *     foo: 'bar'\n   *   }\n   * })\n   */\n  userData = {};\n  /**\n   * The last velocity of the scroll\n   */\n  lastVelocity = 0;\n  /**\n   * The current velocity of the scroll\n   */\n  velocity = 0;\n  /**\n   * The direction of the scroll\n   */\n  direction = 0;\n  /**\n   * The options passed to the lenis instance\n   */\n  options;\n  /**\n   * The target scroll value\n   */\n  targetScroll;\n  /**\n   * The animated scroll value\n   */\n  animatedScroll;\n  // These are instanciated here as they don't need information from the options\n  animate = new Animate();\n  emitter = new Emitter();\n  // These are instanciated in the constructor as they need information from the options\n  dimensions;\n  // This is not private because it's used in the Snap class\n  virtualScroll;\n  constructor({\n    wrapper = window,\n    content = document.documentElement,\n    eventsTarget = wrapper,\n    smoothWheel = true,\n    syncTouch = false,\n    syncTouchLerp = 0.075,\n    touchInertiaExponent = 1.7,\n    duration,\n    // in seconds\n    easing,\n    lerp: lerp2 = 0.1,\n    infinite = false,\n    orientation = \"vertical\",\n    // vertical, horizontal\n    gestureOrientation = \"vertical\",\n    // vertical, horizontal, both\n    touchMultiplier = 1,\n    wheelMultiplier = 1,\n    autoResize = true,\n    prevent,\n    virtualScroll,\n    overscroll = true,\n    autoRaf = false,\n    anchors = false,\n    autoToggle = false,\n    // https://caniuse.com/?search=transition-behavior\n    allowNestedScroll = false,\n    __experimental__naiveDimensions = false\n  } = {}) {\n    window.lenisVersion = version;\n    if (!wrapper || wrapper === document.documentElement) {\n      wrapper = window;\n    }\n    if (typeof duration === \"number\" && typeof easing !== \"function\") {\n      easing = defaultEasing;\n    } else if (typeof easing === \"function\" && typeof duration !== \"number\") {\n      duration = 1;\n    }\n    this.options = {\n      wrapper,\n      content,\n      eventsTarget,\n      smoothWheel,\n      syncTouch,\n      syncTouchLerp,\n      touchInertiaExponent,\n      duration,\n      easing,\n      lerp: lerp2,\n      infinite,\n      gestureOrientation,\n      orientation,\n      touchMultiplier,\n      wheelMultiplier,\n      autoResize,\n      prevent,\n      virtualScroll,\n      overscroll,\n      autoRaf,\n      anchors,\n      autoToggle,\n      allowNestedScroll,\n      __experimental__naiveDimensions\n    };\n    this.dimensions = new Dimensions(wrapper, content, {\n      autoResize\n    });\n    this.updateClassName();\n    this.targetScroll = this.animatedScroll = this.actualScroll;\n    this.options.wrapper.addEventListener(\"scroll\", this.onNativeScroll, false);\n    this.options.wrapper.addEventListener(\"scrollend\", this.onScrollEnd, {\n      capture: true\n    });\n    if (this.options.anchors && this.options.wrapper === window) {\n      this.options.wrapper.addEventListener(\"click\", this.onClick, false);\n    }\n    this.options.wrapper.addEventListener(\"pointerdown\", this.onPointerDown, false);\n    this.virtualScroll = new VirtualScroll(eventsTarget, {\n      touchMultiplier,\n      wheelMultiplier\n    });\n    this.virtualScroll.on(\"scroll\", this.onVirtualScroll);\n    if (this.options.autoToggle) {\n      this.rootElement.addEventListener(\"transitionend\", this.onTransitionEnd, {\n        passive: true\n      });\n    }\n    if (this.options.autoRaf) {\n      this.__rafID = requestAnimationFrame(this.raf);\n    }\n  }\n  /**\n   * Destroy the lenis instance, remove all event listeners and clean up the class name\n   */\n  destroy() {\n    this.emitter.destroy();\n    this.options.wrapper.removeEventListener(\"scroll\", this.onNativeScroll, false);\n    this.options.wrapper.removeEventListener(\"scrollend\", this.onScrollEnd, {\n      capture: true\n    });\n    this.options.wrapper.removeEventListener(\"pointerdown\", this.onPointerDown, false);\n    if (this.options.anchors && this.options.wrapper === window) {\n      this.options.wrapper.removeEventListener(\"click\", this.onClick, false);\n    }\n    this.virtualScroll.destroy();\n    this.dimensions.destroy();\n    this.cleanUpClassName();\n    if (this.__rafID) {\n      cancelAnimationFrame(this.__rafID);\n    }\n  }\n  on(event, callback) {\n    return this.emitter.on(event, callback);\n  }\n  off(event, callback) {\n    return this.emitter.off(event, callback);\n  }\n  onScrollEnd = e => {\n    if (!(e instanceof CustomEvent)) {\n      if (this.isScrolling === \"smooth\" || this.isScrolling === false) {\n        e.stopPropagation();\n      }\n    }\n  };\n  dispatchScrollendEvent = () => {\n    this.options.wrapper.dispatchEvent(new CustomEvent(\"scrollend\", {\n      bubbles: this.options.wrapper === window,\n      // cancelable: false,\n      detail: {\n        lenisScrollEnd: true\n      }\n    }));\n  };\n  onTransitionEnd = event => {\n    if (event.propertyName.includes(\"overflow\")) {\n      const property = this.isHorizontal ? \"overflow-x\" : \"overflow-y\";\n      const overflow = getComputedStyle(this.rootElement)[property];\n      if ([\"hidden\", \"clip\"].includes(overflow)) {\n        this.internalStop();\n      } else {\n        this.internalStart();\n      }\n    }\n  };\n  setScroll(scroll) {\n    if (this.isHorizontal) {\n      this.options.wrapper.scrollTo({\n        left: scroll,\n        behavior: \"instant\"\n      });\n    } else {\n      this.options.wrapper.scrollTo({\n        top: scroll,\n        behavior: \"instant\"\n      });\n    }\n  }\n  onClick = event => {\n    const path = event.composedPath();\n    const anchor = path.find(node => node instanceof HTMLAnchorElement && (node.getAttribute(\"href\")?.startsWith(\"#\") || node.getAttribute(\"href\")?.startsWith(\"/#\") || node.getAttribute(\"href\")?.startsWith(\"./#\")));\n    if (anchor) {\n      const id = anchor.getAttribute(\"href\");\n      if (id) {\n        const options = typeof this.options.anchors === \"object\" && this.options.anchors ? this.options.anchors : void 0;\n        let target = `#${id.split(\"#\")[1]}`;\n        if ([\"#\", \"/#\", \"./#\", \"#top\", \"/#top\", \"./#top\"].includes(id)) {\n          target = 0;\n        }\n        this.scrollTo(target, options);\n      }\n    }\n  };\n  onPointerDown = event => {\n    if (event.button === 1) {\n      this.reset();\n    }\n  };\n  onVirtualScroll = data => {\n    if (typeof this.options.virtualScroll === \"function\" && this.options.virtualScroll(data) === false) return;\n    const {\n      deltaX,\n      deltaY,\n      event\n    } = data;\n    this.emitter.emit(\"virtual-scroll\", {\n      deltaX,\n      deltaY,\n      event\n    });\n    if (event.ctrlKey) return;\n    if (event.lenisStopPropagation) return;\n    const isTouch = event.type.includes(\"touch\");\n    const isWheel = event.type.includes(\"wheel\");\n    this.isTouching = event.type === \"touchstart\" || event.type === \"touchmove\";\n    const isClickOrTap = deltaX === 0 && deltaY === 0;\n    const isTapToStop = this.options.syncTouch && isTouch && event.type === \"touchstart\" && isClickOrTap && !this.isStopped && !this.isLocked;\n    if (isTapToStop) {\n      this.reset();\n      return;\n    }\n    const isUnknownGesture = this.options.gestureOrientation === \"vertical\" && deltaY === 0 || this.options.gestureOrientation === \"horizontal\" && deltaX === 0;\n    if (isClickOrTap || isUnknownGesture) {\n      return;\n    }\n    let composedPath = event.composedPath();\n    composedPath = composedPath.slice(0, composedPath.indexOf(this.rootElement));\n    const prevent = this.options.prevent;\n    if (!!composedPath.find(node => node instanceof HTMLElement && (typeof prevent === \"function\" && prevent?.(node) || node.hasAttribute?.(\"data-lenis-prevent\") || isTouch && node.hasAttribute?.(\"data-lenis-prevent-touch\") || isWheel && node.hasAttribute?.(\"data-lenis-prevent-wheel\") || this.options.allowNestedScroll && this.checkNestedScroll(node, {\n      deltaX,\n      deltaY\n    })))) return;\n    if (this.isStopped || this.isLocked) {\n      if (event.cancelable) {\n        event.preventDefault();\n      }\n      return;\n    }\n    const isSmooth = this.options.syncTouch && isTouch || this.options.smoothWheel && isWheel;\n    if (!isSmooth) {\n      this.isScrolling = \"native\";\n      this.animate.stop();\n      event.lenisStopPropagation = true;\n      return;\n    }\n    let delta = deltaY;\n    if (this.options.gestureOrientation === \"both\") {\n      delta = Math.abs(deltaY) > Math.abs(deltaX) ? deltaY : deltaX;\n    } else if (this.options.gestureOrientation === \"horizontal\") {\n      delta = deltaX;\n    }\n    if (!this.options.overscroll || this.options.infinite || this.options.wrapper !== window && (this.animatedScroll > 0 && this.animatedScroll < this.limit || this.animatedScroll === 0 && deltaY > 0 || this.animatedScroll === this.limit && deltaY < 0)) {\n      event.lenisStopPropagation = true;\n    }\n    if (event.cancelable) {\n      event.preventDefault();\n    }\n    const isSyncTouch = isTouch && this.options.syncTouch;\n    const isTouchEnd = isTouch && event.type === \"touchend\";\n    const hasTouchInertia = isTouchEnd;\n    if (hasTouchInertia) {\n      delta = Math.sign(this.velocity) * Math.pow(Math.abs(this.velocity), this.options.touchInertiaExponent);\n    }\n    this.scrollTo(this.targetScroll + delta, {\n      programmatic: false,\n      ...(isSyncTouch ? {\n        lerp: hasTouchInertia ? this.options.syncTouchLerp : 1\n        // immediate: !hasTouchInertia,\n      } : {\n        lerp: this.options.lerp,\n        duration: this.options.duration,\n        easing: this.options.easing\n      })\n    });\n  };\n  /**\n   * Force lenis to recalculate the dimensions\n   */\n  resize() {\n    this.dimensions.resize();\n    this.animatedScroll = this.targetScroll = this.actualScroll;\n    this.emit();\n  }\n  emit() {\n    this.emitter.emit(\"scroll\", this);\n  }\n  onNativeScroll = () => {\n    if (this._resetVelocityTimeout !== null) {\n      clearTimeout(this._resetVelocityTimeout);\n      this._resetVelocityTimeout = null;\n    }\n    if (this._preventNextNativeScrollEvent) {\n      this._preventNextNativeScrollEvent = false;\n      return;\n    }\n    if (this.isScrolling === false || this.isScrolling === \"native\") {\n      const lastScroll = this.animatedScroll;\n      this.animatedScroll = this.targetScroll = this.actualScroll;\n      this.lastVelocity = this.velocity;\n      this.velocity = this.animatedScroll - lastScroll;\n      this.direction = Math.sign(this.animatedScroll - lastScroll);\n      if (!this.isStopped) {\n        this.isScrolling = \"native\";\n      }\n      this.emit();\n      if (this.velocity !== 0) {\n        this._resetVelocityTimeout = setTimeout(() => {\n          this.lastVelocity = this.velocity;\n          this.velocity = 0;\n          this.isScrolling = false;\n          this.emit();\n        }, 400);\n      }\n    }\n  };\n  reset() {\n    this.isLocked = false;\n    this.isScrolling = false;\n    this.animatedScroll = this.targetScroll = this.actualScroll;\n    this.lastVelocity = this.velocity = 0;\n    this.animate.stop();\n  }\n  /**\n   * Start lenis scroll after it has been stopped\n   */\n  start() {\n    if (!this.isStopped) return;\n    if (this.options.autoToggle) {\n      this.rootElement.style.removeProperty(\"overflow\");\n      return;\n    }\n    this.internalStart();\n  }\n  internalStart() {\n    if (!this.isStopped) return;\n    this.reset();\n    this.isStopped = false;\n    this.emit();\n  }\n  /**\n   * Stop lenis scroll\n   */\n  stop() {\n    if (this.isStopped) return;\n    if (this.options.autoToggle) {\n      this.rootElement.style.setProperty(\"overflow\", \"clip\");\n      return;\n    }\n    this.internalStop();\n  }\n  internalStop() {\n    if (this.isStopped) return;\n    this.reset();\n    this.isStopped = true;\n    this.emit();\n  }\n  /**\n   * RequestAnimationFrame for lenis\n   *\n   * @param time The time in ms from an external clock like `requestAnimationFrame` or Tempus\n   */\n  raf = time => {\n    const deltaTime = time - (this.time || time);\n    this.time = time;\n    this.animate.advance(deltaTime * 1e-3);\n    if (this.options.autoRaf) {\n      this.__rafID = requestAnimationFrame(this.raf);\n    }\n  };\n  /**\n   * Scroll to a target value\n   *\n   * @param target The target value to scroll to\n   * @param options The options for the scroll\n   *\n   * @example\n   * lenis.scrollTo(100, {\n   *   offset: 100,\n   *   duration: 1,\n   *   easing: (t) => 1 - Math.cos((t * Math.PI) / 2),\n   *   lerp: 0.1,\n   *   onStart: () => {\n   *     console.log('onStart')\n   *   },\n   *   onComplete: () => {\n   *     console.log('onComplete')\n   *   },\n   * })\n   */\n  scrollTo(target, {\n    offset = 0,\n    immediate = false,\n    lock = false,\n    duration = this.options.duration,\n    easing = this.options.easing,\n    lerp: lerp2 = this.options.lerp,\n    onStart,\n    onComplete,\n    force = false,\n    // scroll even if stopped\n    programmatic = true,\n    // called from outside of the class\n    userData\n  } = {}) {\n    if ((this.isStopped || this.isLocked) && !force) return;\n    if (typeof target === \"string\" && [\"top\", \"left\", \"start\"].includes(target)) {\n      target = 0;\n    } else if (typeof target === \"string\" && [\"bottom\", \"right\", \"end\"].includes(target)) {\n      target = this.limit;\n    } else {\n      let node;\n      if (typeof target === \"string\") {\n        node = document.querySelector(target);\n      } else if (target instanceof HTMLElement && target?.nodeType) {\n        node = target;\n      }\n      if (node) {\n        if (this.options.wrapper !== window) {\n          const wrapperRect = this.rootElement.getBoundingClientRect();\n          offset -= this.isHorizontal ? wrapperRect.left : wrapperRect.top;\n        }\n        const rect = node.getBoundingClientRect();\n        target = (this.isHorizontal ? rect.left : rect.top) + this.animatedScroll;\n      }\n    }\n    if (typeof target !== \"number\") return;\n    target += offset;\n    target = Math.round(target);\n    if (this.options.infinite) {\n      if (programmatic) {\n        this.targetScroll = this.animatedScroll = this.scroll;\n        const distance = target - this.animatedScroll;\n        if (distance > this.limit / 2) {\n          target = target - this.limit;\n        } else if (distance < -this.limit / 2) {\n          target = target + this.limit;\n        }\n      }\n    } else {\n      target = clamp(0, target, this.limit);\n    }\n    if (target === this.targetScroll) {\n      onStart?.(this);\n      onComplete?.(this);\n      return;\n    }\n    this.userData = userData ?? {};\n    if (immediate) {\n      this.animatedScroll = this.targetScroll = target;\n      this.setScroll(this.scroll);\n      this.reset();\n      this.preventNextNativeScrollEvent();\n      this.emit();\n      onComplete?.(this);\n      this.userData = {};\n      requestAnimationFrame(() => {\n        this.dispatchScrollendEvent();\n      });\n      return;\n    }\n    if (!programmatic) {\n      this.targetScroll = target;\n    }\n    if (typeof duration === \"number\" && typeof easing !== \"function\") {\n      easing = defaultEasing;\n    } else if (typeof easing === \"function\" && typeof duration !== \"number\") {\n      duration = 1;\n    }\n    this.animate.fromTo(this.animatedScroll, target, {\n      duration,\n      easing,\n      lerp: lerp2,\n      onStart: () => {\n        if (lock) this.isLocked = true;\n        this.isScrolling = \"smooth\";\n        onStart?.(this);\n      },\n      onUpdate: (value, completed) => {\n        this.isScrolling = \"smooth\";\n        this.lastVelocity = this.velocity;\n        this.velocity = value - this.animatedScroll;\n        this.direction = Math.sign(this.velocity);\n        this.animatedScroll = value;\n        this.setScroll(this.scroll);\n        if (programmatic) {\n          this.targetScroll = value;\n        }\n        if (!completed) this.emit();\n        if (completed) {\n          this.reset();\n          this.emit();\n          onComplete?.(this);\n          this.userData = {};\n          requestAnimationFrame(() => {\n            this.dispatchScrollendEvent();\n          });\n          this.preventNextNativeScrollEvent();\n        }\n      }\n    });\n  }\n  preventNextNativeScrollEvent() {\n    this._preventNextNativeScrollEvent = true;\n    requestAnimationFrame(() => {\n      this._preventNextNativeScrollEvent = false;\n    });\n  }\n  checkNestedScroll(node, {\n    deltaX,\n    deltaY\n  }) {\n    const time = Date.now();\n    const cache = node._lenis ??= {};\n    let hasOverflowX, hasOverflowY, isScrollableX, isScrollableY, scrollWidth, scrollHeight, clientWidth, clientHeight;\n    const gestureOrientation = this.options.gestureOrientation;\n    if (time - (cache.time ?? 0) > 2e3) {\n      cache.time = Date.now();\n      const computedStyle = window.getComputedStyle(node);\n      cache.computedStyle = computedStyle;\n      const overflowXString = computedStyle.overflowX;\n      const overflowYString = computedStyle.overflowY;\n      hasOverflowX = [\"auto\", \"overlay\", \"scroll\"].includes(overflowXString);\n      hasOverflowY = [\"auto\", \"overlay\", \"scroll\"].includes(overflowYString);\n      cache.hasOverflowX = hasOverflowX;\n      cache.hasOverflowY = hasOverflowY;\n      if (!hasOverflowX && !hasOverflowY) return false;\n      if (gestureOrientation === \"vertical\" && !hasOverflowY) return false;\n      if (gestureOrientation === \"horizontal\" && !hasOverflowX) return false;\n      scrollWidth = node.scrollWidth;\n      scrollHeight = node.scrollHeight;\n      clientWidth = node.clientWidth;\n      clientHeight = node.clientHeight;\n      isScrollableX = scrollWidth > clientWidth;\n      isScrollableY = scrollHeight > clientHeight;\n      cache.isScrollableX = isScrollableX;\n      cache.isScrollableY = isScrollableY;\n      cache.scrollWidth = scrollWidth;\n      cache.scrollHeight = scrollHeight;\n      cache.clientWidth = clientWidth;\n      cache.clientHeight = clientHeight;\n    } else {\n      isScrollableX = cache.isScrollableX;\n      isScrollableY = cache.isScrollableY;\n      hasOverflowX = cache.hasOverflowX;\n      hasOverflowY = cache.hasOverflowY;\n      scrollWidth = cache.scrollWidth;\n      scrollHeight = cache.scrollHeight;\n      clientWidth = cache.clientWidth;\n      clientHeight = cache.clientHeight;\n    }\n    if (!hasOverflowX && !hasOverflowY || !isScrollableX && !isScrollableY) {\n      return false;\n    }\n    if (gestureOrientation === \"vertical\" && (!hasOverflowY || !isScrollableY)) return false;\n    if (gestureOrientation === \"horizontal\" && (!hasOverflowX || !isScrollableX)) return false;\n    let orientation;\n    if (gestureOrientation === \"horizontal\") {\n      orientation = \"x\";\n    } else if (gestureOrientation === \"vertical\") {\n      orientation = \"y\";\n    } else {\n      const isScrollingX = deltaX !== 0;\n      const isScrollingY = deltaY !== 0;\n      if (isScrollingX && hasOverflowX && isScrollableX) {\n        orientation = \"x\";\n      }\n      if (isScrollingY && hasOverflowY && isScrollableY) {\n        orientation = \"y\";\n      }\n    }\n    if (!orientation) return false;\n    let scroll, maxScroll, delta, hasOverflow, isScrollable;\n    if (orientation === \"x\") {\n      scroll = node.scrollLeft;\n      maxScroll = scrollWidth - clientWidth;\n      delta = deltaX;\n      hasOverflow = hasOverflowX;\n      isScrollable = isScrollableX;\n    } else if (orientation === \"y\") {\n      scroll = node.scrollTop;\n      maxScroll = scrollHeight - clientHeight;\n      delta = deltaY;\n      hasOverflow = hasOverflowY;\n      isScrollable = isScrollableY;\n    } else {\n      return false;\n    }\n    const willScroll = delta > 0 ? scroll < maxScroll : scroll > 0;\n    return willScroll && hasOverflow && isScrollable;\n  }\n  /**\n   * The root element on which lenis is instanced\n   */\n  get rootElement() {\n    return this.options.wrapper === window ? document.documentElement : this.options.wrapper;\n  }\n  /**\n   * The limit which is the maximum scroll value\n   */\n  get limit() {\n    if (this.options.__experimental__naiveDimensions) {\n      if (this.isHorizontal) {\n        return this.rootElement.scrollWidth - this.rootElement.clientWidth;\n      } else {\n        return this.rootElement.scrollHeight - this.rootElement.clientHeight;\n      }\n    } else {\n      return this.dimensions.limit[this.isHorizontal ? \"x\" : \"y\"];\n    }\n  }\n  /**\n   * Whether or not the scroll is horizontal\n   */\n  get isHorizontal() {\n    return this.options.orientation === \"horizontal\";\n  }\n  /**\n   * The actual scroll value\n   */\n  get actualScroll() {\n    const wrapper = this.options.wrapper;\n    return this.isHorizontal ? wrapper.scrollX ?? wrapper.scrollLeft : wrapper.scrollY ?? wrapper.scrollTop;\n  }\n  /**\n   * The current scroll value\n   */\n  get scroll() {\n    return this.options.infinite ? modulo(this.animatedScroll, this.limit) : this.animatedScroll;\n  }\n  /**\n   * The progress of the scroll relative to the limit\n   */\n  get progress() {\n    return this.limit === 0 ? 1 : this.scroll / this.limit;\n  }\n  /**\n   * Current scroll state\n   */\n  get isScrolling() {\n    return this._isScrolling;\n  }\n  set isScrolling(value) {\n    if (this._isScrolling !== value) {\n      this._isScrolling = value;\n      this.updateClassName();\n    }\n  }\n  /**\n   * Check if lenis is stopped\n   */\n  get isStopped() {\n    return this._isStopped;\n  }\n  set isStopped(value) {\n    if (this._isStopped !== value) {\n      this._isStopped = value;\n      this.updateClassName();\n    }\n  }\n  /**\n   * Check if lenis is locked\n   */\n  get isLocked() {\n    return this._isLocked;\n  }\n  set isLocked(value) {\n    if (this._isLocked !== value) {\n      this._isLocked = value;\n      this.updateClassName();\n    }\n  }\n  /**\n   * Check if lenis is smooth scrolling\n   */\n  get isSmooth() {\n    return this.isScrolling === \"smooth\";\n  }\n  /**\n   * The class name applied to the wrapper element\n   */\n  get className() {\n    let className = \"lenis\";\n    if (this.options.autoToggle) className += \" lenis-autoToggle\";\n    if (this.isStopped) className += \" lenis-stopped\";\n    if (this.isLocked) className += \" lenis-locked\";\n    if (this.isScrolling) className += \" lenis-scrolling\";\n    if (this.isScrolling === \"smooth\") className += \" lenis-smooth\";\n    return className;\n  }\n  updateClassName() {\n    this.cleanUpClassName();\n    this.rootElement.className = `${this.rootElement.className} ${this.className}`.trim();\n  }\n  cleanUpClassName() {\n    this.rootElement.className = this.rootElement.className.replace(/lenis(-\\w+)?/g, \"\").trim();\n  }\n};\nexport { Lenis as default };", "map": {"version": 3, "names": ["version", "clamp", "min", "input", "max", "Math", "lerp", "x", "y", "t", "damp", "lambda", "deltaTime", "exp", "modulo", "n", "d", "Animate", "isRunning", "value", "from", "to", "currentTime", "duration", "easing", "onUpdate", "advance", "completed", "linearProgress", "easedProgress", "round", "stop", "fromTo", "lerp2", "onStart", "debounce", "callback", "delay", "timer", "args", "context", "clearTimeout", "setTimeout", "apply", "Dimensions", "constructor", "wrapper", "content", "autoResize", "debounceValue", "debouncedResize", "resize", "Window", "window", "addEventListener", "wrapperResizeObserver", "ResizeObserver", "observe", "contentResizeObserver", "width", "height", "scrollHeight", "scrollWidth", "destroy", "disconnect", "removeEventListener", "onWrapperResize", "onContentResize", "innerWidth", "innerHeight", "clientWidth", "clientHeight", "limit", "Emitter", "events", "emit", "event", "callbacks", "i", "length", "on", "cb", "push", "filter", "off", "LINE_HEIGHT", "listenerOptions", "passive", "VirtualScroll", "element", "options", "wheelMultiplier", "touchMultiplier", "onWindowResize", "onWheel", "onTouchStart", "onTouchMove", "onTouchEnd", "touchStart", "<PERSON><PERSON><PERSON><PERSON>", "emitter", "clientX", "clientY", "targetTouches", "deltaX", "deltaY", "deltaMode", "multiplierX", "multiplierY", "defaultEasing", "pow", "<PERSON><PERSON>", "_isScrolling", "_isStopped", "_isLocked", "_preventNextNativeScrollEvent", "_resetVelocityTimeout", "__raf<PERSON>", "isTouching", "time", "userData", "lastVelocity", "velocity", "direction", "targetScroll", "animatedScroll", "animate", "dimensions", "virtualScroll", "document", "documentElement", "eventsTarget", "smoothWheel", "syncTouch", "syncTouchLerp", "touchInertiaExponent", "infinite", "orientation", "gestureOrientation", "prevent", "overscroll", "autoRaf", "anchors", "autoToggle", "allowNestedScroll", "__experimental__naiveDimensions", "lenisVersion", "updateClassName", "actualScroll", "onNativeScroll", "onScrollEnd", "capture", "onClick", "onPointerDown", "onVirtualScroll", "rootElement", "onTransitionEnd", "requestAnimationFrame", "raf", "cleanUpClassName", "cancelAnimationFrame", "e", "CustomEvent", "isScrolling", "stopPropagation", "dispatchScrollendEvent", "dispatchEvent", "bubbles", "detail", "lenisScrollEnd", "propertyName", "includes", "property", "isHorizontal", "overflow", "getComputedStyle", "internalStop", "internalStart", "setScroll", "scroll", "scrollTo", "left", "behavior", "top", "path", "<PERSON><PERSON><PERSON>", "anchor", "find", "node", "HTMLAnchorElement", "getAttribute", "startsWith", "id", "target", "split", "button", "reset", "data", "ctrl<PERSON>ey", "lenisStopPropagation", "is<PERSON><PERSON>ch", "type", "isWheel", "isClickOrTap", "isTapToStop", "isStopped", "isLocked", "isUnknownGesture", "slice", "indexOf", "HTMLElement", "hasAttribute", "checkNestedScroll", "cancelable", "preventDefault", "isSmooth", "delta", "abs", "isSyncTouch", "isTouchEnd", "hasTouchInertia", "sign", "programmatic", "lastScroll", "start", "style", "removeProperty", "setProperty", "offset", "immediate", "lock", "onComplete", "force", "querySelector", "nodeType", "wrapperRect", "getBoundingClientRect", "rect", "distance", "preventNextNativeScrollEvent", "Date", "now", "cache", "_lenis", "hasOverflowX", "hasOverflowY", "isScrollableX", "isScrollableY", "computedStyle", "overflowXString", "overflowX", "overflowYString", "overflowY", "isScrollingX", "isScrollingY", "maxScroll", "hasOverflow", "isScrollable", "scrollLeft", "scrollTop", "willScroll", "scrollX", "scrollY", "progress", "className", "trim", "replace"], "sources": ["/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/node_modules/lenis/package.json", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/node_modules/lenis/packages/core/src/maths.ts", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/node_modules/lenis/packages/core/src/animate.ts", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/node_modules/lenis/packages/core/src/debounce.ts", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/node_modules/lenis/packages/core/src/dimensions.ts", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/node_modules/lenis/packages/core/src/emitter.ts", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/node_modules/lenis/packages/core/src/virtual-scroll.ts", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/node_modules/lenis/packages/core/src/lenis.ts"], "sourcesContent": ["{\n  \"name\": \"lenis\",\n  \"version\": \"1.3.8\",\n  \"description\": \"How smooth scroll should be\",\n  \"type\": \"module\",\n  \"sideEffects\": false,\n  \"author\": \"darkroom.engineering\",\n  \"license\": \"MIT\",\n  \"repository\": {\n    \"type\": \"git\",\n    \"url\": \"git+https://github.com/darkroomengineering/lenis.git\"\n  },\n  \"bugs\": {\n    \"url\": \"https://github.com/darkroomengineering/lenis/issues\"\n  },\n  \"homepage\": \"https://github.com/darkroomengineering/lenis\",\n  \"funding\": {\n    \"type\": \"github\",\n    \"url\": \"https://github.com/sponsors/darkroomengineering\"\n  },\n  \"keywords\": [\n    \"scroll\",\n    \"smooth\",\n    \"lenis\",\n    \"react\",\n    \"vue\"\n  ],\n  \"scripts\": {\n    \"build\": \"pnpm build:core && pnpm build:all\",\n    \"build:core\": \"tsup --config tsup.core.ts\",\n    \"build:all\": \"tsup\",\n    \"dev\": \"pnpm run -w --parallel /^dev:.*/\",\n    \"dev:build\": \"tsup --watch\",\n    \"dev:playground\": \"pnpm --filter playground dev\",\n    \"dev:nuxt\": \"pnpm --filter playground-nuxt dev\",\n    \"readme\": \"node ./scripts/update-readme.js\",\n    \"version:dev\": \"npm version prerelease --preid dev --force --no-git-tag-version\",\n    \"version:patch\": \"npm version patch --force --no-git-tag-version\",\n    \"version:minor\": \"npm version minor --force --no-git-tag-version\",\n    \"version:major\": \"npm version major --force --no-git-tag-version\",\n    \"postversion\": \"pnpm build && pnpm readme\",\n    \"publish:main\": \"npm publish\",\n    \"publish:dev\": \"npm publish --tag dev\"\n  },\n  \"files\": [\n    \"dist\"\n  ],\n  \"devDependencies\": {\n    \"terser\": \"^5.37.0\",\n    \"tsup\": \"^8.3.5\",\n    \"typescript\": \"^5.7.3\"\n  },\n  \"peerDependencies\": {\n    \"react\": \">=17.0.0\",\n    \"vue\": \">=3.0.0\",\n    \"@nuxt/kit\": \">=3.0.0\"\n  },\n  \"peerDependenciesMeta\": {\n    \"react\": {\n      \"optional\": true\n    },\n    \"vue\": {\n      \"optional\": true\n    },\n    \"@nuxt/kit\": {\n      \"optional\": true\n    }\n  },\n  \"unpkg\": \"./dist/lenis.mjs\",\n  \"main\": \"./dist/lenis.mjs\",\n  \"module\": \"./dist/lenis.mjs\",\n  \"types\": \"./dist/lenis.d.ts\",\n  \"exports\": {\n    \".\": {\n      \"types\": \"./dist/lenis.d.ts\",\n      \"default\": \"./dist/lenis.mjs\"\n    },\n    \"./react\": {\n      \"types\": \"./dist/lenis-react.d.ts\",\n      \"default\": \"./dist/lenis-react.mjs\"\n    },\n    \"./snap\": {\n      \"types\": \"./dist/lenis-snap.d.ts\",\n      \"default\": \"./dist/lenis-snap.mjs\"\n    },\n    \"./vue\": {\n      \"types\": \"./dist/lenis-vue.d.ts\",\n      \"default\": \"./dist/lenis-vue.mjs\"\n    },\n    \"./nuxt\": {\n      \"default\": \"./dist/lenis-vue-nuxt.mjs\"\n    },\n    \"./nuxt/runtime/*\": {\n      \"default\": \"./dist/nuxt/runtime/*.mjs\"\n    },\n    \"./dist/*\": \"./dist/*\"\n  }\n}\n", "/**\r\n * Clamp a value between a minimum and maximum value\r\n *\r\n * @param min Minimum value\r\n * @param input Value to clamp\r\n * @param max Maximum value\r\n * @returns Clamped value\r\n */\r\nexport function clamp(min: number, input: number, max: number) {\r\n  return Math.max(min, Math.min(input, max))\r\n}\r\n\r\n/**\r\n * Truncate a floating-point number to a specified number of decimal places\r\n *\r\n * @param value Value to truncate\r\n * @param decimals Number of decimal places to truncate to\r\n * @returns Truncated value\r\n */\r\nexport function truncate(value: number, decimals = 0) {\r\n  return parseFloat(value.toFixed(decimals))\r\n}\r\n\r\n/**\r\n *  Linearly interpolate between two values using an amount (0 <= t <= 1)\r\n *\r\n * @param x First value\r\n * @param y Second value\r\n * @param t Amount to interpolate (0 <= t <= 1)\r\n * @returns Interpolated value\r\n */\r\nexport function lerp(x: number, y: number, t: number) {\r\n  return (1 - t) * x + t * y\r\n}\r\n\r\n/**\r\n * Damp a value over time using a damping factor\r\n * {@link http://www.rorydriscoll.com/2016/03/07/frame-rate-independent-damping-using-lerp/}\r\n *\r\n * @param x Initial value\r\n * @param y Target value\r\n * @param lambda Damping factor\r\n * @param dt Time elapsed since the last update\r\n * @returns Damped value\r\n */\r\nexport function damp(x: number, y: number, lambda: number, deltaTime: number) {\r\n  return lerp(x, y, 1 - Math.exp(-lambda * deltaTime))\r\n}\r\n\r\n/**\r\n * Calculate the modulo of the dividend and divisor while keeping the result within the same sign as the divisor\r\n * {@link https://anguscroll.com/just/just-modulo}\r\n *\r\n * @param n Dividend\r\n * @param d Divisor\r\n * @returns Modulo\r\n */\r\nexport function modulo(n: number, d: number) {\r\n  return ((n % d) + d) % d\r\n}\r\n", "import { clamp, damp } from './maths'\r\nimport type { EasingFunction, FromToOptions, OnUpdateCallback } from './types'\r\n\r\n/**\r\n * Animate class to handle value animations with lerping or easing\r\n *\r\n * @example\r\n * const animate = new Animate()\r\n * animate.fromTo(0, 100, { duration: 1, easing: (t) => t })\r\n * animate.advance(0.5) // 50\r\n */\r\nexport class Animate {\r\n  isRunning = false\r\n  value = 0\r\n  from = 0\r\n  to = 0\r\n  currentTime = 0\r\n\r\n  // These are instanciated in the fromTo method\r\n  lerp?: number\r\n  duration?: number\r\n  easing?: EasingFunction\r\n  onUpdate?: OnUpdateCallback\r\n\r\n  /**\r\n   * Advance the animation by the given delta time\r\n   *\r\n   * @param deltaTime - The time in seconds to advance the animation\r\n   */\r\n  advance(deltaTime: number) {\r\n    if (!this.isRunning) return\r\n\r\n    let completed = false\r\n\r\n    if (this.duration && this.easing) {\r\n      this.currentTime += deltaTime\r\n      const linearProgress = clamp(0, this.currentTime / this.duration, 1)\r\n\r\n      completed = linearProgress >= 1\r\n      const easedProgress = completed ? 1 : this.easing(linearProgress)\r\n      this.value = this.from + (this.to - this.from) * easedProgress\r\n    } else if (this.lerp) {\r\n      this.value = damp(this.value, this.to, this.lerp * 60, deltaTime)\r\n      if (Math.round(this.value) === this.to) {\r\n        this.value = this.to\r\n        completed = true\r\n      }\r\n    } else {\r\n      // If no easing or lerp, just jump to the end value\r\n      this.value = this.to\r\n      completed = true\r\n    }\r\n\r\n    if (completed) {\r\n      this.stop()\r\n    }\r\n\r\n    // Call the onUpdate callback with the current value and completed status\r\n    this.onUpdate?.(this.value, completed)\r\n  }\r\n\r\n  /** Stop the animation */\r\n  stop() {\r\n    this.isRunning = false\r\n  }\r\n\r\n  /**\r\n   * Set up the animation from a starting value to an ending value\r\n   * with optional parameters for lerping, duration, easing, and onUpdate callback\r\n   *\r\n   * @param from - The starting value\r\n   * @param to - The ending value\r\n   * @param options - Options for the animation\r\n   */\r\n  fromTo(\r\n    from: number,\r\n    to: number,\r\n    { lerp, duration, easing, onStart, onUpdate }: FromToOptions\r\n  ) {\r\n    this.from = this.value = from\r\n    this.to = to\r\n    this.lerp = lerp\r\n    this.duration = duration\r\n    this.easing = easing\r\n    this.currentTime = 0\r\n    this.isRunning = true\r\n\r\n    onStart?.()\r\n    this.onUpdate = onUpdate\r\n  }\r\n}\r\n", "export function debounce<CB extends (...args: any[]) => void>(\r\n  callback: CB,\r\n  delay: number\r\n) {\r\n  let timer: number | undefined\r\n  return function <T>(this: T, ...args: Parameters<typeof callback>) {\r\n    let context = this\r\n    clearTimeout(timer)\r\n    timer = setTimeout(() => {\r\n      timer = undefined\r\n      callback.apply(context, args)\r\n    }, delay)\r\n  }\r\n}\r\n", "import { debounce } from './debounce'\r\n\r\n/**\r\n * Dimensions class to handle the size of the content and wrapper\r\n *\r\n * @example\r\n * const dimensions = new Dimensions(wrapper, content)\r\n * dimensions.on('resize', (e) => {\r\n *   console.log(e.width, e.height)\r\n * })\r\n */\r\nexport class Dimensions {\r\n  width = 0\r\n  height = 0\r\n  scrollHeight = 0\r\n  scrollWidth = 0\r\n\r\n  // These are instanciated in the constructor as they need information from the options\r\n  private debouncedResize?: (...args: unknown[]) => void\r\n  private wrapperResizeObserver?: ResizeObserver\r\n  private contentResizeObserver?: ResizeObserver\r\n\r\n  constructor(\r\n    private wrapper: HTMLElement | Window | Element,\r\n    private content: HTMLElement | Element,\r\n    { autoResize = true, debounce: debounceValue = 250 } = {}\r\n  ) {\r\n    if (autoResize) {\r\n      this.debouncedResize = debounce(this.resize, debounceValue)\r\n\r\n      if (this.wrapper instanceof Window) {\r\n        window.addEventListener('resize', this.debouncedResize, false)\r\n      } else {\r\n        this.wrapperResizeObserver = new ResizeObserver(this.debouncedResize)\r\n        this.wrapperResizeObserver.observe(this.wrapper)\r\n      }\r\n\r\n      this.contentResizeObserver = new ResizeObserver(this.debouncedResize)\r\n      this.contentResizeObserver.observe(this.content)\r\n    }\r\n\r\n    this.resize()\r\n  }\r\n\r\n  destroy() {\r\n    this.wrapperResizeObserver?.disconnect()\r\n    this.contentResizeObserver?.disconnect()\r\n\r\n    if (this.wrapper === window && this.debouncedResize) {\r\n      window.removeEventListener('resize', this.debouncedResize, false)\r\n    }\r\n  }\r\n\r\n  resize = () => {\r\n    this.onWrapperResize()\r\n    this.onContentResize()\r\n  }\r\n\r\n  onWrapperResize = () => {\r\n    if (this.wrapper instanceof Window) {\r\n      this.width = window.innerWidth\r\n      this.height = window.innerHeight\r\n    } else {\r\n      this.width = this.wrapper.clientWidth\r\n      this.height = this.wrapper.clientHeight\r\n    }\r\n  }\r\n\r\n  onContentResize = () => {\r\n    if (this.wrapper instanceof Window) {\r\n      this.scrollHeight = this.content.scrollHeight\r\n      this.scrollWidth = this.content.scrollWidth\r\n    } else {\r\n      this.scrollHeight = this.wrapper.scrollHeight\r\n      this.scrollWidth = this.wrapper.scrollWidth\r\n    }\r\n  }\r\n\r\n  get limit() {\r\n    return {\r\n      x: this.scrollWidth - this.width,\r\n      y: this.scrollHeight - this.height,\r\n    }\r\n  }\r\n}\r\n", "/**\r\n * Emitter class to handle events\r\n * @example\r\n * const emitter = new Emitter()\r\n * emitter.on('event', (data) => {\r\n *   console.log(data)\r\n * })\r\n * emitter.emit('event', 'data')\r\n */\r\nexport class Emitter {\r\n  private events: Record<\r\n    string,\r\n    Array<(...args: unknown[]) => void> | undefined\r\n  > = {}\r\n\r\n  /**\r\n   * Emit an event with the given data\r\n   * @param event Event name\r\n   * @param args Data to pass to the event handlers\r\n   */\r\n  emit(event: string, ...args: unknown[]) {\r\n    let callbacks = this.events[event] || []\r\n    for (let i = 0, length = callbacks.length; i < length; i++) {\r\n      callbacks[i]?.(...args)\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Add a callback to the event\r\n   * @param event Event name\r\n   * @param cb Callback function\r\n   * @returns Unsubscribe function\r\n   */\r\n  on<CB extends (...args: any[]) => void>(event: string, cb: CB) {\r\n    // Add the callback to the event's callback list, or create a new list with the callback\r\n    this.events[event]?.push(cb) || (this.events[event] = [cb])\r\n\r\n    // Return an unsubscribe function\r\n    return () => {\r\n      this.events[event] = this.events[event]?.filter((i) => cb !== i)\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Remove a callback from the event\r\n   * @param event Event name\r\n   * @param callback Callback function\r\n   */\r\n  off<CB extends (...args: any[]) => void>(event: string, callback: CB) {\r\n    this.events[event] = this.events[event]?.filter((i) => callback !== i)\r\n  }\r\n\r\n  /**\r\n   * Remove all event listeners and clean up\r\n   */\r\n  destroy() {\r\n    this.events = {}\r\n  }\r\n}\r\n", "import { Emitter } from './emitter'\r\nimport type { VirtualScrollCallback } from './types'\r\n\r\nconst LINE_HEIGHT = 100 / 6\r\nconst listenerOptions: AddEventListenerOptions = { passive: false }\r\n\r\nexport class VirtualScroll {\r\n  touchStart = {\r\n    x: 0,\r\n    y: 0,\r\n  }\r\n  lastDelta = {\r\n    x: 0,\r\n    y: 0,\r\n  }\r\n  window = {\r\n    width: 0,\r\n    height: 0,\r\n  }\r\n  private emitter = new Emitter()\r\n\r\n  constructor(\r\n    private element: HTMLElement,\r\n    private options = { wheelMultiplier: 1, touchMultiplier: 1 }\r\n  ) {\r\n    window.addEventListener('resize', this.onWindowResize, false)\r\n    this.onWindowResize()\r\n\r\n    this.element.addEventListener('wheel', this.onWheel, listenerOptions)\r\n    this.element.addEventListener(\r\n      'touchstart',\r\n      this.onTouchStart,\r\n      listenerOptions\r\n    )\r\n    this.element.addEventListener(\r\n      'touchmove',\r\n      this.onTouchMove,\r\n      listenerOptions\r\n    )\r\n    this.element.addEventListener('touchend', this.onTouchEnd, listenerOptions)\r\n  }\r\n\r\n  /**\r\n   * Add an event listener for the given event and callback\r\n   *\r\n   * @param event Event name\r\n   * @param callback Callback function\r\n   */\r\n  on(event: string, callback: VirtualScrollCallback) {\r\n    return this.emitter.on(event, callback)\r\n  }\r\n\r\n  /** Remove all event listeners and clean up */\r\n  destroy() {\r\n    this.emitter.destroy()\r\n\r\n    window.removeEventListener('resize', this.onWindowResize, false)\r\n\r\n    this.element.removeEventListener('wheel', this.onWheel, listenerOptions)\r\n    this.element.removeEventListener(\r\n      'touchstart',\r\n      this.onTouchStart,\r\n      listenerOptions\r\n    )\r\n    this.element.removeEventListener(\r\n      'touchmove',\r\n      this.onTouchMove,\r\n      listenerOptions\r\n    )\r\n    this.element.removeEventListener(\r\n      'touchend',\r\n      this.onTouchEnd,\r\n      listenerOptions\r\n    )\r\n  }\r\n\r\n  /**\r\n   * Event handler for 'touchstart' event\r\n   *\r\n   * @param event Touch event\r\n   */\r\n  onTouchStart = (event: TouchEvent) => {\r\n    // @ts-expect-error - event.targetTouches is not defined\r\n    const { clientX, clientY } = event.targetTouches\r\n      ? event.targetTouches[0]\r\n      : event\r\n\r\n    this.touchStart.x = clientX\r\n    this.touchStart.y = clientY\r\n\r\n    this.lastDelta = {\r\n      x: 0,\r\n      y: 0,\r\n    }\r\n\r\n    this.emitter.emit('scroll', {\r\n      deltaX: 0,\r\n      deltaY: 0,\r\n      event,\r\n    })\r\n  }\r\n\r\n  /** Event handler for 'touchmove' event */\r\n  onTouchMove = (event: TouchEvent) => {\r\n    // @ts-expect-error - event.targetTouches is not defined\r\n    const { clientX, clientY } = event.targetTouches\r\n      ? event.targetTouches[0]\r\n      : event\r\n\r\n    const deltaX = -(clientX - this.touchStart.x) * this.options.touchMultiplier\r\n    const deltaY = -(clientY - this.touchStart.y) * this.options.touchMultiplier\r\n\r\n    this.touchStart.x = clientX\r\n    this.touchStart.y = clientY\r\n\r\n    this.lastDelta = {\r\n      x: deltaX,\r\n      y: deltaY,\r\n    }\r\n\r\n    this.emitter.emit('scroll', {\r\n      deltaX,\r\n      deltaY,\r\n      event,\r\n    })\r\n  }\r\n\r\n  onTouchEnd = (event: TouchEvent) => {\r\n    this.emitter.emit('scroll', {\r\n      deltaX: this.lastDelta.x,\r\n      deltaY: this.lastDelta.y,\r\n      event,\r\n    })\r\n  }\r\n\r\n  /** Event handler for 'wheel' event */\r\n  onWheel = (event: WheelEvent) => {\r\n    let { deltaX, deltaY, deltaMode } = event\r\n\r\n    const multiplierX =\r\n      deltaMode === 1 ? LINE_HEIGHT : deltaMode === 2 ? this.window.width : 1\r\n    const multiplierY =\r\n      deltaMode === 1 ? LINE_HEIGHT : deltaMode === 2 ? this.window.height : 1\r\n\r\n    deltaX *= multiplierX\r\n    deltaY *= multiplierY\r\n\r\n    deltaX *= this.options.wheelMultiplier\r\n    deltaY *= this.options.wheelMultiplier\r\n\r\n    this.emitter.emit('scroll', { deltaX, deltaY, event })\r\n  }\r\n\r\n  onWindowResize = () => {\r\n    this.window = {\r\n      width: window.innerWidth,\r\n      height: window.innerHeight,\r\n    }\r\n  }\r\n}\r\n", "import { version } from '../../../package.json'\nimport { Animate } from './animate'\nimport { Dimensions } from './dimensions'\nimport { Emitter } from './emitter'\nimport { clamp, modulo } from './maths'\nimport type {\n  LenisEvent,\n  LenisOptions,\n  ScrollCallback,\n  Scrolling,\n  ScrollToOptions,\n  UserData,\n  VirtualScrollCallback,\n  VirtualScrollData,\n} from './types'\nimport { VirtualScroll } from './virtual-scroll'\n\n// Technical explanation\n// - listen to 'wheel' events\n// - prevent 'wheel' event to prevent scroll\n// - normalize wheel delta\n// - add delta to targetScroll\n// - animate scroll to targetScroll (smooth context)\n// - if animation is not running, listen to 'scroll' events (native context)\n\ntype OptionalPick<T, F extends keyof T> = Omit<T, F> & Partial<Pick<T, F>>\n\nconst defaultEasing = (t: number) => Math.min(1, 1.001 - Math.pow(2, -10 * t))\n\nexport class Lenis {\n  private _isScrolling: Scrolling = false // true when scroll is animating\n  private _isStopped = false // true if user should not be able to scroll - enable/disable programmatically\n  private _isLocked = false // same as isStopped but enabled/disabled when scroll reaches target\n  private _preventNextNativeScrollEvent = false\n  private _resetVelocityTimeout: ReturnType<typeof setTimeout> | null = null\n  private __rafID: number | null = null\n\n  /**\n   * Whether or not the user is touching the screen\n   */\n  isTouching?: boolean\n  /**\n   * The time in ms since the lenis instance was created\n   */\n  time = 0\n  /**\n   * User data that will be forwarded through the scroll event\n   *\n   * @example\n   * lenis.scrollTo(100, {\n   *   userData: {\n   *     foo: 'bar'\n   *   }\n   * })\n   */\n  userData: UserData = {}\n  /**\n   * The last velocity of the scroll\n   */\n  lastVelocity = 0\n  /**\n   * The current velocity of the scroll\n   */\n  velocity = 0\n  /**\n   * The direction of the scroll\n   */\n  direction: 1 | -1 | 0 = 0\n  /**\n   * The options passed to the lenis instance\n   */\n  options: OptionalPick<\n    Required<LenisOptions>,\n    'duration' | 'easing' | 'prevent' | 'virtualScroll'\n  >\n  /**\n   * The target scroll value\n   */\n  targetScroll: number\n  /**\n   * The animated scroll value\n   */\n  animatedScroll: number\n\n  // These are instanciated here as they don't need information from the options\n  private readonly animate = new Animate()\n  private readonly emitter = new Emitter()\n  // These are instanciated in the constructor as they need information from the options\n  readonly dimensions: Dimensions // This is not private because it's used in the Snap class\n  private readonly virtualScroll: VirtualScroll\n\n  constructor({\n    wrapper = window,\n    content = document.documentElement,\n    eventsTarget = wrapper,\n    smoothWheel = true,\n    syncTouch = false,\n    syncTouchLerp = 0.075,\n    touchInertiaExponent = 1.7,\n    duration, // in seconds\n    easing,\n    lerp = 0.1,\n    infinite = false,\n    orientation = 'vertical', // vertical, horizontal\n    gestureOrientation = 'vertical', // vertical, horizontal, both\n    touchMultiplier = 1,\n    wheelMultiplier = 1,\n    autoResize = true,\n    prevent,\n    virtualScroll,\n    overscroll = true,\n    autoRaf = false,\n    anchors = false,\n    autoToggle = false, // https://caniuse.com/?search=transition-behavior\n    allowNestedScroll = false,\n    __experimental__naiveDimensions = false,\n  }: LenisOptions = {}) {\n    // Set version\n    window.lenisVersion = version\n\n    // Check if wrapper is <html>, fallback to window\n    if (!wrapper || wrapper === document.documentElement) {\n      wrapper = window\n    }\n\n    // flip to easing/time based animation if at least one of them is provided\n    if (typeof duration === 'number' && typeof easing !== 'function') {\n      easing = defaultEasing\n    } else if (typeof easing === 'function' && typeof duration !== 'number') {\n      duration = 1\n    }\n\n    // Setup options\n    this.options = {\n      wrapper,\n      content,\n      eventsTarget,\n      smoothWheel,\n      syncTouch,\n      syncTouchLerp,\n      touchInertiaExponent,\n      duration,\n      easing,\n      lerp,\n      infinite,\n      gestureOrientation,\n      orientation,\n      touchMultiplier,\n      wheelMultiplier,\n      autoResize,\n      prevent,\n      virtualScroll,\n      overscroll,\n      autoRaf,\n      anchors,\n      autoToggle,\n      allowNestedScroll,\n      __experimental__naiveDimensions,\n    }\n\n    // Setup dimensions instance\n    this.dimensions = new Dimensions(wrapper, content, { autoResize })\n\n    // Setup class name\n    this.updateClassName()\n\n    // Set the initial scroll value for all scroll information\n    this.targetScroll = this.animatedScroll = this.actualScroll\n\n    // Add event listeners\n    this.options.wrapper.addEventListener('scroll', this.onNativeScroll, false)\n\n    this.options.wrapper.addEventListener('scrollend', this.onScrollEnd, {\n      capture: true,\n    })\n\n    if (this.options.anchors && this.options.wrapper === window) {\n      this.options.wrapper.addEventListener(\n        'click',\n        this.onClick as EventListener,\n        false\n      )\n    }\n\n    this.options.wrapper.addEventListener(\n      'pointerdown',\n      this.onPointerDown as EventListener,\n      false\n    )\n\n    // Setup virtual scroll instance\n    this.virtualScroll = new VirtualScroll(eventsTarget as HTMLElement, {\n      touchMultiplier,\n      wheelMultiplier,\n    })\n    this.virtualScroll.on('scroll', this.onVirtualScroll)\n\n    if (this.options.autoToggle) {\n      this.rootElement.addEventListener('transitionend', this.onTransitionEnd, {\n        passive: true,\n      })\n    }\n\n    if (this.options.autoRaf) {\n      this.__rafID = requestAnimationFrame(this.raf)\n    }\n  }\n\n  /**\n   * Destroy the lenis instance, remove all event listeners and clean up the class name\n   */\n  destroy() {\n    this.emitter.destroy()\n\n    this.options.wrapper.removeEventListener(\n      'scroll',\n      this.onNativeScroll,\n      false\n    )\n\n    this.options.wrapper.removeEventListener('scrollend', this.onScrollEnd, {\n      capture: true,\n    })\n\n    this.options.wrapper.removeEventListener(\n      'pointerdown',\n      this.onPointerDown as EventListener,\n      false\n    )\n\n    if (this.options.anchors && this.options.wrapper === window) {\n      this.options.wrapper.removeEventListener(\n        'click',\n        this.onClick as EventListener,\n        false\n      )\n    }\n\n    this.virtualScroll.destroy()\n    this.dimensions.destroy()\n\n    this.cleanUpClassName()\n\n    if (this.__rafID) {\n      cancelAnimationFrame(this.__rafID)\n    }\n  }\n\n  /**\n   * Add an event listener for the given event and callback\n   *\n   * @param event Event name\n   * @param callback Callback function\n   * @returns Unsubscribe function\n   */\n  on(event: 'scroll', callback: ScrollCallback): () => void\n  on(event: 'virtual-scroll', callback: VirtualScrollCallback): () => void\n  on(event: LenisEvent, callback: any) {\n    return this.emitter.on(event, callback)\n  }\n\n  /**\n   * Remove an event listener for the given event and callback\n   *\n   * @param event Event name\n   * @param callback Callback function\n   */\n  off(event: 'scroll', callback: ScrollCallback): void\n  off(event: 'virtual-scroll', callback: VirtualScrollCallback): void\n  off(event: LenisEvent, callback: any) {\n    return this.emitter.off(event, callback)\n  }\n\n  private onScrollEnd = (e: Event | CustomEvent) => {\n    if (!(e instanceof CustomEvent)) {\n      if (this.isScrolling === 'smooth' || this.isScrolling === false) {\n        e.stopPropagation()\n      }\n    }\n  }\n\n  private dispatchScrollendEvent = () => {\n    this.options.wrapper.dispatchEvent(\n      new CustomEvent('scrollend', {\n        bubbles: this.options.wrapper === window,\n        // cancelable: false,\n        detail: {\n          lenisScrollEnd: true,\n        },\n      })\n    )\n  }\n\n  private onTransitionEnd = (event: TransitionEvent) => {\n    if (event.propertyName.includes('overflow')) {\n      const property = this.isHorizontal ? 'overflow-x' : 'overflow-y'\n\n      const overflow = getComputedStyle(this.rootElement)[\n        property as keyof CSSStyleDeclaration\n      ] as string\n\n      if (['hidden', 'clip'].includes(overflow)) {\n        this.internalStop()\n      } else {\n        this.internalStart()\n      }\n    }\n  }\n\n  private setScroll(scroll: number) {\n    // behavior: 'instant' bypasses the scroll-behavior CSS property\n\n    if (this.isHorizontal) {\n      this.options.wrapper.scrollTo({ left: scroll, behavior: 'instant' })\n    } else {\n      this.options.wrapper.scrollTo({ top: scroll, behavior: 'instant' })\n    }\n  }\n\n  private onClick = (event: PointerEvent | MouseEvent) => {\n    const path = event.composedPath()\n    const anchor = path.find(\n      (node) =>\n        node instanceof HTMLAnchorElement &&\n        (node.getAttribute('href')?.startsWith('#') ||\n          node.getAttribute('href')?.startsWith('/#') ||\n          node.getAttribute('href')?.startsWith('./#'))\n    ) as HTMLAnchorElement | undefined\n    if (anchor) {\n      const id = anchor.getAttribute('href')\n\n      if (id) {\n        const options =\n          typeof this.options.anchors === 'object' && this.options.anchors\n            ? this.options.anchors\n            : undefined\n\n        let target: number | string = `#${id.split('#')[1]}`\n        if (['#', '/#', './#', '#top', '/#top', './#top'].includes(id)) {\n          target = 0\n        }\n\n        this.scrollTo(target, options)\n      }\n    }\n  }\n\n  private onPointerDown = (event: PointerEvent | MouseEvent) => {\n    if (event.button === 1) {\n      this.reset()\n    }\n  }\n\n  private onVirtualScroll = (data: VirtualScrollData) => {\n    if (\n      typeof this.options.virtualScroll === 'function' &&\n      this.options.virtualScroll(data) === false\n    )\n      return\n\n    const { deltaX, deltaY, event } = data\n\n    this.emitter.emit('virtual-scroll', { deltaX, deltaY, event })\n\n    // keep zoom feature\n    if (event.ctrlKey) return\n    // @ts-ignore\n    if (event.lenisStopPropagation) return\n\n    const isTouch = event.type.includes('touch')\n    const isWheel = event.type.includes('wheel')\n\n    this.isTouching = event.type === 'touchstart' || event.type === 'touchmove'\n    // if (event.type === 'touchend') {\n    //   console.log('touchend', this.scroll)\n    //   // this.lastVelocity = this.velocity\n    //   // this.velocity = 0\n    //   // this.isScrolling = false\n    //   this.emit({ type: 'touchend' })\n    //   // alert('touchend')\n    //   return\n    // }\n\n    const isClickOrTap = deltaX === 0 && deltaY === 0\n\n    const isTapToStop =\n      this.options.syncTouch &&\n      isTouch &&\n      event.type === 'touchstart' &&\n      isClickOrTap &&\n      !this.isStopped &&\n      !this.isLocked\n\n    if (isTapToStop) {\n      this.reset()\n      return\n    }\n\n    // const isPullToRefresh =\n    //   this.options.gestureOrientation === 'vertical' &&\n    //   this.scroll === 0 &&\n    //   !this.options.infinite &&\n    //   deltaY <= 5 // touch pull to refresh, not reliable yet\n\n    const isUnknownGesture =\n      (this.options.gestureOrientation === 'vertical' && deltaY === 0) ||\n      (this.options.gestureOrientation === 'horizontal' && deltaX === 0)\n\n    if (isClickOrTap || isUnknownGesture) {\n      // console.log('prevent')\n      return\n    }\n\n    // catch if scrolling on nested scroll elements\n    let composedPath = event.composedPath()\n    composedPath = composedPath.slice(0, composedPath.indexOf(this.rootElement)) // remove parents elements\n\n    const prevent = this.options.prevent\n\n    if (\n      !!composedPath.find(\n        (node) =>\n          node instanceof HTMLElement &&\n          ((typeof prevent === 'function' && prevent?.(node)) ||\n            node.hasAttribute?.('data-lenis-prevent') ||\n            (isTouch && node.hasAttribute?.('data-lenis-prevent-touch')) ||\n            (isWheel && node.hasAttribute?.('data-lenis-prevent-wheel')) ||\n            (this.options.allowNestedScroll &&\n              this.checkNestedScroll(node, { deltaX, deltaY })))\n      )\n    )\n      return\n\n    if (this.isStopped || this.isLocked) {\n      if (event.cancelable) {\n        event.preventDefault() // this will stop forwarding the event to the parent, this is problematic\n      }\n      return\n    }\n\n    const isSmooth =\n      (this.options.syncTouch && isTouch) ||\n      (this.options.smoothWheel && isWheel)\n\n    if (!isSmooth) {\n      this.isScrolling = 'native'\n      this.animate.stop()\n      // @ts-ignore\n      event.lenisStopPropagation = true\n      return\n    }\n\n    let delta = deltaY\n    if (this.options.gestureOrientation === 'both') {\n      delta = Math.abs(deltaY) > Math.abs(deltaX) ? deltaY : deltaX\n    } else if (this.options.gestureOrientation === 'horizontal') {\n      delta = deltaX\n    }\n\n    if (\n      !this.options.overscroll ||\n      this.options.infinite ||\n      (this.options.wrapper !== window &&\n        ((this.animatedScroll > 0 && this.animatedScroll < this.limit) ||\n          (this.animatedScroll === 0 && deltaY > 0) ||\n          (this.animatedScroll === this.limit && deltaY < 0)))\n    ) {\n      // @ts-ignore\n      event.lenisStopPropagation = true\n      // event.stopPropagation()\n    }\n\n    if (event.cancelable) {\n      event.preventDefault()\n    }\n\n    const isSyncTouch = isTouch && this.options.syncTouch\n    const isTouchEnd = isTouch && event.type === 'touchend'\n\n    const hasTouchInertia = isTouchEnd\n\n    if (hasTouchInertia) {\n      // delta = this.velocity * this.options.touchInertiaMultiplier\n      delta =\n        Math.sign(this.velocity) *\n        Math.pow(Math.abs(this.velocity), this.options.touchInertiaExponent)\n    }\n\n    this.scrollTo(this.targetScroll + delta, {\n      programmatic: false,\n      ...(isSyncTouch\n        ? {\n            lerp: hasTouchInertia ? this.options.syncTouchLerp : 1,\n            // immediate: !hasTouchInertia,\n          }\n        : {\n            lerp: this.options.lerp,\n            duration: this.options.duration,\n            easing: this.options.easing,\n          }),\n    })\n  }\n\n  /**\n   * Force lenis to recalculate the dimensions\n   */\n  resize() {\n    this.dimensions.resize()\n    this.animatedScroll = this.targetScroll = this.actualScroll\n    this.emit()\n  }\n\n  private emit() {\n    this.emitter.emit('scroll', this)\n  }\n\n  private onNativeScroll = () => {\n    if (this._resetVelocityTimeout !== null) {\n      clearTimeout(this._resetVelocityTimeout)\n      this._resetVelocityTimeout = null\n    }\n\n    if (this._preventNextNativeScrollEvent) {\n      this._preventNextNativeScrollEvent = false\n      return\n    }\n\n    if (this.isScrolling === false || this.isScrolling === 'native') {\n      const lastScroll = this.animatedScroll\n      this.animatedScroll = this.targetScroll = this.actualScroll\n      this.lastVelocity = this.velocity\n      this.velocity = this.animatedScroll - lastScroll\n      this.direction = Math.sign(\n        this.animatedScroll - lastScroll\n      ) as Lenis['direction']\n\n      if (!this.isStopped) {\n        this.isScrolling = 'native'\n      }\n\n      this.emit()\n\n      if (this.velocity !== 0) {\n        this._resetVelocityTimeout = setTimeout(() => {\n          this.lastVelocity = this.velocity\n          this.velocity = 0\n          this.isScrolling = false\n          this.emit()\n        }, 400)\n      }\n    }\n  }\n\n  private reset() {\n    this.isLocked = false\n    this.isScrolling = false\n    this.animatedScroll = this.targetScroll = this.actualScroll\n    this.lastVelocity = this.velocity = 0\n    this.animate.stop()\n  }\n\n  /**\n   * Start lenis scroll after it has been stopped\n   */\n  start() {\n    if (!this.isStopped) return\n\n    if (this.options.autoToggle) {\n      this.rootElement.style.removeProperty('overflow')\n      return\n    }\n\n    this.internalStart()\n  }\n\n  private internalStart() {\n    if (!this.isStopped) return\n\n    this.reset()\n    this.isStopped = false\n    this.emit()\n  }\n\n  /**\n   * Stop lenis scroll\n   */\n  stop() {\n    if (this.isStopped) return\n\n    if (this.options.autoToggle) {\n      this.rootElement.style.setProperty('overflow', 'clip')\n      return\n    }\n\n    this.internalStop()\n  }\n\n  private internalStop() {\n    if (this.isStopped) return\n\n    this.reset()\n    this.isStopped = true\n    this.emit()\n  }\n\n  /**\n   * RequestAnimationFrame for lenis\n   *\n   * @param time The time in ms from an external clock like `requestAnimationFrame` or Tempus\n   */\n  raf = (time: number) => {\n    const deltaTime = time - (this.time || time)\n    this.time = time\n\n    this.animate.advance(deltaTime * 0.001)\n\n    if (this.options.autoRaf) {\n      this.__rafID = requestAnimationFrame(this.raf)\n    }\n  }\n\n  /**\n   * Scroll to a target value\n   *\n   * @param target The target value to scroll to\n   * @param options The options for the scroll\n   *\n   * @example\n   * lenis.scrollTo(100, {\n   *   offset: 100,\n   *   duration: 1,\n   *   easing: (t) => 1 - Math.cos((t * Math.PI) / 2),\n   *   lerp: 0.1,\n   *   onStart: () => {\n   *     console.log('onStart')\n   *   },\n   *   onComplete: () => {\n   *     console.log('onComplete')\n   *   },\n   * })\n   */\n  scrollTo(\n    target: number | string | HTMLElement,\n    {\n      offset = 0,\n      immediate = false,\n      lock = false,\n      duration = this.options.duration,\n      easing = this.options.easing,\n      lerp = this.options.lerp,\n      onStart,\n      onComplete,\n      force = false, // scroll even if stopped\n      programmatic = true, // called from outside of the class\n      userData,\n    }: ScrollToOptions = {}\n  ) {\n    if ((this.isStopped || this.isLocked) && !force) return\n\n    // keywords\n    if (\n      typeof target === 'string' &&\n      ['top', 'left', 'start'].includes(target)\n    ) {\n      target = 0\n    } else if (\n      typeof target === 'string' &&\n      ['bottom', 'right', 'end'].includes(target)\n    ) {\n      target = this.limit\n    } else {\n      let node\n\n      if (typeof target === 'string') {\n        // CSS selector\n        node = document.querySelector(target)\n      } else if (target instanceof HTMLElement && target?.nodeType) {\n        // Node element\n        node = target\n      }\n\n      if (node) {\n        if (this.options.wrapper !== window) {\n          // nested scroll offset correction\n          const wrapperRect = this.rootElement.getBoundingClientRect()\n          offset -= this.isHorizontal ? wrapperRect.left : wrapperRect.top\n        }\n\n        const rect = node.getBoundingClientRect()\n\n        target =\n          (this.isHorizontal ? rect.left : rect.top) + this.animatedScroll\n      }\n    }\n\n    if (typeof target !== 'number') return\n\n    target += offset\n    target = Math.round(target)\n\n    if (this.options.infinite) {\n      if (programmatic) {\n        this.targetScroll = this.animatedScroll = this.scroll\n\n        const distance = target - this.animatedScroll\n\n        if (distance > this.limit / 2) {\n          target = target - this.limit\n        } else if (distance < -this.limit / 2) {\n          target = target + this.limit\n        }\n      }\n    } else {\n      target = clamp(0, target, this.limit)\n    }\n\n    if (target === this.targetScroll) {\n      onStart?.(this)\n      onComplete?.(this)\n      return\n    }\n\n    this.userData = userData ?? {}\n\n    if (immediate) {\n      this.animatedScroll = this.targetScroll = target\n      this.setScroll(this.scroll)\n      this.reset()\n      this.preventNextNativeScrollEvent()\n      this.emit()\n      onComplete?.(this)\n      this.userData = {}\n\n      requestAnimationFrame(() => {\n        this.dispatchScrollendEvent()\n      })\n      return\n    }\n\n    if (!programmatic) {\n      this.targetScroll = target\n    }\n\n    // flip to easing/time based animation if at least one of them is provided\n    if (typeof duration === 'number' && typeof easing !== 'function') {\n      easing = defaultEasing\n    } else if (typeof easing === 'function' && typeof duration !== 'number') {\n      duration = 1\n    }\n\n    this.animate.fromTo(this.animatedScroll, target, {\n      duration,\n      easing,\n      lerp,\n      onStart: () => {\n        // started\n        if (lock) this.isLocked = true\n        this.isScrolling = 'smooth'\n        onStart?.(this)\n      },\n      onUpdate: (value: number, completed: boolean) => {\n        this.isScrolling = 'smooth'\n\n        // updated\n        this.lastVelocity = this.velocity\n        this.velocity = value - this.animatedScroll\n        this.direction = Math.sign(this.velocity) as Lenis['direction']\n\n        this.animatedScroll = value\n        this.setScroll(this.scroll)\n\n        if (programmatic) {\n          // wheel during programmatic should stop it\n          this.targetScroll = value\n        }\n\n        if (!completed) this.emit()\n\n        if (completed) {\n          this.reset()\n          this.emit()\n          onComplete?.(this)\n          this.userData = {}\n\n          requestAnimationFrame(() => {\n            this.dispatchScrollendEvent()\n          })\n\n          // avoid emitting event twice\n          this.preventNextNativeScrollEvent()\n        }\n      },\n    })\n  }\n\n  private preventNextNativeScrollEvent() {\n    this._preventNextNativeScrollEvent = true\n\n    requestAnimationFrame(() => {\n      this._preventNextNativeScrollEvent = false\n    })\n  }\n\n  private checkNestedScroll(\n    node: HTMLElement,\n    { deltaX, deltaY }: { deltaX: number; deltaY: number }\n  ) {\n    const time = Date.now()\n\n    // @ts-ignore\n    const cache = (node._lenis ??= {})\n\n    let hasOverflowX,\n      hasOverflowY,\n      isScrollableX,\n      isScrollableY,\n      scrollWidth,\n      scrollHeight,\n      clientWidth,\n      clientHeight\n\n    const gestureOrientation = this.options.gestureOrientation\n\n    if (time - (cache.time ?? 0) > 2000) {\n      cache.time = Date.now()\n\n      const computedStyle = window.getComputedStyle(node)\n      cache.computedStyle = computedStyle\n\n      const overflowXString = computedStyle.overflowX\n      const overflowYString = computedStyle.overflowY\n\n      hasOverflowX = ['auto', 'overlay', 'scroll'].includes(overflowXString)\n      hasOverflowY = ['auto', 'overlay', 'scroll'].includes(overflowYString)\n      cache.hasOverflowX = hasOverflowX\n      cache.hasOverflowY = hasOverflowY\n\n      if (!hasOverflowX && !hasOverflowY) return false // if no overflow, it's not scrollable no matter what, early return saves some computations\n      if (gestureOrientation === 'vertical' && !hasOverflowY) return false\n      if (gestureOrientation === 'horizontal' && !hasOverflowX) return false\n\n      scrollWidth = node.scrollWidth\n      scrollHeight = node.scrollHeight\n\n      clientWidth = node.clientWidth\n      clientHeight = node.clientHeight\n\n      isScrollableX = scrollWidth > clientWidth\n      isScrollableY = scrollHeight > clientHeight\n\n      cache.isScrollableX = isScrollableX\n      cache.isScrollableY = isScrollableY\n      cache.scrollWidth = scrollWidth\n      cache.scrollHeight = scrollHeight\n      cache.clientWidth = clientWidth\n      cache.clientHeight = clientHeight\n    } else {\n      isScrollableX = cache.isScrollableX\n      isScrollableY = cache.isScrollableY\n      hasOverflowX = cache.hasOverflowX\n      hasOverflowY = cache.hasOverflowY\n      scrollWidth = cache.scrollWidth\n      scrollHeight = cache.scrollHeight\n      clientWidth = cache.clientWidth\n      clientHeight = cache.clientHeight\n    }\n\n    if (\n      (!hasOverflowX && !hasOverflowY) ||\n      (!isScrollableX && !isScrollableY)\n    ) {\n      return false\n    }\n\n    if (gestureOrientation === 'vertical' && (!hasOverflowY || !isScrollableY))\n      return false\n\n    if (\n      gestureOrientation === 'horizontal' &&\n      (!hasOverflowX || !isScrollableX)\n    )\n      return false\n\n    let orientation: 'x' | 'y' | undefined\n\n    if (gestureOrientation === 'horizontal') {\n      orientation = 'x'\n    } else if (gestureOrientation === 'vertical') {\n      orientation = 'y'\n    } else {\n      const isScrollingX = deltaX !== 0\n      const isScrollingY = deltaY !== 0\n\n      if (isScrollingX && hasOverflowX && isScrollableX) {\n        orientation = 'x'\n      }\n\n      if (isScrollingY && hasOverflowY && isScrollableY) {\n        orientation = 'y'\n      }\n    }\n\n    if (!orientation) return false\n\n    let scroll, maxScroll, delta, hasOverflow, isScrollable\n\n    if (orientation === 'x') {\n      scroll = node.scrollLeft\n      maxScroll = scrollWidth - clientWidth\n      delta = deltaX\n\n      hasOverflow = hasOverflowX\n      isScrollable = isScrollableX\n    } else if (orientation === 'y') {\n      scroll = node.scrollTop\n      maxScroll = scrollHeight - clientHeight\n      delta = deltaY\n\n      hasOverflow = hasOverflowY\n      isScrollable = isScrollableY\n    } else {\n      return false\n    }\n\n    const willScroll = delta > 0 ? scroll < maxScroll : scroll > 0\n\n    return willScroll && hasOverflow && isScrollable\n  }\n\n  /**\n   * The root element on which lenis is instanced\n   */\n  get rootElement() {\n    return (\n      this.options.wrapper === window\n        ? document.documentElement\n        : this.options.wrapper\n    ) as HTMLElement\n  }\n\n  /**\n   * The limit which is the maximum scroll value\n   */\n  get limit() {\n    if (this.options.__experimental__naiveDimensions) {\n      if (this.isHorizontal) {\n        return this.rootElement.scrollWidth - this.rootElement.clientWidth\n      } else {\n        return this.rootElement.scrollHeight - this.rootElement.clientHeight\n      }\n    } else {\n      return this.dimensions.limit[this.isHorizontal ? 'x' : 'y']\n    }\n  }\n\n  /**\n   * Whether or not the scroll is horizontal\n   */\n  get isHorizontal() {\n    return this.options.orientation === 'horizontal'\n  }\n\n  /**\n   * The actual scroll value\n   */\n  get actualScroll() {\n    // value browser takes into account\n    // it has to be this way because of DOCTYPE declaration\n    const wrapper = this.options.wrapper as Window | HTMLElement\n\n    return this.isHorizontal\n      ? (wrapper as Window).scrollX ?? (wrapper as HTMLElement).scrollLeft\n      : (wrapper as Window).scrollY ?? (wrapper as HTMLElement).scrollTop\n  }\n\n  /**\n   * The current scroll value\n   */\n  get scroll() {\n    return this.options.infinite\n      ? modulo(this.animatedScroll, this.limit)\n      : this.animatedScroll\n  }\n\n  /**\n   * The progress of the scroll relative to the limit\n   */\n  get progress() {\n    // avoid progress to be NaN\n    return this.limit === 0 ? 1 : this.scroll / this.limit\n  }\n\n  /**\n   * Current scroll state\n   */\n  get isScrolling() {\n    return this._isScrolling\n  }\n\n  private set isScrolling(value: Scrolling) {\n    if (this._isScrolling !== value) {\n      this._isScrolling = value\n      this.updateClassName()\n    }\n  }\n\n  /**\n   * Check if lenis is stopped\n   */\n  get isStopped() {\n    return this._isStopped\n  }\n\n  private set isStopped(value: boolean) {\n    if (this._isStopped !== value) {\n      this._isStopped = value\n      this.updateClassName()\n    }\n  }\n\n  /**\n   * Check if lenis is locked\n   */\n  get isLocked() {\n    return this._isLocked\n  }\n\n  private set isLocked(value: boolean) {\n    if (this._isLocked !== value) {\n      this._isLocked = value\n      this.updateClassName()\n    }\n  }\n\n  /**\n   * Check if lenis is smooth scrolling\n   */\n  get isSmooth() {\n    return this.isScrolling === 'smooth'\n  }\n\n  /**\n   * The class name applied to the wrapper element\n   */\n  get className() {\n    let className = 'lenis'\n    if (this.options.autoToggle) className += ' lenis-autoToggle'\n    if (this.isStopped) className += ' lenis-stopped'\n    if (this.isLocked) className += ' lenis-locked'\n    if (this.isScrolling) className += ' lenis-scrolling'\n    if (this.isScrolling === 'smooth') className += ' lenis-smooth'\n    return className\n  }\n\n  private updateClassName() {\n    this.cleanUpClassName()\n\n    this.rootElement.className =\n      `${this.rootElement.className} ${this.className}`.trim()\n  }\n\n  private cleanUpClassName() {\n    this.rootElement.className = this.rootElement.className\n      .replace(/lenis(-\\w+)?/g, '')\n      .trim()\n  }\n}\n"], "mappings": ";AAEE,IAAAA,OAAA,GAAW;;;ACMN,SAASC,MAAMC,GAAA,EAAaC,KAAA,EAAeC,GAAA,EAAa;EAC7D,OAAOC,IAAA,CAAKD,GAAA,CAAIF,GAAA,EAAKG,IAAA,CAAKH,GAAA,CAAIC,KAAA,EAAOC,GAAG,CAAC;AAC3C;AAqBO,SAASE,KAAKC,CAAA,EAAWC,CAAA,EAAWC,CAAA,EAAW;EACpD,QAAQ,IAAIA,CAAA,IAAKF,CAAA,GAAIE,CAAA,GAAID,CAAA;AAC3B;AAYO,SAASE,KAAKH,CAAA,EAAWC,CAAA,EAAWG,MAAA,EAAgBC,SAAA,EAAmB;EAC5E,OAAON,IAAA,CAAKC,CAAA,EAAGC,CAAA,EAAG,IAAIH,IAAA,CAAKQ,GAAA,CAAI,CAACF,MAAA,GAASC,SAAS,CAAC;AACrD;AAUO,SAASE,OAAOC,CAAA,EAAWC,CAAA,EAAW;EAC3C,QAASD,CAAA,GAAIC,CAAA,GAAKA,CAAA,IAAKA,CAAA;AACzB;;;AChDO,IAAMC,OAAA,GAAN,MAAc;EACnBC,SAAA,GAAY;EACZC,KAAA,GAAQ;EACRC,IAAA,GAAO;EACPC,EAAA,GAAK;EACLC,WAAA,GAAc;EAAA;EAGdhB,IAAA;EACAiB,QAAA;EACAC,MAAA;EACAC,QAAA;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAC,QAAQd,SAAA,EAAmB;IACzB,IAAI,CAAC,KAAKM,SAAA,EAAW;IAErB,IAAIS,SAAA,GAAY;IAEhB,IAAI,KAAKJ,QAAA,IAAY,KAAKC,MAAA,EAAQ;MAChC,KAAKF,WAAA,IAAeV,SAAA;MACpB,MAAMgB,cAAA,GAAiB3B,KAAA,CAAM,GAAG,KAAKqB,WAAA,GAAc,KAAKC,QAAA,EAAU,CAAC;MAEnEI,SAAA,GAAYC,cAAA,IAAkB;MAC9B,MAAMC,aAAA,GAAgBF,SAAA,GAAY,IAAI,KAAKH,MAAA,CAAOI,cAAc;MAChE,KAAKT,KAAA,GAAQ,KAAKC,IAAA,IAAQ,KAAKC,EAAA,GAAK,KAAKD,IAAA,IAAQS,aAAA;IACnD,WAAW,KAAKvB,IAAA,EAAM;MACpB,KAAKa,KAAA,GAAQT,IAAA,CAAK,KAAKS,KAAA,EAAO,KAAKE,EAAA,EAAI,KAAKf,IAAA,GAAO,IAAIM,SAAS;MAChE,IAAIP,IAAA,CAAKyB,KAAA,CAAM,KAAKX,KAAK,MAAM,KAAKE,EAAA,EAAI;QACtC,KAAKF,KAAA,GAAQ,KAAKE,EAAA;QAClBM,SAAA,GAAY;MACd;IACF,OAAO;MAEL,KAAKR,KAAA,GAAQ,KAAKE,EAAA;MAClBM,SAAA,GAAY;IACd;IAEA,IAAIA,SAAA,EAAW;MACb,KAAKI,IAAA,CAAK;IACZ;IAGA,KAAKN,QAAA,GAAW,KAAKN,KAAA,EAAOQ,SAAS;EACvC;EAAA;EAGAI,KAAA,EAAO;IACL,KAAKb,SAAA,GAAY;EACnB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUAc,OACEZ,IAAA,EACAC,EAAA,EACA;IAAEf,IAAA,EAAA2B,KAAA;IAAMV,QAAA;IAAUC,MAAA;IAAQU,OAAA;IAAST;EAAS,GAC5C;IACA,KAAKL,IAAA,GAAO,KAAKD,KAAA,GAAQC,IAAA;IACzB,KAAKC,EAAA,GAAKA,EAAA;IACV,KAAKf,IAAA,GAAO2B,KAAA;IACZ,KAAKV,QAAA,GAAWA,QAAA;IAChB,KAAKC,MAAA,GAASA,MAAA;IACd,KAAKF,WAAA,GAAc;IACnB,KAAKJ,SAAA,GAAY;IAEjBgB,OAAA,GAAU;IACV,KAAKT,QAAA,GAAWA,QAAA;EAClB;AACF;;;AC1FO,SAASU,SACdC,QAAA,EACAC,KAAA,EACA;EACA,IAAIC,KAAA;EACJ,OAAO,aAAyBC,IAAA,EAAmC;IACjE,IAAIC,OAAA,GAAU;IACdC,YAAA,CAAaH,KAAK;IAClBA,KAAA,GAAQI,UAAA,CAAW,MAAM;MACvBJ,KAAA,GAAQ;MACRF,QAAA,CAASO,KAAA,CAAMH,OAAA,EAASD,IAAI;IAC9B,GAAGF,KAAK;EACV;AACF;;;ACFO,IAAMO,UAAA,GAAN,MAAiB;EAWtBC,YACUC,OAAA,EACAC,OAAA,EACR;IAAEC,UAAA,GAAa;IAAMb,QAAA,EAAUc,aAAA,GAAgB;EAAI,IAAI,CAAC,GACxD;IAHQ,KAAAH,OAAA,GAAAA,OAAA;IACA,KAAAC,OAAA,GAAAA,OAAA;IAGR,IAAIC,UAAA,EAAY;MACd,KAAKE,eAAA,GAAkBf,QAAA,CAAS,KAAKgB,MAAA,EAAQF,aAAa;MAE1D,IAAI,KAAKH,OAAA,YAAmBM,MAAA,EAAQ;QAClCC,MAAA,CAAOC,gBAAA,CAAiB,UAAU,KAAKJ,eAAA,EAAiB,KAAK;MAC/D,OAAO;QACL,KAAKK,qBAAA,GAAwB,IAAIC,cAAA,CAAe,KAAKN,eAAe;QACpE,KAAKK,qBAAA,CAAsBE,OAAA,CAAQ,KAAKX,OAAO;MACjD;MAEA,KAAKY,qBAAA,GAAwB,IAAIF,cAAA,CAAe,KAAKN,eAAe;MACpE,KAAKQ,qBAAA,CAAsBD,OAAA,CAAQ,KAAKV,OAAO;IACjD;IAEA,KAAKI,MAAA,CAAO;EACd;EA9BAQ,KAAA,GAAQ;EACRC,MAAA,GAAS;EACTC,YAAA,GAAe;EACfC,WAAA,GAAc;EAAA;EAGNZ,eAAA;EACAK,qBAAA;EACAG,qBAAA;EAwBRK,QAAA,EAAU;IACR,KAAKR,qBAAA,EAAuBS,UAAA,CAAW;IACvC,KAAKN,qBAAA,EAAuBM,UAAA,CAAW;IAEvC,IAAI,KAAKlB,OAAA,KAAYO,MAAA,IAAU,KAAKH,eAAA,EAAiB;MACnDG,MAAA,CAAOY,mBAAA,CAAoB,UAAU,KAAKf,eAAA,EAAiB,KAAK;IAClE;EACF;EAEAC,MAAA,GAASA,CAAA,KAAM;IACb,KAAKe,eAAA,CAAgB;IACrB,KAAKC,eAAA,CAAgB;EACvB;EAEAD,eAAA,GAAkBA,CAAA,KAAM;IACtB,IAAI,KAAKpB,OAAA,YAAmBM,MAAA,EAAQ;MAClC,KAAKO,KAAA,GAAQN,MAAA,CAAOe,UAAA;MACpB,KAAKR,MAAA,GAASP,MAAA,CAAOgB,WAAA;IACvB,OAAO;MACL,KAAKV,KAAA,GAAQ,KAAKb,OAAA,CAAQwB,WAAA;MAC1B,KAAKV,MAAA,GAAS,KAAKd,OAAA,CAAQyB,YAAA;IAC7B;EACF;EAEAJ,eAAA,GAAkBA,CAAA,KAAM;IACtB,IAAI,KAAKrB,OAAA,YAAmBM,MAAA,EAAQ;MAClC,KAAKS,YAAA,GAAe,KAAKd,OAAA,CAAQc,YAAA;MACjC,KAAKC,WAAA,GAAc,KAAKf,OAAA,CAAQe,WAAA;IAClC,OAAO;MACL,KAAKD,YAAA,GAAe,KAAKf,OAAA,CAAQe,YAAA;MACjC,KAAKC,WAAA,GAAc,KAAKhB,OAAA,CAAQgB,WAAA;IAClC;EACF;EAEA,IAAIU,MAAA,EAAQ;IACV,OAAO;MACLjE,CAAA,EAAG,KAAKuD,WAAA,GAAc,KAAKH,KAAA;MAC3BnD,CAAA,EAAG,KAAKqD,YAAA,GAAe,KAAKD;IAC9B;EACF;AACF;;;AC3EO,IAAMa,OAAA,GAAN,MAAc;EACXC,MAAA,GAGJ,CAAC;EAAA;AAAA;AAAA;AAAA;AAAA;EAOLC,KAAKC,KAAA,KAAkBrC,IAAA,EAAiB;IACtC,IAAIsC,SAAA,GAAY,KAAKH,MAAA,CAAOE,KAAK,KAAK,EAAC;IACvC,SAASE,CAAA,GAAI,GAAGC,MAAA,GAASF,SAAA,CAAUE,MAAA,EAAQD,CAAA,GAAIC,MAAA,EAAQD,CAAA,IAAK;MAC1DD,SAAA,CAAUC,CAAC,IAAI,GAAGvC,IAAI;IACxB;EACF;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQAyC,GAAwCJ,KAAA,EAAeK,EAAA,EAAQ;IAE7D,KAAKP,MAAA,CAAOE,KAAK,GAAGM,IAAA,CAAKD,EAAE,MAAM,KAAKP,MAAA,CAAOE,KAAK,IAAI,CAACK,EAAE;IAGzD,OAAO,MAAM;MACX,KAAKP,MAAA,CAAOE,KAAK,IAAI,KAAKF,MAAA,CAAOE,KAAK,GAAGO,MAAA,CAAQL,CAAA,IAAMG,EAAA,KAAOH,CAAC;IACjE;EACF;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAM,IAAyCR,KAAA,EAAexC,QAAA,EAAc;IACpE,KAAKsC,MAAA,CAAOE,KAAK,IAAI,KAAKF,MAAA,CAAOE,KAAK,GAAGO,MAAA,CAAQL,CAAA,IAAM1C,QAAA,KAAa0C,CAAC;EACvE;EAAA;AAAA;AAAA;EAKAf,QAAA,EAAU;IACR,KAAKW,MAAA,GAAS,CAAC;EACjB;AACF;;;ACvDA,IAAMW,WAAA,GAAc,MAAM;AAC1B,IAAMC,eAAA,GAA2C;EAAEC,OAAA,EAAS;AAAM;AAE3D,IAAMC,aAAA,GAAN,MAAoB;EAezB3C,YACU4C,OAAA,EACAC,OAAA,GAAU;IAAEC,eAAA,EAAiB;IAAGC,eAAA,EAAiB;EAAE,GAC3D;IAFQ,KAAAH,OAAA,GAAAA,OAAA;IACA,KAAAC,OAAA,GAAAA,OAAA;IAERrC,MAAA,CAAOC,gBAAA,CAAiB,UAAU,KAAKuC,cAAA,EAAgB,KAAK;IAC5D,KAAKA,cAAA,CAAe;IAEpB,KAAKJ,OAAA,CAAQnC,gBAAA,CAAiB,SAAS,KAAKwC,OAAA,EAASR,eAAe;IACpE,KAAKG,OAAA,CAAQnC,gBAAA,CACX,cACA,KAAKyC,YAAA,EACLT,eACF;IACA,KAAKG,OAAA,CAAQnC,gBAAA,CACX,aACA,KAAK0C,WAAA,EACLV,eACF;IACA,KAAKG,OAAA,CAAQnC,gBAAA,CAAiB,YAAY,KAAK2C,UAAA,EAAYX,eAAe;EAC5E;EAjCAY,UAAA,GAAa;IACX3F,CAAA,EAAG;IACHC,CAAA,EAAG;EACL;EACA2F,SAAA,GAAY;IACV5F,CAAA,EAAG;IACHC,CAAA,EAAG;EACL;EACA6C,MAAA,GAAS;IACPM,KAAA,EAAO;IACPC,MAAA,EAAQ;EACV;EACQwC,OAAA,GAAU,IAAI3B,OAAA,CAAQ;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EA6B9BO,GAAGJ,KAAA,EAAexC,QAAA,EAAiC;IACjD,OAAO,KAAKgE,OAAA,CAAQpB,EAAA,CAAGJ,KAAA,EAAOxC,QAAQ;EACxC;EAAA;EAGA2B,QAAA,EAAU;IACR,KAAKqC,OAAA,CAAQrC,OAAA,CAAQ;IAErBV,MAAA,CAAOY,mBAAA,CAAoB,UAAU,KAAK4B,cAAA,EAAgB,KAAK;IAE/D,KAAKJ,OAAA,CAAQxB,mBAAA,CAAoB,SAAS,KAAK6B,OAAA,EAASR,eAAe;IACvE,KAAKG,OAAA,CAAQxB,mBAAA,CACX,cACA,KAAK8B,YAAA,EACLT,eACF;IACA,KAAKG,OAAA,CAAQxB,mBAAA,CACX,aACA,KAAK+B,WAAA,EACLV,eACF;IACA,KAAKG,OAAA,CAAQxB,mBAAA,CACX,YACA,KAAKgC,UAAA,EACLX,eACF;EACF;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAS,YAAA,GAAgBnB,KAAA,IAAsB;IAEpC,MAAM;MAAEyB,OAAA;MAASC;IAAQ,IAAI1B,KAAA,CAAM2B,aAAA,GAC/B3B,KAAA,CAAM2B,aAAA,CAAc,CAAC,IACrB3B,KAAA;IAEJ,KAAKsB,UAAA,CAAW3F,CAAA,GAAI8F,OAAA;IACpB,KAAKH,UAAA,CAAW1F,CAAA,GAAI8F,OAAA;IAEpB,KAAKH,SAAA,GAAY;MACf5F,CAAA,EAAG;MACHC,CAAA,EAAG;IACL;IAEA,KAAK4F,OAAA,CAAQzB,IAAA,CAAK,UAAU;MAC1B6B,MAAA,EAAQ;MACRC,MAAA,EAAQ;MACR7B;IACF,CAAC;EACH;EAAA;EAGAoB,WAAA,GAAepB,KAAA,IAAsB;IAEnC,MAAM;MAAEyB,OAAA;MAASC;IAAQ,IAAI1B,KAAA,CAAM2B,aAAA,GAC/B3B,KAAA,CAAM2B,aAAA,CAAc,CAAC,IACrB3B,KAAA;IAEJ,MAAM4B,MAAA,GAAS,EAAEH,OAAA,GAAU,KAAKH,UAAA,CAAW3F,CAAA,IAAK,KAAKmF,OAAA,CAAQE,eAAA;IAC7D,MAAMa,MAAA,GAAS,EAAEH,OAAA,GAAU,KAAKJ,UAAA,CAAW1F,CAAA,IAAK,KAAKkF,OAAA,CAAQE,eAAA;IAE7D,KAAKM,UAAA,CAAW3F,CAAA,GAAI8F,OAAA;IACpB,KAAKH,UAAA,CAAW1F,CAAA,GAAI8F,OAAA;IAEpB,KAAKH,SAAA,GAAY;MACf5F,CAAA,EAAGiG,MAAA;MACHhG,CAAA,EAAGiG;IACL;IAEA,KAAKL,OAAA,CAAQzB,IAAA,CAAK,UAAU;MAC1B6B,MAAA;MACAC,MAAA;MACA7B;IACF,CAAC;EACH;EAEAqB,UAAA,GAAcrB,KAAA,IAAsB;IAClC,KAAKwB,OAAA,CAAQzB,IAAA,CAAK,UAAU;MAC1B6B,MAAA,EAAQ,KAAKL,SAAA,CAAU5F,CAAA;MACvBkG,MAAA,EAAQ,KAAKN,SAAA,CAAU3F,CAAA;MACvBoE;IACF,CAAC;EACH;EAAA;EAGAkB,OAAA,GAAWlB,KAAA,IAAsB;IAC/B,IAAI;MAAE4B,MAAA;MAAQC,MAAA;MAAQC;IAAU,IAAI9B,KAAA;IAEpC,MAAM+B,WAAA,GACJD,SAAA,KAAc,IAAIrB,WAAA,GAAcqB,SAAA,KAAc,IAAI,KAAKrD,MAAA,CAAOM,KAAA,GAAQ;IACxE,MAAMiD,WAAA,GACJF,SAAA,KAAc,IAAIrB,WAAA,GAAcqB,SAAA,KAAc,IAAI,KAAKrD,MAAA,CAAOO,MAAA,GAAS;IAEzE4C,MAAA,IAAUG,WAAA;IACVF,MAAA,IAAUG,WAAA;IAEVJ,MAAA,IAAU,KAAKd,OAAA,CAAQC,eAAA;IACvBc,MAAA,IAAU,KAAKf,OAAA,CAAQC,eAAA;IAEvB,KAAKS,OAAA,CAAQzB,IAAA,CAAK,UAAU;MAAE6B,MAAA;MAAQC,MAAA;MAAQ7B;IAAM,CAAC;EACvD;EAEAiB,cAAA,GAAiBA,CAAA,KAAM;IACrB,KAAKxC,MAAA,GAAS;MACZM,KAAA,EAAON,MAAA,CAAOe,UAAA;MACdR,MAAA,EAAQP,MAAA,CAAOgB;IACjB;EACF;AACF;;;ACpIA,IAAMwC,aAAA,GAAiBpG,CAAA,IAAcJ,IAAA,CAAKH,GAAA,CAAI,GAAG,QAAQG,IAAA,CAAKyG,GAAA,CAAI,GAAG,MAAMrG,CAAC,CAAC;AAEtE,IAAMsG,KAAA,GAAN,MAAY;EACTC,YAAA,GAA0B;EAAA;EAC1BC,UAAA,GAAa;EAAA;EACbC,SAAA,GAAY;EAAA;EACZC,6BAAA,GAAgC;EAChCC,qBAAA,GAA8D;EAC9DC,OAAA,GAAyB;EAAA;AAAA;AAAA;EAKjCC,UAAA;EAAA;AAAA;AAAA;EAIAC,IAAA,GAAO;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWPC,QAAA,GAAqB,CAAC;EAAA;AAAA;AAAA;EAItBC,YAAA,GAAe;EAAA;AAAA;AAAA;EAIfC,QAAA,GAAW;EAAA;AAAA;AAAA;EAIXC,SAAA,GAAwB;EAAA;AAAA;AAAA;EAIxBjC,OAAA;EAAA;AAAA;AAAA;EAOAkC,YAAA;EAAA;AAAA;AAAA;EAIAC,cAAA;EAAA;EAGiBC,OAAA,GAAU,IAAI7G,OAAA,CAAQ;EACtBmF,OAAA,GAAU,IAAI3B,OAAA,CAAQ;EAAA;EAE9BsD,UAAA;EAAA;EACQC,aAAA;EAEjBnF,YAAY;IACVC,OAAA,GAAUO,MAAA;IACVN,OAAA,GAAUkF,QAAA,CAASC,eAAA;IACnBC,YAAA,GAAerF,OAAA;IACfsF,WAAA,GAAc;IACdC,SAAA,GAAY;IACZC,aAAA,GAAgB;IAChBC,oBAAA,GAAuB;IACvBhH,QAAA;IAAA;IACAC,MAAA;IACAlB,IAAA,EAAA2B,KAAA,GAAO;IACPuG,QAAA,GAAW;IACXC,WAAA,GAAc;IAAA;IACdC,kBAAA,GAAqB;IAAA;IACrB9C,eAAA,GAAkB;IAClBD,eAAA,GAAkB;IAClB3C,UAAA,GAAa;IACb2F,OAAA;IACAX,aAAA;IACAY,UAAA,GAAa;IACbC,OAAA,GAAU;IACVC,OAAA,GAAU;IACVC,UAAA,GAAa;IAAA;IACbC,iBAAA,GAAoB;IACpBC,+BAAA,GAAkC;EACpC,IAAkB,CAAC,GAAG;IAEpB5F,MAAA,CAAO6F,YAAA,GAAelJ,OAAA;IAGtB,IAAI,CAAC8C,OAAA,IAAWA,OAAA,KAAYmF,QAAA,CAASC,eAAA,EAAiB;MACpDpF,OAAA,GAAUO,MAAA;IACZ;IAGA,IAAI,OAAO9B,QAAA,KAAa,YAAY,OAAOC,MAAA,KAAW,YAAY;MAChEA,MAAA,GAASqF,aAAA;IACX,WAAW,OAAOrF,MAAA,KAAW,cAAc,OAAOD,QAAA,KAAa,UAAU;MACvEA,QAAA,GAAW;IACb;IAGA,KAAKmE,OAAA,GAAU;MACb5C,OAAA;MACAC,OAAA;MACAoF,YAAA;MACAC,WAAA;MACAC,SAAA;MACAC,aAAA;MACAC,oBAAA;MACAhH,QAAA;MACAC,MAAA;MACAlB,IAAA,EAAA2B,KAAA;MACAuG,QAAA;MACAE,kBAAA;MACAD,WAAA;MACA7C,eAAA;MACAD,eAAA;MACA3C,UAAA;MACA2F,OAAA;MACAX,aAAA;MACAY,UAAA;MACAC,OAAA;MACAC,OAAA;MACAC,UAAA;MACAC,iBAAA;MACAC;IACF;IAGA,KAAKlB,UAAA,GAAa,IAAInF,UAAA,CAAWE,OAAA,EAASC,OAAA,EAAS;MAAEC;IAAW,CAAC;IAGjE,KAAKmG,eAAA,CAAgB;IAGrB,KAAKvB,YAAA,GAAe,KAAKC,cAAA,GAAiB,KAAKuB,YAAA;IAG/C,KAAK1D,OAAA,CAAQ5C,OAAA,CAAQQ,gBAAA,CAAiB,UAAU,KAAK+F,cAAA,EAAgB,KAAK;IAE1E,KAAK3D,OAAA,CAAQ5C,OAAA,CAAQQ,gBAAA,CAAiB,aAAa,KAAKgG,WAAA,EAAa;MACnEC,OAAA,EAAS;IACX,CAAC;IAED,IAAI,KAAK7D,OAAA,CAAQoD,OAAA,IAAW,KAAKpD,OAAA,CAAQ5C,OAAA,KAAYO,MAAA,EAAQ;MAC3D,KAAKqC,OAAA,CAAQ5C,OAAA,CAAQQ,gBAAA,CACnB,SACA,KAAKkG,OAAA,EACL,KACF;IACF;IAEA,KAAK9D,OAAA,CAAQ5C,OAAA,CAAQQ,gBAAA,CACnB,eACA,KAAKmG,aAAA,EACL,KACF;IAGA,KAAKzB,aAAA,GAAgB,IAAIxC,aAAA,CAAc2C,YAAA,EAA6B;MAClEvC,eAAA;MACAD;IACF,CAAC;IACD,KAAKqC,aAAA,CAAchD,EAAA,CAAG,UAAU,KAAK0E,eAAe;IAEpD,IAAI,KAAKhE,OAAA,CAAQqD,UAAA,EAAY;MAC3B,KAAKY,WAAA,CAAYrG,gBAAA,CAAiB,iBAAiB,KAAKsG,eAAA,EAAiB;QACvErE,OAAA,EAAS;MACX,CAAC;IACH;IAEA,IAAI,KAAKG,OAAA,CAAQmD,OAAA,EAAS;MACxB,KAAKxB,OAAA,GAAUwC,qBAAA,CAAsB,KAAKC,GAAG;IAC/C;EACF;EAAA;AAAA;AAAA;EAKA/F,QAAA,EAAU;IACR,KAAKqC,OAAA,CAAQrC,OAAA,CAAQ;IAErB,KAAK2B,OAAA,CAAQ5C,OAAA,CAAQmB,mBAAA,CACnB,UACA,KAAKoF,cAAA,EACL,KACF;IAEA,KAAK3D,OAAA,CAAQ5C,OAAA,CAAQmB,mBAAA,CAAoB,aAAa,KAAKqF,WAAA,EAAa;MACtEC,OAAA,EAAS;IACX,CAAC;IAED,KAAK7D,OAAA,CAAQ5C,OAAA,CAAQmB,mBAAA,CACnB,eACA,KAAKwF,aAAA,EACL,KACF;IAEA,IAAI,KAAK/D,OAAA,CAAQoD,OAAA,IAAW,KAAKpD,OAAA,CAAQ5C,OAAA,KAAYO,MAAA,EAAQ;MAC3D,KAAKqC,OAAA,CAAQ5C,OAAA,CAAQmB,mBAAA,CACnB,SACA,KAAKuF,OAAA,EACL,KACF;IACF;IAEA,KAAKxB,aAAA,CAAcjE,OAAA,CAAQ;IAC3B,KAAKgE,UAAA,CAAWhE,OAAA,CAAQ;IAExB,KAAKgG,gBAAA,CAAiB;IAEtB,IAAI,KAAK1C,OAAA,EAAS;MAChB2C,oBAAA,CAAqB,KAAK3C,OAAO;IACnC;EACF;EAWArC,GAAGJ,KAAA,EAAmBxC,QAAA,EAAe;IACnC,OAAO,KAAKgE,OAAA,CAAQpB,EAAA,CAAGJ,KAAA,EAAOxC,QAAQ;EACxC;EAUAgD,IAAIR,KAAA,EAAmBxC,QAAA,EAAe;IACpC,OAAO,KAAKgE,OAAA,CAAQhB,GAAA,CAAIR,KAAA,EAAOxC,QAAQ;EACzC;EAEQkH,WAAA,GAAeW,CAAA,IAA2B;IAChD,IAAI,EAAEA,CAAA,YAAaC,WAAA,GAAc;MAC/B,IAAI,KAAKC,WAAA,KAAgB,YAAY,KAAKA,WAAA,KAAgB,OAAO;QAC/DF,CAAA,CAAEG,eAAA,CAAgB;MACpB;IACF;EACF;EAEQC,sBAAA,GAAyBA,CAAA,KAAM;IACrC,KAAK3E,OAAA,CAAQ5C,OAAA,CAAQwH,aAAA,CACnB,IAAIJ,WAAA,CAAY,aAAa;MAC3BK,OAAA,EAAS,KAAK7E,OAAA,CAAQ5C,OAAA,KAAYO,MAAA;MAAA;MAElCmH,MAAA,EAAQ;QACNC,cAAA,EAAgB;MAClB;IACF,CAAC,CACH;EACF;EAEQb,eAAA,GAAmBhF,KAAA,IAA2B;IACpD,IAAIA,KAAA,CAAM8F,YAAA,CAAaC,QAAA,CAAS,UAAU,GAAG;MAC3C,MAAMC,QAAA,GAAW,KAAKC,YAAA,GAAe,eAAe;MAEpD,MAAMC,QAAA,GAAWC,gBAAA,CAAiB,KAAKpB,WAAW,EAChDiB,QACF;MAEA,IAAI,CAAC,UAAU,MAAM,EAAED,QAAA,CAASG,QAAQ,GAAG;QACzC,KAAKE,YAAA,CAAa;MACpB,OAAO;QACL,KAAKC,aAAA,CAAc;MACrB;IACF;EACF;EAEQC,UAAUC,MAAA,EAAgB;IAGhC,IAAI,KAAKN,YAAA,EAAc;MACrB,KAAKnF,OAAA,CAAQ5C,OAAA,CAAQsI,QAAA,CAAS;QAAEC,IAAA,EAAMF,MAAA;QAAQG,QAAA,EAAU;MAAU,CAAC;IACrE,OAAO;MACL,KAAK5F,OAAA,CAAQ5C,OAAA,CAAQsI,QAAA,CAAS;QAAEG,GAAA,EAAKJ,MAAA;QAAQG,QAAA,EAAU;MAAU,CAAC;IACpE;EACF;EAEQ9B,OAAA,GAAW5E,KAAA,IAAqC;IACtD,MAAM4G,IAAA,GAAO5G,KAAA,CAAM6G,YAAA,CAAa;IAChC,MAAMC,MAAA,GAASF,IAAA,CAAKG,IAAA,CACjBC,IAAA,IACCA,IAAA,YAAgBC,iBAAA,KACfD,IAAA,CAAKE,YAAA,CAAa,MAAM,GAAGC,UAAA,CAAW,GAAG,KACxCH,IAAA,CAAKE,YAAA,CAAa,MAAM,GAAGC,UAAA,CAAW,IAAI,KAC1CH,IAAA,CAAKE,YAAA,CAAa,MAAM,GAAGC,UAAA,CAAW,KAAK,EACjD;IACA,IAAIL,MAAA,EAAQ;MACV,MAAMM,EAAA,GAAKN,MAAA,CAAOI,YAAA,CAAa,MAAM;MAErC,IAAIE,EAAA,EAAI;QACN,MAAMtG,OAAA,GACJ,OAAO,KAAKA,OAAA,CAAQoD,OAAA,KAAY,YAAY,KAAKpD,OAAA,CAAQoD,OAAA,GACrD,KAAKpD,OAAA,CAAQoD,OAAA,GACb;QAEN,IAAImD,MAAA,GAA0B,IAAID,EAAA,CAAGE,KAAA,CAAM,GAAG,EAAE,CAAC,CAAC;QAClD,IAAI,CAAC,KAAK,MAAM,OAAO,QAAQ,SAAS,QAAQ,EAAEvB,QAAA,CAASqB,EAAE,GAAG;UAC9DC,MAAA,GAAS;QACX;QAEA,KAAKb,QAAA,CAASa,MAAA,EAAQvG,OAAO;MAC/B;IACF;EACF;EAEQ+D,aAAA,GAAiB7E,KAAA,IAAqC;IAC5D,IAAIA,KAAA,CAAMuH,MAAA,KAAW,GAAG;MACtB,KAAKC,KAAA,CAAM;IACb;EACF;EAEQ1C,eAAA,GAAmB2C,IAAA,IAA4B;IACrD,IACE,OAAO,KAAK3G,OAAA,CAAQsC,aAAA,KAAkB,cACtC,KAAKtC,OAAA,CAAQsC,aAAA,CAAcqE,IAAI,MAAM,OAErC;IAEF,MAAM;MAAE7F,MAAA;MAAQC,MAAA;MAAQ7B;IAAM,IAAIyH,IAAA;IAElC,KAAKjG,OAAA,CAAQzB,IAAA,CAAK,kBAAkB;MAAE6B,MAAA;MAAQC,MAAA;MAAQ7B;IAAM,CAAC;IAG7D,IAAIA,KAAA,CAAM0H,OAAA,EAAS;IAEnB,IAAI1H,KAAA,CAAM2H,oBAAA,EAAsB;IAEhC,MAAMC,OAAA,GAAU5H,KAAA,CAAM6H,IAAA,CAAK9B,QAAA,CAAS,OAAO;IAC3C,MAAM+B,OAAA,GAAU9H,KAAA,CAAM6H,IAAA,CAAK9B,QAAA,CAAS,OAAO;IAE3C,KAAKrD,UAAA,GAAa1C,KAAA,CAAM6H,IAAA,KAAS,gBAAgB7H,KAAA,CAAM6H,IAAA,KAAS;IAWhE,MAAME,YAAA,GAAenG,MAAA,KAAW,KAAKC,MAAA,KAAW;IAEhD,MAAMmG,WAAA,GACJ,KAAKlH,OAAA,CAAQ2C,SAAA,IACbmE,OAAA,IACA5H,KAAA,CAAM6H,IAAA,KAAS,gBACfE,YAAA,IACA,CAAC,KAAKE,SAAA,IACN,CAAC,KAAKC,QAAA;IAER,IAAIF,WAAA,EAAa;MACf,KAAKR,KAAA,CAAM;MACX;IACF;IAQA,MAAMW,gBAAA,GACH,KAAKrH,OAAA,CAAQgD,kBAAA,KAAuB,cAAcjC,MAAA,KAAW,KAC7D,KAAKf,OAAA,CAAQgD,kBAAA,KAAuB,gBAAgBlC,MAAA,KAAW;IAElE,IAAImG,YAAA,IAAgBI,gBAAA,EAAkB;MAEpC;IACF;IAGA,IAAItB,YAAA,GAAe7G,KAAA,CAAM6G,YAAA,CAAa;IACtCA,YAAA,GAAeA,YAAA,CAAauB,KAAA,CAAM,GAAGvB,YAAA,CAAawB,OAAA,CAAQ,KAAKtD,WAAW,CAAC;IAE3E,MAAMhB,OAAA,GAAU,KAAKjD,OAAA,CAAQiD,OAAA;IAE7B,IACE,CAAC,CAAC8C,YAAA,CAAaE,IAAA,CACZC,IAAA,IACCA,IAAA,YAAgBsB,WAAA,KACd,OAAOvE,OAAA,KAAY,cAAcA,OAAA,GAAUiD,IAAI,KAC/CA,IAAA,CAAKuB,YAAA,GAAe,oBAAoB,KACvCX,OAAA,IAAWZ,IAAA,CAAKuB,YAAA,GAAe,0BAA0B,KACzDT,OAAA,IAAWd,IAAA,CAAKuB,YAAA,GAAe,0BAA0B,KACzD,KAAKzH,OAAA,CAAQsD,iBAAA,IACZ,KAAKoE,iBAAA,CAAkBxB,IAAA,EAAM;MAAEpF,MAAA;MAAQC;IAAO,CAAC,EACvD,GAEA;IAEF,IAAI,KAAKoG,SAAA,IAAa,KAAKC,QAAA,EAAU;MACnC,IAAIlI,KAAA,CAAMyI,UAAA,EAAY;QACpBzI,KAAA,CAAM0I,cAAA,CAAe;MACvB;MACA;IACF;IAEA,MAAMC,QAAA,GACH,KAAK7H,OAAA,CAAQ2C,SAAA,IAAamE,OAAA,IAC1B,KAAK9G,OAAA,CAAQ0C,WAAA,IAAesE,OAAA;IAE/B,IAAI,CAACa,QAAA,EAAU;MACb,KAAKpD,WAAA,GAAc;MACnB,KAAKrC,OAAA,CAAQ/F,IAAA,CAAK;MAElB6C,KAAA,CAAM2H,oBAAA,GAAuB;MAC7B;IACF;IAEA,IAAIiB,KAAA,GAAQ/G,MAAA;IACZ,IAAI,KAAKf,OAAA,CAAQgD,kBAAA,KAAuB,QAAQ;MAC9C8E,KAAA,GAAQnN,IAAA,CAAKoN,GAAA,CAAIhH,MAAM,IAAIpG,IAAA,CAAKoN,GAAA,CAAIjH,MAAM,IAAIC,MAAA,GAASD,MAAA;IACzD,WAAW,KAAKd,OAAA,CAAQgD,kBAAA,KAAuB,cAAc;MAC3D8E,KAAA,GAAQhH,MAAA;IACV;IAEA,IACE,CAAC,KAAKd,OAAA,CAAQkD,UAAA,IACd,KAAKlD,OAAA,CAAQ8C,QAAA,IACZ,KAAK9C,OAAA,CAAQ5C,OAAA,KAAYO,MAAA,KACtB,KAAKwE,cAAA,GAAiB,KAAK,KAAKA,cAAA,GAAiB,KAAKrD,KAAA,IACrD,KAAKqD,cAAA,KAAmB,KAAKpB,MAAA,GAAS,KACtC,KAAKoB,cAAA,KAAmB,KAAKrD,KAAA,IAASiC,MAAA,GAAS,IACpD;MAEA7B,KAAA,CAAM2H,oBAAA,GAAuB;IAE/B;IAEA,IAAI3H,KAAA,CAAMyI,UAAA,EAAY;MACpBzI,KAAA,CAAM0I,cAAA,CAAe;IACvB;IAEA,MAAMI,WAAA,GAAclB,OAAA,IAAW,KAAK9G,OAAA,CAAQ2C,SAAA;IAC5C,MAAMsF,UAAA,GAAanB,OAAA,IAAW5H,KAAA,CAAM6H,IAAA,KAAS;IAE7C,MAAMmB,eAAA,GAAkBD,UAAA;IAExB,IAAIC,eAAA,EAAiB;MAEnBJ,KAAA,GACEnN,IAAA,CAAKwN,IAAA,CAAK,KAAKnG,QAAQ,IACvBrH,IAAA,CAAKyG,GAAA,CAAIzG,IAAA,CAAKoN,GAAA,CAAI,KAAK/F,QAAQ,GAAG,KAAKhC,OAAA,CAAQ6C,oBAAoB;IACvE;IAEA,KAAK6C,QAAA,CAAS,KAAKxD,YAAA,GAAe4F,KAAA,EAAO;MACvCM,YAAA,EAAc;MACd,IAAIJ,WAAA,GACA;QACEpN,IAAA,EAAMsN,eAAA,GAAkB,KAAKlI,OAAA,CAAQ4C,aAAA,GAAgB;QAAA;MAEvD,IACA;QACEhI,IAAA,EAAM,KAAKoF,OAAA,CAAQpF,IAAA;QACnBiB,QAAA,EAAU,KAAKmE,OAAA,CAAQnE,QAAA;QACvBC,MAAA,EAAQ,KAAKkE,OAAA,CAAQlE;MACvB;IACN,CAAC;EACH;EAAA;AAAA;AAAA;EAKA2B,OAAA,EAAS;IACP,KAAK4E,UAAA,CAAW5E,MAAA,CAAO;IACvB,KAAK0E,cAAA,GAAiB,KAAKD,YAAA,GAAe,KAAKwB,YAAA;IAC/C,KAAKzE,IAAA,CAAK;EACZ;EAEQA,KAAA,EAAO;IACb,KAAKyB,OAAA,CAAQzB,IAAA,CAAK,UAAU,IAAI;EAClC;EAEQ0E,cAAA,GAAiBA,CAAA,KAAM;IAC7B,IAAI,KAAKjC,qBAAA,KAA0B,MAAM;MACvC3E,YAAA,CAAa,KAAK2E,qBAAqB;MACvC,KAAKA,qBAAA,GAAwB;IAC/B;IAEA,IAAI,KAAKD,6BAAA,EAA+B;MACtC,KAAKA,6BAAA,GAAgC;MACrC;IACF;IAEA,IAAI,KAAKgD,WAAA,KAAgB,SAAS,KAAKA,WAAA,KAAgB,UAAU;MAC/D,MAAM4D,UAAA,GAAa,KAAKlG,cAAA;MACxB,KAAKA,cAAA,GAAiB,KAAKD,YAAA,GAAe,KAAKwB,YAAA;MAC/C,KAAK3B,YAAA,GAAe,KAAKC,QAAA;MACzB,KAAKA,QAAA,GAAW,KAAKG,cAAA,GAAiBkG,UAAA;MACtC,KAAKpG,SAAA,GAAYtH,IAAA,CAAKwN,IAAA,CACpB,KAAKhG,cAAA,GAAiBkG,UACxB;MAEA,IAAI,CAAC,KAAKlB,SAAA,EAAW;QACnB,KAAK1C,WAAA,GAAc;MACrB;MAEA,KAAKxF,IAAA,CAAK;MAEV,IAAI,KAAK+C,QAAA,KAAa,GAAG;QACvB,KAAKN,qBAAA,GAAwB1E,UAAA,CAAW,MAAM;UAC5C,KAAK+E,YAAA,GAAe,KAAKC,QAAA;UACzB,KAAKA,QAAA,GAAW;UAChB,KAAKyC,WAAA,GAAc;UACnB,KAAKxF,IAAA,CAAK;QACZ,GAAG,GAAG;MACR;IACF;EACF;EAEQyH,MAAA,EAAQ;IACd,KAAKU,QAAA,GAAW;IAChB,KAAK3C,WAAA,GAAc;IACnB,KAAKtC,cAAA,GAAiB,KAAKD,YAAA,GAAe,KAAKwB,YAAA;IAC/C,KAAK3B,YAAA,GAAe,KAAKC,QAAA,GAAW;IACpC,KAAKI,OAAA,CAAQ/F,IAAA,CAAK;EACpB;EAAA;AAAA;AAAA;EAKAiM,MAAA,EAAQ;IACN,IAAI,CAAC,KAAKnB,SAAA,EAAW;IAErB,IAAI,KAAKnH,OAAA,CAAQqD,UAAA,EAAY;MAC3B,KAAKY,WAAA,CAAYsE,KAAA,CAAMC,cAAA,CAAe,UAAU;MAChD;IACF;IAEA,KAAKjD,aAAA,CAAc;EACrB;EAEQA,cAAA,EAAgB;IACtB,IAAI,CAAC,KAAK4B,SAAA,EAAW;IAErB,KAAKT,KAAA,CAAM;IACX,KAAKS,SAAA,GAAY;IACjB,KAAKlI,IAAA,CAAK;EACZ;EAAA;AAAA;AAAA;EAKA5C,KAAA,EAAO;IACL,IAAI,KAAK8K,SAAA,EAAW;IAEpB,IAAI,KAAKnH,OAAA,CAAQqD,UAAA,EAAY;MAC3B,KAAKY,WAAA,CAAYsE,KAAA,CAAME,WAAA,CAAY,YAAY,MAAM;MACrD;IACF;IAEA,KAAKnD,YAAA,CAAa;EACpB;EAEQA,aAAA,EAAe;IACrB,IAAI,KAAK6B,SAAA,EAAW;IAEpB,KAAKT,KAAA,CAAM;IACX,KAAKS,SAAA,GAAY;IACjB,KAAKlI,IAAA,CAAK;EACZ;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAmF,GAAA,GAAOvC,IAAA,IAAiB;IACtB,MAAM3G,SAAA,GAAY2G,IAAA,IAAQ,KAAKA,IAAA,IAAQA,IAAA;IACvC,KAAKA,IAAA,GAAOA,IAAA;IAEZ,KAAKO,OAAA,CAAQpG,OAAA,CAAQd,SAAA,GAAY,IAAK;IAEtC,IAAI,KAAK8E,OAAA,CAAQmD,OAAA,EAAS;MACxB,KAAKxB,OAAA,GAAUwC,qBAAA,CAAsB,KAAKC,GAAG;IAC/C;EACF;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAsBAsB,SACEa,MAAA,EACA;IACEmC,MAAA,GAAS;IACTC,SAAA,GAAY;IACZC,IAAA,GAAO;IACP/M,QAAA,GAAW,KAAKmE,OAAA,CAAQnE,QAAA;IACxBC,MAAA,GAAS,KAAKkE,OAAA,CAAQlE,MAAA;IACtBlB,IAAA,EAAA2B,KAAA,GAAO,KAAKyD,OAAA,CAAQpF,IAAA;IACpB4B,OAAA;IACAqM,UAAA;IACAC,KAAA,GAAQ;IAAA;IACRV,YAAA,GAAe;IAAA;IACftG;EACF,IAAqB,CAAC,GACtB;IACA,KAAK,KAAKqF,SAAA,IAAa,KAAKC,QAAA,KAAa,CAAC0B,KAAA,EAAO;IAGjD,IACE,OAAOvC,MAAA,KAAW,YAClB,CAAC,OAAO,QAAQ,OAAO,EAAEtB,QAAA,CAASsB,MAAM,GACxC;MACAA,MAAA,GAAS;IACX,WACE,OAAOA,MAAA,KAAW,YAClB,CAAC,UAAU,SAAS,KAAK,EAAEtB,QAAA,CAASsB,MAAM,GAC1C;MACAA,MAAA,GAAS,KAAKzH,KAAA;IAChB,OAAO;MACL,IAAIoH,IAAA;MAEJ,IAAI,OAAOK,MAAA,KAAW,UAAU;QAE9BL,IAAA,GAAO3D,QAAA,CAASwG,aAAA,CAAcxC,MAAM;MACtC,WAAWA,MAAA,YAAkBiB,WAAA,IAAejB,MAAA,EAAQyC,QAAA,EAAU;QAE5D9C,IAAA,GAAOK,MAAA;MACT;MAEA,IAAIL,IAAA,EAAM;QACR,IAAI,KAAKlG,OAAA,CAAQ5C,OAAA,KAAYO,MAAA,EAAQ;UAEnC,MAAMsL,WAAA,GAAc,KAAKhF,WAAA,CAAYiF,qBAAA,CAAsB;UAC3DR,MAAA,IAAU,KAAKvD,YAAA,GAAe8D,WAAA,CAAYtD,IAAA,GAAOsD,WAAA,CAAYpD,GAAA;QAC/D;QAEA,MAAMsD,IAAA,GAAOjD,IAAA,CAAKgD,qBAAA,CAAsB;QAExC3C,MAAA,IACG,KAAKpB,YAAA,GAAegE,IAAA,CAAKxD,IAAA,GAAOwD,IAAA,CAAKtD,GAAA,IAAO,KAAK1D,cAAA;MACtD;IACF;IAEA,IAAI,OAAOoE,MAAA,KAAW,UAAU;IAEhCA,MAAA,IAAUmC,MAAA;IACVnC,MAAA,GAAS5L,IAAA,CAAKyB,KAAA,CAAMmK,MAAM;IAE1B,IAAI,KAAKvG,OAAA,CAAQ8C,QAAA,EAAU;MACzB,IAAIsF,YAAA,EAAc;QAChB,KAAKlG,YAAA,GAAe,KAAKC,cAAA,GAAiB,KAAKsD,MAAA;QAE/C,MAAM2D,QAAA,GAAW7C,MAAA,GAAS,KAAKpE,cAAA;QAE/B,IAAIiH,QAAA,GAAW,KAAKtK,KAAA,GAAQ,GAAG;UAC7ByH,MAAA,GAASA,MAAA,GAAS,KAAKzH,KAAA;QACzB,WAAWsK,QAAA,GAAW,CAAC,KAAKtK,KAAA,GAAQ,GAAG;UACrCyH,MAAA,GAASA,MAAA,GAAS,KAAKzH,KAAA;QACzB;MACF;IACF,OAAO;MACLyH,MAAA,GAAShM,KAAA,CAAM,GAAGgM,MAAA,EAAQ,KAAKzH,KAAK;IACtC;IAEA,IAAIyH,MAAA,KAAW,KAAKrE,YAAA,EAAc;MAChC1F,OAAA,GAAU,IAAI;MACdqM,UAAA,GAAa,IAAI;MACjB;IACF;IAEA,KAAK/G,QAAA,GAAWA,QAAA,IAAY,CAAC;IAE7B,IAAI6G,SAAA,EAAW;MACb,KAAKxG,cAAA,GAAiB,KAAKD,YAAA,GAAeqE,MAAA;MAC1C,KAAKf,SAAA,CAAU,KAAKC,MAAM;MAC1B,KAAKiB,KAAA,CAAM;MACX,KAAK2C,4BAAA,CAA6B;MAClC,KAAKpK,IAAA,CAAK;MACV4J,UAAA,GAAa,IAAI;MACjB,KAAK/G,QAAA,GAAW,CAAC;MAEjBqC,qBAAA,CAAsB,MAAM;QAC1B,KAAKQ,sBAAA,CAAuB;MAC9B,CAAC;MACD;IACF;IAEA,IAAI,CAACyD,YAAA,EAAc;MACjB,KAAKlG,YAAA,GAAeqE,MAAA;IACtB;IAGA,IAAI,OAAO1K,QAAA,KAAa,YAAY,OAAOC,MAAA,KAAW,YAAY;MAChEA,MAAA,GAASqF,aAAA;IACX,WAAW,OAAOrF,MAAA,KAAW,cAAc,OAAOD,QAAA,KAAa,UAAU;MACvEA,QAAA,GAAW;IACb;IAEA,KAAKuG,OAAA,CAAQ9F,MAAA,CAAO,KAAK6F,cAAA,EAAgBoE,MAAA,EAAQ;MAC/C1K,QAAA;MACAC,MAAA;MACAlB,IAAA,EAAA2B,KAAA;MACAC,OAAA,EAASA,CAAA,KAAM;QAEb,IAAIoM,IAAA,EAAM,KAAKxB,QAAA,GAAW;QAC1B,KAAK3C,WAAA,GAAc;QACnBjI,OAAA,GAAU,IAAI;MAChB;MACAT,QAAA,EAAUA,CAACN,KAAA,EAAeQ,SAAA,KAAuB;QAC/C,KAAKwI,WAAA,GAAc;QAGnB,KAAK1C,YAAA,GAAe,KAAKC,QAAA;QACzB,KAAKA,QAAA,GAAWvG,KAAA,GAAQ,KAAK0G,cAAA;QAC7B,KAAKF,SAAA,GAAYtH,IAAA,CAAKwN,IAAA,CAAK,KAAKnG,QAAQ;QAExC,KAAKG,cAAA,GAAiB1G,KAAA;QACtB,KAAK+J,SAAA,CAAU,KAAKC,MAAM;QAE1B,IAAI2C,YAAA,EAAc;UAEhB,KAAKlG,YAAA,GAAezG,KAAA;QACtB;QAEA,IAAI,CAACQ,SAAA,EAAW,KAAKgD,IAAA,CAAK;QAE1B,IAAIhD,SAAA,EAAW;UACb,KAAKyK,KAAA,CAAM;UACX,KAAKzH,IAAA,CAAK;UACV4J,UAAA,GAAa,IAAI;UACjB,KAAK/G,QAAA,GAAW,CAAC;UAEjBqC,qBAAA,CAAsB,MAAM;YAC1B,KAAKQ,sBAAA,CAAuB;UAC9B,CAAC;UAGD,KAAK0E,4BAAA,CAA6B;QACpC;MACF;IACF,CAAC;EACH;EAEQA,6BAAA,EAA+B;IACrC,KAAK5H,6BAAA,GAAgC;IAErC0C,qBAAA,CAAsB,MAAM;MAC1B,KAAK1C,6BAAA,GAAgC;IACvC,CAAC;EACH;EAEQiG,kBACNxB,IAAA,EACA;IAAEpF,MAAA;IAAQC;EAAO,GACjB;IACA,MAAMc,IAAA,GAAOyH,IAAA,CAAKC,GAAA,CAAI;IAGtB,MAAMC,KAAA,GAAStD,IAAA,CAAKuD,MAAA,KAAW,CAAC;IAEhC,IAAIC,YAAA,EACFC,YAAA,EACAC,aAAA,EACAC,aAAA,EACAzL,WAAA,EACAD,YAAA,EACAS,WAAA,EACAC,YAAA;IAEF,MAAMmE,kBAAA,GAAqB,KAAKhD,OAAA,CAAQgD,kBAAA;IAExC,IAAInB,IAAA,IAAQ2H,KAAA,CAAM3H,IAAA,IAAQ,KAAK,KAAM;MACnC2H,KAAA,CAAM3H,IAAA,GAAOyH,IAAA,CAAKC,GAAA,CAAI;MAEtB,MAAMO,aAAA,GAAgBnM,MAAA,CAAO0H,gBAAA,CAAiBa,IAAI;MAClDsD,KAAA,CAAMM,aAAA,GAAgBA,aAAA;MAEtB,MAAMC,eAAA,GAAkBD,aAAA,CAAcE,SAAA;MACtC,MAAMC,eAAA,GAAkBH,aAAA,CAAcI,SAAA;MAEtCR,YAAA,GAAe,CAAC,QAAQ,WAAW,QAAQ,EAAEzE,QAAA,CAAS8E,eAAe;MACrEJ,YAAA,GAAe,CAAC,QAAQ,WAAW,QAAQ,EAAE1E,QAAA,CAASgF,eAAe;MACrET,KAAA,CAAME,YAAA,GAAeA,YAAA;MACrBF,KAAA,CAAMG,YAAA,GAAeA,YAAA;MAErB,IAAI,CAACD,YAAA,IAAgB,CAACC,YAAA,EAAc,OAAO;MAC3C,IAAI3G,kBAAA,KAAuB,cAAc,CAAC2G,YAAA,EAAc,OAAO;MAC/D,IAAI3G,kBAAA,KAAuB,gBAAgB,CAAC0G,YAAA,EAAc,OAAO;MAEjEtL,WAAA,GAAc8H,IAAA,CAAK9H,WAAA;MACnBD,YAAA,GAAe+H,IAAA,CAAK/H,YAAA;MAEpBS,WAAA,GAAcsH,IAAA,CAAKtH,WAAA;MACnBC,YAAA,GAAeqH,IAAA,CAAKrH,YAAA;MAEpB+K,aAAA,GAAgBxL,WAAA,GAAcQ,WAAA;MAC9BiL,aAAA,GAAgB1L,YAAA,GAAeU,YAAA;MAE/B2K,KAAA,CAAMI,aAAA,GAAgBA,aAAA;MACtBJ,KAAA,CAAMK,aAAA,GAAgBA,aAAA;MACtBL,KAAA,CAAMpL,WAAA,GAAcA,WAAA;MACpBoL,KAAA,CAAMrL,YAAA,GAAeA,YAAA;MACrBqL,KAAA,CAAM5K,WAAA,GAAcA,WAAA;MACpB4K,KAAA,CAAM3K,YAAA,GAAeA,YAAA;IACvB,OAAO;MACL+K,aAAA,GAAgBJ,KAAA,CAAMI,aAAA;MACtBC,aAAA,GAAgBL,KAAA,CAAMK,aAAA;MACtBH,YAAA,GAAeF,KAAA,CAAME,YAAA;MACrBC,YAAA,GAAeH,KAAA,CAAMG,YAAA;MACrBvL,WAAA,GAAcoL,KAAA,CAAMpL,WAAA;MACpBD,YAAA,GAAeqL,KAAA,CAAMrL,YAAA;MACrBS,WAAA,GAAc4K,KAAA,CAAM5K,WAAA;MACpBC,YAAA,GAAe2K,KAAA,CAAM3K,YAAA;IACvB;IAEA,IACG,CAAC6K,YAAA,IAAgB,CAACC,YAAA,IAClB,CAACC,aAAA,IAAiB,CAACC,aAAA,EACpB;MACA,OAAO;IACT;IAEA,IAAI7G,kBAAA,KAAuB,eAAe,CAAC2G,YAAA,IAAgB,CAACE,aAAA,GAC1D,OAAO;IAET,IACE7G,kBAAA,KAAuB,iBACtB,CAAC0G,YAAA,IAAgB,CAACE,aAAA,GAEnB,OAAO;IAET,IAAI7G,WAAA;IAEJ,IAAIC,kBAAA,KAAuB,cAAc;MACvCD,WAAA,GAAc;IAChB,WAAWC,kBAAA,KAAuB,YAAY;MAC5CD,WAAA,GAAc;IAChB,OAAO;MACL,MAAMoH,YAAA,GAAerJ,MAAA,KAAW;MAChC,MAAMsJ,YAAA,GAAerJ,MAAA,KAAW;MAEhC,IAAIoJ,YAAA,IAAgBT,YAAA,IAAgBE,aAAA,EAAe;QACjD7G,WAAA,GAAc;MAChB;MAEA,IAAIqH,YAAA,IAAgBT,YAAA,IAAgBE,aAAA,EAAe;QACjD9G,WAAA,GAAc;MAChB;IACF;IAEA,IAAI,CAACA,WAAA,EAAa,OAAO;IAEzB,IAAI0C,MAAA,EAAQ4E,SAAA,EAAWvC,KAAA,EAAOwC,WAAA,EAAaC,YAAA;IAE3C,IAAIxH,WAAA,KAAgB,KAAK;MACvB0C,MAAA,GAASS,IAAA,CAAKsE,UAAA;MACdH,SAAA,GAAYjM,WAAA,GAAcQ,WAAA;MAC1BkJ,KAAA,GAAQhH,MAAA;MAERwJ,WAAA,GAAcZ,YAAA;MACda,YAAA,GAAeX,aAAA;IACjB,WAAW7G,WAAA,KAAgB,KAAK;MAC9B0C,MAAA,GAASS,IAAA,CAAKuE,SAAA;MACdJ,SAAA,GAAYlM,YAAA,GAAeU,YAAA;MAC3BiJ,KAAA,GAAQ/G,MAAA;MAERuJ,WAAA,GAAcX,YAAA;MACdY,YAAA,GAAeV,aAAA;IACjB,OAAO;MACL,OAAO;IACT;IAEA,MAAMa,UAAA,GAAa5C,KAAA,GAAQ,IAAIrC,MAAA,GAAS4E,SAAA,GAAY5E,MAAA,GAAS;IAE7D,OAAOiF,UAAA,IAAcJ,WAAA,IAAeC,YAAA;EACtC;EAAA;AAAA;AAAA;EAKA,IAAItG,YAAA,EAAc;IAChB,OACE,KAAKjE,OAAA,CAAQ5C,OAAA,KAAYO,MAAA,GACrB4E,QAAA,CAASC,eAAA,GACT,KAAKxC,OAAA,CAAQ5C,OAAA;EAErB;EAAA;AAAA;AAAA;EAKA,IAAI0B,MAAA,EAAQ;IACV,IAAI,KAAKkB,OAAA,CAAQuD,+BAAA,EAAiC;MAChD,IAAI,KAAK4B,YAAA,EAAc;QACrB,OAAO,KAAKlB,WAAA,CAAY7F,WAAA,GAAc,KAAK6F,WAAA,CAAYrF,WAAA;MACzD,OAAO;QACL,OAAO,KAAKqF,WAAA,CAAY9F,YAAA,GAAe,KAAK8F,WAAA,CAAYpF,YAAA;MAC1D;IACF,OAAO;MACL,OAAO,KAAKwD,UAAA,CAAWvD,KAAA,CAAM,KAAKqG,YAAA,GAAe,MAAM,GAAG;IAC5D;EACF;EAAA;AAAA;AAAA;EAKA,IAAIA,aAAA,EAAe;IACjB,OAAO,KAAKnF,OAAA,CAAQ+C,WAAA,KAAgB;EACtC;EAAA;AAAA;AAAA;EAKA,IAAIW,aAAA,EAAe;IAGjB,MAAMtG,OAAA,GAAU,KAAK4C,OAAA,CAAQ5C,OAAA;IAE7B,OAAO,KAAK+H,YAAA,GACP/H,OAAA,CAAmBuN,OAAA,IAAYvN,OAAA,CAAwBoN,UAAA,GACvDpN,OAAA,CAAmBwN,OAAA,IAAYxN,OAAA,CAAwBqN,SAAA;EAC9D;EAAA;AAAA;AAAA;EAKA,IAAIhF,OAAA,EAAS;IACX,OAAO,KAAKzF,OAAA,CAAQ8C,QAAA,GAChB1H,MAAA,CAAO,KAAK+G,cAAA,EAAgB,KAAKrD,KAAK,IACtC,KAAKqD,cAAA;EACX;EAAA;AAAA;AAAA;EAKA,IAAI0I,SAAA,EAAW;IAEb,OAAO,KAAK/L,KAAA,KAAU,IAAI,IAAI,KAAK2G,MAAA,GAAS,KAAK3G,KAAA;EACnD;EAAA;AAAA;AAAA;EAKA,IAAI2F,YAAA,EAAc;IAChB,OAAO,KAAKnD,YAAA;EACd;EAEA,IAAYmD,YAAYhJ,KAAA,EAAkB;IACxC,IAAI,KAAK6F,YAAA,KAAiB7F,KAAA,EAAO;MAC/B,KAAK6F,YAAA,GAAe7F,KAAA;MACpB,KAAKgI,eAAA,CAAgB;IACvB;EACF;EAAA;AAAA;AAAA;EAKA,IAAI0D,UAAA,EAAY;IACd,OAAO,KAAK5F,UAAA;EACd;EAEA,IAAY4F,UAAU1L,KAAA,EAAgB;IACpC,IAAI,KAAK8F,UAAA,KAAe9F,KAAA,EAAO;MAC7B,KAAK8F,UAAA,GAAa9F,KAAA;MAClB,KAAKgI,eAAA,CAAgB;IACvB;EACF;EAAA;AAAA;AAAA;EAKA,IAAI2D,SAAA,EAAW;IACb,OAAO,KAAK5F,SAAA;EACd;EAEA,IAAY4F,SAAS3L,KAAA,EAAgB;IACnC,IAAI,KAAK+F,SAAA,KAAc/F,KAAA,EAAO;MAC5B,KAAK+F,SAAA,GAAY/F,KAAA;MACjB,KAAKgI,eAAA,CAAgB;IACvB;EACF;EAAA;AAAA;AAAA;EAKA,IAAIoE,SAAA,EAAW;IACb,OAAO,KAAKpD,WAAA,KAAgB;EAC9B;EAAA;AAAA;AAAA;EAKA,IAAIqG,UAAA,EAAY;IACd,IAAIA,SAAA,GAAY;IAChB,IAAI,KAAK9K,OAAA,CAAQqD,UAAA,EAAYyH,SAAA,IAAa;IAC1C,IAAI,KAAK3D,SAAA,EAAW2D,SAAA,IAAa;IACjC,IAAI,KAAK1D,QAAA,EAAU0D,SAAA,IAAa;IAChC,IAAI,KAAKrG,WAAA,EAAaqG,SAAA,IAAa;IACnC,IAAI,KAAKrG,WAAA,KAAgB,UAAUqG,SAAA,IAAa;IAChD,OAAOA,SAAA;EACT;EAEQrH,gBAAA,EAAkB;IACxB,KAAKY,gBAAA,CAAiB;IAEtB,KAAKJ,WAAA,CAAY6G,SAAA,GACf,GAAG,KAAK7G,WAAA,CAAY6G,SAAS,IAAI,KAAKA,SAAS,GAAGC,IAAA,CAAK;EAC3D;EAEQ1G,iBAAA,EAAmB;IACzB,KAAKJ,WAAA,CAAY6G,SAAA,GAAY,KAAK7G,WAAA,CAAY6G,SAAA,CAC3CE,OAAA,CAAQ,iBAAiB,EAAE,EAC3BD,IAAA,CAAK;EACV;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}