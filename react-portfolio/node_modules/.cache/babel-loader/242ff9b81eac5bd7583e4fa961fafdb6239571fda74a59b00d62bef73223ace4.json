{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ScrollStack.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport \"./ScrollStack.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const ScrollStackItem = ({\n  children,\n  itemClassName = \"\"\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: `scroll-stack-card ${itemClassName}`.trim(),\n  children: children\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 5,\n  columnNumber: 3\n}, this);\n_c = ScrollStackItem;\nconst ScrollStack = ({\n  children,\n  className = \"\"\n}) => {\n  _s();\n  const [scrollProgress, setScrollProgress] = useState(0);\n  const containerRef = useRef(null);\n  useEffect(() => {\n    const handleScroll = () => {\n      if (!containerRef.current) return;\n      const rect = containerRef.current.getBoundingClientRect();\n      const windowHeight = window.innerHeight;\n\n      // Calculate progress: 0 when container enters viewport, 1 when it exits\n      let progress = 0;\n      if (rect.top <= windowHeight * 0.8 && rect.bottom >= windowHeight * 0.2) {\n        const totalScrollDistance = rect.height + windowHeight * 0.6;\n        const currentScrollDistance = windowHeight * 0.8 - rect.top;\n        progress = Math.max(0, Math.min(1, currentScrollDistance / totalScrollDistance));\n      } else if (rect.top < windowHeight * 0.2) {\n        progress = 1;\n      }\n      setScrollProgress(progress);\n      console.log(`ScrollStack Progress: ${progress.toFixed(2)}, rect.top: ${rect.top.toFixed(0)}`);\n\n      // Apply transforms to each card\n      const items = containerRef.current.querySelectorAll('.scroll-stack-item');\n      items.forEach((item, index) => {\n        const card = item.querySelector('.scroll-stack-card');\n        if (!card) return;\n\n        // Y position: start stacked, end spread\n        const stackedY = index * 20;\n        const spreadY = index * 200;\n        const currentY = stackedY + progress * (spreadY - stackedY);\n\n        // Scale: start smaller, end full size\n        const stackedScale = Math.pow(0.92, index);\n        const currentScale = stackedScale + progress * (1 - stackedScale);\n\n        // Rotation for 3D effect\n        const rotation = (1 - progress) * index * 8;\n\n        // Opacity\n        const opacity = 0.6 + progress * 0.4 + (index === 0 ? 0.2 : 0);\n        console.log(`Card ${index}: Y=${currentY.toFixed(1)}, Scale=${currentScale.toFixed(2)}, Progress=${progress.toFixed(2)}`);\n\n        // Apply transforms to the item (not the card inside)\n        item.style.transform = `translate(-50%, -50%) translateY(${currentY}px) scale(${currentScale}) rotateX(${rotation}deg)`;\n        item.style.opacity = Math.min(opacity, 1);\n        item.style.zIndex = items.length - index;\n      });\n    };\n    window.addEventListener('scroll', handleScroll, {\n      passive: true\n    });\n    handleScroll();\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: containerRef,\n    className: `scroll-stack ${className}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"scroll-stack-container\",\n      children: React.Children.map(children, (child, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"scroll-stack-item\",\n        children: child\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 70,\n    columnNumber: 5\n  }, this);\n};\n_s(ScrollStack, \"s1hDQaLO+Y+X3sQDuGrxH3NJatY=\");\n_c2 = ScrollStack;\nexport default ScrollStack;\nvar _c, _c2;\n$RefreshReg$(_c, \"ScrollStackItem\");\n$RefreshReg$(_c2, \"ScrollStack\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "ScrollStackItem", "children", "itemClassName", "className", "trim", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "ScrollStack", "_s", "scrollProgress", "setScrollProgress", "containerRef", "handleScroll", "current", "rect", "getBoundingClientRect", "windowHeight", "window", "innerHeight", "progress", "top", "bottom", "totalScrollDistance", "height", "currentScrollDistance", "Math", "max", "min", "console", "log", "toFixed", "items", "querySelectorAll", "for<PERSON>ach", "item", "index", "card", "querySelector", "stackedY", "spreadY", "currentY", "stackedScale", "pow", "currentScale", "rotation", "opacity", "style", "transform", "zIndex", "length", "addEventListener", "passive", "removeEventListener", "ref", "Children", "map", "child", "_c2", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ScrollStack.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport \"./ScrollStack.css\";\n\nexport const ScrollStackItem = ({ children, itemClassName = \"\" }) => (\n  <div className={`scroll-stack-card ${itemClassName}`.trim()}>{children}</div>\n);\n\nconst ScrollStack = ({ children, className = \"\" }) => {\n  const [scrollProgress, setScrollProgress] = useState(0);\n  const containerRef = useRef(null);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      if (!containerRef.current) return;\n\n      const rect = containerRef.current.getBoundingClientRect();\n      const windowHeight = window.innerHeight;\n\n      // Calculate progress: 0 when container enters viewport, 1 when it exits\n      let progress = 0;\n      if (rect.top <= windowHeight * 0.8 && rect.bottom >= windowHeight * 0.2) {\n        const totalScrollDistance = rect.height + windowHeight * 0.6;\n        const currentScrollDistance = windowHeight * 0.8 - rect.top;\n        progress = Math.max(0, Math.min(1, currentScrollDistance / totalScrollDistance));\n      } else if (rect.top < windowHeight * 0.2) {\n        progress = 1;\n      }\n\n      setScrollProgress(progress);\n\n      console.log(`ScrollStack Progress: ${progress.toFixed(2)}, rect.top: ${rect.top.toFixed(0)}`);\n\n      // Apply transforms to each card\n      const items = containerRef.current.querySelectorAll('.scroll-stack-item');\n      items.forEach((item, index) => {\n        const card = item.querySelector('.scroll-stack-card');\n        if (!card) return;\n\n        // Y position: start stacked, end spread\n        const stackedY = index * 20;\n        const spreadY = index * 200;\n        const currentY = stackedY + (progress * (spreadY - stackedY));\n\n        // Scale: start smaller, end full size\n        const stackedScale = Math.pow(0.92, index);\n        const currentScale = stackedScale + (progress * (1 - stackedScale));\n\n        // Rotation for 3D effect\n        const rotation = (1 - progress) * index * 8;\n\n        // Opacity\n        const opacity = 0.6 + (progress * 0.4) + (index === 0 ? 0.2 : 0);\n\n        console.log(`Card ${index}: Y=${currentY.toFixed(1)}, Scale=${currentScale.toFixed(2)}, Progress=${progress.toFixed(2)}`);\n\n        // Apply transforms to the item (not the card inside)\n        item.style.transform = `translate(-50%, -50%) translateY(${currentY}px) scale(${currentScale}) rotateX(${rotation}deg)`;\n        item.style.opacity = Math.min(opacity, 1);\n        item.style.zIndex = items.length - index;\n      });\n    };\n\n    window.addEventListener('scroll', handleScroll, { passive: true });\n    handleScroll();\n\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  return (\n    <div\n      ref={containerRef}\n      className={`scroll-stack ${className}`}\n    >\n      <div className=\"scroll-stack-container\">\n        {React.Children.map(children, (child, index) => (\n          <div key={index} className=\"scroll-stack-item\">\n            {child}\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n};\n\nexport default ScrollStack;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,OAAO,MAAMC,eAAe,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,aAAa,GAAG;AAAG,CAAC,kBAC9DH,OAAA;EAAKI,SAAS,EAAE,qBAAqBD,aAAa,EAAE,CAACE,IAAI,CAAC,CAAE;EAAAH,QAAA,EAAEA;AAAQ;EAAAI,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAAM,CAC7E;AAACC,EAAA,GAFWT,eAAe;AAI5B,MAAMU,WAAW,GAAGA,CAAC;EAAET,QAAQ;EAAEE,SAAS,GAAG;AAAG,CAAC,KAAK;EAAAQ,EAAA;EACpD,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGlB,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAMmB,YAAY,GAAGjB,MAAM,CAAC,IAAI,CAAC;EAEjCD,SAAS,CAAC,MAAM;IACd,MAAMmB,YAAY,GAAGA,CAAA,KAAM;MACzB,IAAI,CAACD,YAAY,CAACE,OAAO,EAAE;MAE3B,MAAMC,IAAI,GAAGH,YAAY,CAACE,OAAO,CAACE,qBAAqB,CAAC,CAAC;MACzD,MAAMC,YAAY,GAAGC,MAAM,CAACC,WAAW;;MAEvC;MACA,IAAIC,QAAQ,GAAG,CAAC;MAChB,IAAIL,IAAI,CAACM,GAAG,IAAIJ,YAAY,GAAG,GAAG,IAAIF,IAAI,CAACO,MAAM,IAAIL,YAAY,GAAG,GAAG,EAAE;QACvE,MAAMM,mBAAmB,GAAGR,IAAI,CAACS,MAAM,GAAGP,YAAY,GAAG,GAAG;QAC5D,MAAMQ,qBAAqB,GAAGR,YAAY,GAAG,GAAG,GAAGF,IAAI,CAACM,GAAG;QAC3DD,QAAQ,GAAGM,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEH,qBAAqB,GAAGF,mBAAmB,CAAC,CAAC;MAClF,CAAC,MAAM,IAAIR,IAAI,CAACM,GAAG,GAAGJ,YAAY,GAAG,GAAG,EAAE;QACxCG,QAAQ,GAAG,CAAC;MACd;MAEAT,iBAAiB,CAACS,QAAQ,CAAC;MAE3BS,OAAO,CAACC,GAAG,CAAC,yBAAyBV,QAAQ,CAACW,OAAO,CAAC,CAAC,CAAC,eAAehB,IAAI,CAACM,GAAG,CAACU,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;;MAE7F;MACA,MAAMC,KAAK,GAAGpB,YAAY,CAACE,OAAO,CAACmB,gBAAgB,CAAC,oBAAoB,CAAC;MACzED,KAAK,CAACE,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;QAC7B,MAAMC,IAAI,GAAGF,IAAI,CAACG,aAAa,CAAC,oBAAoB,CAAC;QACrD,IAAI,CAACD,IAAI,EAAE;;QAEX;QACA,MAAME,QAAQ,GAAGH,KAAK,GAAG,EAAE;QAC3B,MAAMI,OAAO,GAAGJ,KAAK,GAAG,GAAG;QAC3B,MAAMK,QAAQ,GAAGF,QAAQ,GAAInB,QAAQ,IAAIoB,OAAO,GAAGD,QAAQ,CAAE;;QAE7D;QACA,MAAMG,YAAY,GAAGhB,IAAI,CAACiB,GAAG,CAAC,IAAI,EAAEP,KAAK,CAAC;QAC1C,MAAMQ,YAAY,GAAGF,YAAY,GAAItB,QAAQ,IAAI,CAAC,GAAGsB,YAAY,CAAE;;QAEnE;QACA,MAAMG,QAAQ,GAAG,CAAC,CAAC,GAAGzB,QAAQ,IAAIgB,KAAK,GAAG,CAAC;;QAE3C;QACA,MAAMU,OAAO,GAAG,GAAG,GAAI1B,QAAQ,GAAG,GAAI,IAAIgB,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;QAEhEP,OAAO,CAACC,GAAG,CAAC,QAAQM,KAAK,OAAOK,QAAQ,CAACV,OAAO,CAAC,CAAC,CAAC,WAAWa,YAAY,CAACb,OAAO,CAAC,CAAC,CAAC,cAAcX,QAAQ,CAACW,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;;QAEzH;QACAI,IAAI,CAACY,KAAK,CAACC,SAAS,GAAG,oCAAoCP,QAAQ,aAAaG,YAAY,aAAaC,QAAQ,MAAM;QACvHV,IAAI,CAACY,KAAK,CAACD,OAAO,GAAGpB,IAAI,CAACE,GAAG,CAACkB,OAAO,EAAE,CAAC,CAAC;QACzCX,IAAI,CAACY,KAAK,CAACE,MAAM,GAAGjB,KAAK,CAACkB,MAAM,GAAGd,KAAK;MAC1C,CAAC,CAAC;IACJ,CAAC;IAEDlB,MAAM,CAACiC,gBAAgB,CAAC,QAAQ,EAAEtC,YAAY,EAAE;MAAEuC,OAAO,EAAE;IAAK,CAAC,CAAC;IAClEvC,YAAY,CAAC,CAAC;IAEd,OAAO,MAAMK,MAAM,CAACmC,mBAAmB,CAAC,QAAQ,EAAExC,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEhB,OAAA;IACEyD,GAAG,EAAE1C,YAAa;IAClBX,SAAS,EAAE,gBAAgBA,SAAS,EAAG;IAAAF,QAAA,eAEvCF,OAAA;MAAKI,SAAS,EAAC,wBAAwB;MAAAF,QAAA,EACpCP,KAAK,CAAC+D,QAAQ,CAACC,GAAG,CAACzD,QAAQ,EAAE,CAAC0D,KAAK,EAAErB,KAAK,kBACzCvC,OAAA;QAAiBI,SAAS,EAAC,mBAAmB;QAAAF,QAAA,EAC3C0D;MAAK,GADErB,KAAK;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEV,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACG,EAAA,CA3EID,WAAW;AAAAkD,GAAA,GAAXlD,WAAW;AA6EjB,eAAeA,WAAW;AAAC,IAAAD,EAAA,EAAAmD,GAAA;AAAAC,YAAA,CAAApD,EAAA;AAAAoD,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}