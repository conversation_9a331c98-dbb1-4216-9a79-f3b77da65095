{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ScrollStack.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport './ScrollStack.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ScrollStack = ({\n  children,\n  className = \"\",\n  style = {},\n  stackOffset = 20,\n  stackScale = 0.95,\n  threshold = 0.1\n}) => {\n  _s();\n  const [scrollProgress, setScrollProgress] = useState(0);\n  const containerRef = useRef(null);\n  useEffect(() => {\n    const handleScroll = () => {\n      if (!containerRef.current) return;\n      const rect = containerRef.current.getBoundingClientRect();\n      const windowHeight = window.innerHeight;\n      const containerTop = rect.top;\n      const containerHeight = rect.height;\n\n      // Calculate scroll progress based on container position\n      const startPoint = windowHeight * 0.8; // Start animation when 80% in view\n      const endPoint = -containerHeight * 0.2; // End when 20% out of view\n\n      let progress = 0;\n      if (containerTop <= startPoint && containerTop >= endPoint) {\n        progress = (startPoint - containerTop) / (startPoint - endPoint);\n        progress = Math.max(0, Math.min(1, progress));\n      } else if (containerTop < endPoint) {\n        progress = 1;\n      }\n      setScrollProgress(progress);\n    };\n    window.addEventListener('scroll', handleScroll, {\n      passive: true\n    });\n    handleScroll();\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n  const getStackStyle = (index, total) => {\n    // Initial stacked position (all cards start stacked)\n    const initialOffset = stackOffset * index;\n    const initialScale = Math.pow(stackScale, index);\n\n    // Final position (cards spread out)\n    const finalOffset = index * 100; // Spread cards vertically\n    const finalScale = 1;\n\n    // Interpolate between initial and final states\n    const currentOffset = initialOffset + scrollProgress * (finalOffset - initialOffset);\n    const currentScale = initialScale + scrollProgress * (finalScale - initialScale);\n\n    // Opacity based on scroll progress and index\n    let opacity = 1;\n    if (scrollProgress < 0.1) {\n      // Initially, only show first few cards\n      opacity = index < 2 ? 1 - index * 0.3 : 0.1;\n    } else {\n      // As we scroll, reveal more cards\n      const revealThreshold = index * 0.15;\n      opacity = scrollProgress > revealThreshold ? 1 : 0.3;\n    }\n    return {\n      transform: `\n        translate(-50%, -50%)\n        translateY(${currentOffset}px)\n        scale(${currentScale})\n        rotateX(${(1 - scrollProgress) * index * 5}deg)\n      `,\n      zIndex: total - index,\n      opacity: opacity,\n      transition: 'none',\n      // Remove transitions for smooth scroll-based animation\n      transformOrigin: 'center center'\n    };\n  };\n\n  // Convert children to array if it's not already\n  const childrenArray = React.Children.toArray(children);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: containerRef,\n    className: `scroll-stack ${className}`,\n    style: style,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"scroll-stack-container\",\n      children: childrenArray.map((child, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"scroll-stack-item\",\n        style: getStackStyle(index, childrenArray.length),\n        children: child\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 87,\n    columnNumber: 5\n  }, this);\n};\n_s(ScrollStack, \"s1hDQaLO+Y+X3sQDuGrxH3NJatY=\");\n_c = ScrollStack;\nexport default ScrollStack;\nvar _c;\n$RefreshReg$(_c, \"ScrollStack\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "ScrollStack", "children", "className", "style", "stackOffset", "stackScale", "threshold", "_s", "scrollProgress", "setScrollProgress", "containerRef", "handleScroll", "current", "rect", "getBoundingClientRect", "windowHeight", "window", "innerHeight", "containerTop", "top", "containerHeight", "height", "startPoint", "endPoint", "progress", "Math", "max", "min", "addEventListener", "passive", "removeEventListener", "getStackStyle", "index", "total", "initialOffset", "initialScale", "pow", "finalOffset", "finalScale", "currentOffset", "currentScale", "opacity", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transform", "zIndex", "transition", "transform<PERSON><PERSON>in", "childrenA<PERSON>y", "Children", "toArray", "ref", "map", "child", "length", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ScrollStack.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport './ScrollStack.css';\n\nconst ScrollStack = ({\n  children,\n  className = \"\",\n  style = {},\n  stackOffset = 20,\n  stackScale = 0.95,\n  threshold = 0.1\n}) => {\n  const [scrollProgress, setScrollProgress] = useState(0);\n  const containerRef = useRef(null);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      if (!containerRef.current) return;\n\n      const rect = containerRef.current.getBoundingClientRect();\n      const windowHeight = window.innerHeight;\n      const containerTop = rect.top;\n      const containerHeight = rect.height;\n\n      // Calculate scroll progress based on container position\n      const startPoint = windowHeight * 0.8; // Start animation when 80% in view\n      const endPoint = -containerHeight * 0.2; // End when 20% out of view\n\n      let progress = 0;\n      if (containerTop <= startPoint && containerTop >= endPoint) {\n        progress = (startPoint - containerTop) / (startPoint - endPoint);\n        progress = Math.max(0, Math.min(1, progress));\n      } else if (containerTop < endPoint) {\n        progress = 1;\n      }\n\n      setScrollProgress(progress);\n    };\n\n    window.addEventListener('scroll', handleScroll, { passive: true });\n    handleScroll();\n\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const getStackStyle = (index, total) => {\n    // Initial stacked position (all cards start stacked)\n    const initialOffset = stackOffset * index;\n    const initialScale = Math.pow(stackScale, index);\n\n    // Final position (cards spread out)\n    const finalOffset = index * 100; // Spread cards vertically\n    const finalScale = 1;\n\n    // Interpolate between initial and final states\n    const currentOffset = initialOffset + (scrollProgress * (finalOffset - initialOffset));\n    const currentScale = initialScale + (scrollProgress * (finalScale - initialScale));\n\n    // Opacity based on scroll progress and index\n    let opacity = 1;\n    if (scrollProgress < 0.1) {\n      // Initially, only show first few cards\n      opacity = index < 2 ? 1 - (index * 0.3) : 0.1;\n    } else {\n      // As we scroll, reveal more cards\n      const revealThreshold = index * 0.15;\n      opacity = scrollProgress > revealThreshold ? 1 : 0.3;\n    }\n\n    return {\n      transform: `\n        translate(-50%, -50%)\n        translateY(${currentOffset}px)\n        scale(${currentScale})\n        rotateX(${(1 - scrollProgress) * index * 5}deg)\n      `,\n      zIndex: total - index,\n      opacity: opacity,\n      transition: 'none', // Remove transitions for smooth scroll-based animation\n      transformOrigin: 'center center'\n    };\n  };\n\n  // Convert children to array if it's not already\n  const childrenArray = React.Children.toArray(children);\n\n  return (\n    <div\n      ref={containerRef}\n      className={`scroll-stack ${className}`}\n      style={style}\n    >\n      <div className=\"scroll-stack-container\">\n        {childrenArray.map((child, index) => (\n          <div\n            key={index}\n            className=\"scroll-stack-item\"\n            style={getStackStyle(index, childrenArray.length)}\n          >\n            {child}\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n};\n\nexport default ScrollStack;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,WAAW,GAAGA,CAAC;EACnBC,QAAQ;EACRC,SAAS,GAAG,EAAE;EACdC,KAAK,GAAG,CAAC,CAAC;EACVC,WAAW,GAAG,EAAE;EAChBC,UAAU,GAAG,IAAI;EACjBC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGd,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAMe,YAAY,GAAGb,MAAM,CAAC,IAAI,CAAC;EAEjCD,SAAS,CAAC,MAAM;IACd,MAAMe,YAAY,GAAGA,CAAA,KAAM;MACzB,IAAI,CAACD,YAAY,CAACE,OAAO,EAAE;MAE3B,MAAMC,IAAI,GAAGH,YAAY,CAACE,OAAO,CAACE,qBAAqB,CAAC,CAAC;MACzD,MAAMC,YAAY,GAAGC,MAAM,CAACC,WAAW;MACvC,MAAMC,YAAY,GAAGL,IAAI,CAACM,GAAG;MAC7B,MAAMC,eAAe,GAAGP,IAAI,CAACQ,MAAM;;MAEnC;MACA,MAAMC,UAAU,GAAGP,YAAY,GAAG,GAAG,CAAC,CAAC;MACvC,MAAMQ,QAAQ,GAAG,CAACH,eAAe,GAAG,GAAG,CAAC,CAAC;;MAEzC,IAAII,QAAQ,GAAG,CAAC;MAChB,IAAIN,YAAY,IAAII,UAAU,IAAIJ,YAAY,IAAIK,QAAQ,EAAE;QAC1DC,QAAQ,GAAG,CAACF,UAAU,GAAGJ,YAAY,KAAKI,UAAU,GAAGC,QAAQ,CAAC;QAChEC,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEH,QAAQ,CAAC,CAAC;MAC/C,CAAC,MAAM,IAAIN,YAAY,GAAGK,QAAQ,EAAE;QAClCC,QAAQ,GAAG,CAAC;MACd;MAEAf,iBAAiB,CAACe,QAAQ,CAAC;IAC7B,CAAC;IAEDR,MAAM,CAACY,gBAAgB,CAAC,QAAQ,EAAEjB,YAAY,EAAE;MAAEkB,OAAO,EAAE;IAAK,CAAC,CAAC;IAClElB,YAAY,CAAC,CAAC;IAEd,OAAO,MAAMK,MAAM,CAACc,mBAAmB,CAAC,QAAQ,EAAEnB,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMoB,aAAa,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACtC;IACA,MAAMC,aAAa,GAAG9B,WAAW,GAAG4B,KAAK;IACzC,MAAMG,YAAY,GAAGV,IAAI,CAACW,GAAG,CAAC/B,UAAU,EAAE2B,KAAK,CAAC;;IAEhD;IACA,MAAMK,WAAW,GAAGL,KAAK,GAAG,GAAG,CAAC,CAAC;IACjC,MAAMM,UAAU,GAAG,CAAC;;IAEpB;IACA,MAAMC,aAAa,GAAGL,aAAa,GAAI1B,cAAc,IAAI6B,WAAW,GAAGH,aAAa,CAAE;IACtF,MAAMM,YAAY,GAAGL,YAAY,GAAI3B,cAAc,IAAI8B,UAAU,GAAGH,YAAY,CAAE;;IAElF;IACA,IAAIM,OAAO,GAAG,CAAC;IACf,IAAIjC,cAAc,GAAG,GAAG,EAAE;MACxB;MACAiC,OAAO,GAAGT,KAAK,GAAG,CAAC,GAAG,CAAC,GAAIA,KAAK,GAAG,GAAI,GAAG,GAAG;IAC/C,CAAC,MAAM;MACL;MACA,MAAMU,eAAe,GAAGV,KAAK,GAAG,IAAI;MACpCS,OAAO,GAAGjC,cAAc,GAAGkC,eAAe,GAAG,CAAC,GAAG,GAAG;IACtD;IAEA,OAAO;MACLC,SAAS,EAAE;AACjB;AACA,qBAAqBJ,aAAa;AAClC,gBAAgBC,YAAY;AAC5B,kBAAkB,CAAC,CAAC,GAAGhC,cAAc,IAAIwB,KAAK,GAAG,CAAC;AAClD,OAAO;MACDY,MAAM,EAAEX,KAAK,GAAGD,KAAK;MACrBS,OAAO,EAAEA,OAAO;MAChBI,UAAU,EAAE,MAAM;MAAE;MACpBC,eAAe,EAAE;IACnB,CAAC;EACH,CAAC;;EAED;EACA,MAAMC,aAAa,GAAGrD,KAAK,CAACsD,QAAQ,CAACC,OAAO,CAAChD,QAAQ,CAAC;EAEtD,oBACEF,OAAA;IACEmD,GAAG,EAAExC,YAAa;IAClBR,SAAS,EAAE,gBAAgBA,SAAS,EAAG;IACvCC,KAAK,EAAEA,KAAM;IAAAF,QAAA,eAEbF,OAAA;MAAKG,SAAS,EAAC,wBAAwB;MAAAD,QAAA,EACpC8C,aAAa,CAACI,GAAG,CAAC,CAACC,KAAK,EAAEpB,KAAK,kBAC9BjC,OAAA;QAEEG,SAAS,EAAC,mBAAmB;QAC7BC,KAAK,EAAE4B,aAAa,CAACC,KAAK,EAAEe,aAAa,CAACM,MAAM,CAAE;QAAApD,QAAA,EAEjDmD;MAAK,GAJDpB,KAAK;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKP,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClD,EAAA,CArGIP,WAAW;AAAA0D,EAAA,GAAX1D,WAAW;AAuGjB,eAAeA,WAAW;AAAC,IAAA0D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}