{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ScrollStack.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport './ScrollStack.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ScrollStack = ({\n  children,\n  className = \"\",\n  style = {},\n  stackOffset = 20,\n  stackScale = 0.95,\n  threshold = 0.1\n}) => {\n  _s();\n  const [scrollProgress, setScrollProgress] = useState(0);\n  const containerRef = useRef(null);\n  useEffect(() => {\n    const handleScroll = () => {\n      if (!containerRef.current) return;\n      const rect = containerRef.current.getBoundingClientRect();\n      const windowHeight = window.innerHeight;\n      const containerTop = rect.top;\n      const containerHeight = rect.height;\n\n      // Calculate scroll progress - simpler approach\n      // Start animation when container is 80% visible\n      // End animation when container is 20% out of view\n      const startTrigger = windowHeight * 0.8;\n      const endTrigger = -containerHeight * 0.2;\n      let progress = 0;\n      if (containerTop <= startTrigger && containerTop >= endTrigger) {\n        // Container is in the animation range\n        const totalDistance = startTrigger - endTrigger;\n        const currentDistance = startTrigger - containerTop;\n        progress = currentDistance / totalDistance;\n        progress = Math.max(0, Math.min(1, progress));\n      } else if (containerTop < endTrigger) {\n        // Container has passed the end trigger\n        progress = 1;\n      }\n      // If containerTop > startTrigger, progress remains 0\n\n      setScrollProgress(progress);\n\n      // Debug log\n      console.log(`ScrollStack: containerTop=${containerTop.toFixed(0)}, progress=${progress.toFixed(2)}`);\n    };\n    window.addEventListener('scroll', handleScroll, {\n      passive: true\n    });\n    handleScroll();\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n  const getStackStyle = (index, total) => {\n    // Base stacking values when progress = 0\n    const initialOffset = index * stackOffset; // Cards stacked with small offset\n    const initialScale = Math.pow(stackScale, index); // Each card slightly smaller\n\n    // Final values when progress = 1\n    const finalOffset = index * 150; // Cards spread out vertically\n    const finalScale = 1; // All cards full size\n\n    // Interpolate between initial and final states\n    const currentOffset = initialOffset + scrollProgress * (finalOffset - initialOffset);\n    const currentScale = initialScale + scrollProgress * (finalScale - initialScale);\n\n    // Opacity - start visible, stay visible\n    const opacity = Math.max(0.7, 1 - index * 0.1 + scrollProgress * 0.3);\n\n    // 3D rotation effect\n    const rotationX = (1 - scrollProgress) * index * 5;\n    return {\n      transform: `\n        translate(-50%, -50%)\n        translateY(${currentOffset}px)\n        scale(${currentScale})\n        rotateX(${rotationX}deg)\n        translateZ(${-index * 15}px)\n      `,\n      zIndex: total - index,\n      opacity: opacity,\n      transformOrigin: 'center center'\n    };\n  };\n\n  // Convert children to array if it's not already\n  const childrenArray = React.Children.toArray(children);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: containerRef,\n    className: `scroll-stack ${className}`,\n    style: style,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"scroll-stack-container\",\n      children: childrenArray.map((child, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"scroll-stack-item\",\n        style: getStackStyle(index, childrenArray.length),\n        children: child\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 93,\n    columnNumber: 5\n  }, this);\n};\n_s(ScrollStack, \"s1hDQaLO+Y+X3sQDuGrxH3NJatY=\");\n_c = ScrollStack;\nexport default ScrollStack;\nvar _c;\n$RefreshReg$(_c, \"ScrollStack\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "ScrollStack", "children", "className", "style", "stackOffset", "stackScale", "threshold", "_s", "scrollProgress", "setScrollProgress", "containerRef", "handleScroll", "current", "rect", "getBoundingClientRect", "windowHeight", "window", "innerHeight", "containerTop", "top", "containerHeight", "height", "startTrigger", "endTrigger", "progress", "totalDistance", "currentDistance", "Math", "max", "min", "console", "log", "toFixed", "addEventListener", "passive", "removeEventListener", "getStackStyle", "index", "total", "initialOffset", "initialScale", "pow", "finalOffset", "finalScale", "currentOffset", "currentScale", "opacity", "rotationX", "transform", "zIndex", "transform<PERSON><PERSON>in", "childrenA<PERSON>y", "Children", "toArray", "ref", "map", "child", "length", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ScrollStack.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport './ScrollStack.css';\n\nconst ScrollStack = ({\n  children,\n  className = \"\",\n  style = {},\n  stackOffset = 20,\n  stackScale = 0.95,\n  threshold = 0.1\n}) => {\n  const [scrollProgress, setScrollProgress] = useState(0);\n  const containerRef = useRef(null);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      if (!containerRef.current) return;\n\n      const rect = containerRef.current.getBoundingClientRect();\n      const windowHeight = window.innerHeight;\n      const containerTop = rect.top;\n      const containerHeight = rect.height;\n\n      // Calculate scroll progress - simpler approach\n      // Start animation when container is 80% visible\n      // End animation when container is 20% out of view\n      const startTrigger = windowHeight * 0.8;\n      const endTrigger = -containerHeight * 0.2;\n\n      let progress = 0;\n\n      if (containerTop <= startTrigger && containerTop >= endTrigger) {\n        // Container is in the animation range\n        const totalDistance = startTrigger - endTrigger;\n        const currentDistance = startTrigger - containerTop;\n        progress = currentDistance / totalDistance;\n        progress = Math.max(0, Math.min(1, progress));\n      } else if (containerTop < endTrigger) {\n        // Container has passed the end trigger\n        progress = 1;\n      }\n      // If containerTop > startTrigger, progress remains 0\n\n      setScrollProgress(progress);\n\n      // Debug log\n      console.log(`ScrollStack: containerTop=${containerTop.toFixed(0)}, progress=${progress.toFixed(2)}`);\n    };\n\n    window.addEventListener('scroll', handleScroll, { passive: true });\n    handleScroll();\n\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const getStackStyle = (index, total) => {\n    // Base stacking values when progress = 0\n    const initialOffset = index * stackOffset; // Cards stacked with small offset\n    const initialScale = Math.pow(stackScale, index); // Each card slightly smaller\n\n    // Final values when progress = 1\n    const finalOffset = index * 150; // Cards spread out vertically\n    const finalScale = 1; // All cards full size\n\n    // Interpolate between initial and final states\n    const currentOffset = initialOffset + (scrollProgress * (finalOffset - initialOffset));\n    const currentScale = initialScale + (scrollProgress * (finalScale - initialScale));\n\n    // Opacity - start visible, stay visible\n    const opacity = Math.max(0.7, 1 - (index * 0.1) + (scrollProgress * 0.3));\n\n    // 3D rotation effect\n    const rotationX = (1 - scrollProgress) * index * 5;\n\n    return {\n      transform: `\n        translate(-50%, -50%)\n        translateY(${currentOffset}px)\n        scale(${currentScale})\n        rotateX(${rotationX}deg)\n        translateZ(${-index * 15}px)\n      `,\n      zIndex: total - index,\n      opacity: opacity,\n      transformOrigin: 'center center'\n    };\n  };\n\n  // Convert children to array if it's not already\n  const childrenArray = React.Children.toArray(children);\n\n  return (\n    <div\n      ref={containerRef}\n      className={`scroll-stack ${className}`}\n      style={style}\n    >\n\n\n      <div className=\"scroll-stack-container\">\n        {childrenArray.map((child, index) => (\n          <div\n            key={index}\n            className=\"scroll-stack-item\"\n            style={getStackStyle(index, childrenArray.length)}\n          >\n            {child}\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n};\n\nexport default ScrollStack;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,WAAW,GAAGA,CAAC;EACnBC,QAAQ;EACRC,SAAS,GAAG,EAAE;EACdC,KAAK,GAAG,CAAC,CAAC;EACVC,WAAW,GAAG,EAAE;EAChBC,UAAU,GAAG,IAAI;EACjBC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGd,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAMe,YAAY,GAAGb,MAAM,CAAC,IAAI,CAAC;EAEjCD,SAAS,CAAC,MAAM;IACd,MAAMe,YAAY,GAAGA,CAAA,KAAM;MACzB,IAAI,CAACD,YAAY,CAACE,OAAO,EAAE;MAE3B,MAAMC,IAAI,GAAGH,YAAY,CAACE,OAAO,CAACE,qBAAqB,CAAC,CAAC;MACzD,MAAMC,YAAY,GAAGC,MAAM,CAACC,WAAW;MACvC,MAAMC,YAAY,GAAGL,IAAI,CAACM,GAAG;MAC7B,MAAMC,eAAe,GAAGP,IAAI,CAACQ,MAAM;;MAEnC;MACA;MACA;MACA,MAAMC,YAAY,GAAGP,YAAY,GAAG,GAAG;MACvC,MAAMQ,UAAU,GAAG,CAACH,eAAe,GAAG,GAAG;MAEzC,IAAII,QAAQ,GAAG,CAAC;MAEhB,IAAIN,YAAY,IAAII,YAAY,IAAIJ,YAAY,IAAIK,UAAU,EAAE;QAC9D;QACA,MAAME,aAAa,GAAGH,YAAY,GAAGC,UAAU;QAC/C,MAAMG,eAAe,GAAGJ,YAAY,GAAGJ,YAAY;QACnDM,QAAQ,GAAGE,eAAe,GAAGD,aAAa;QAC1CD,QAAQ,GAAGG,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEL,QAAQ,CAAC,CAAC;MAC/C,CAAC,MAAM,IAAIN,YAAY,GAAGK,UAAU,EAAE;QACpC;QACAC,QAAQ,GAAG,CAAC;MACd;MACA;;MAEAf,iBAAiB,CAACe,QAAQ,CAAC;;MAE3B;MACAM,OAAO,CAACC,GAAG,CAAC,6BAA6Bb,YAAY,CAACc,OAAO,CAAC,CAAC,CAAC,cAAcR,QAAQ,CAACQ,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;IACtG,CAAC;IAEDhB,MAAM,CAACiB,gBAAgB,CAAC,QAAQ,EAAEtB,YAAY,EAAE;MAAEuB,OAAO,EAAE;IAAK,CAAC,CAAC;IAClEvB,YAAY,CAAC,CAAC;IAEd,OAAO,MAAMK,MAAM,CAACmB,mBAAmB,CAAC,QAAQ,EAAExB,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMyB,aAAa,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACtC;IACA,MAAMC,aAAa,GAAGF,KAAK,GAAGjC,WAAW,CAAC,CAAC;IAC3C,MAAMoC,YAAY,GAAGb,IAAI,CAACc,GAAG,CAACpC,UAAU,EAAEgC,KAAK,CAAC,CAAC,CAAC;;IAElD;IACA,MAAMK,WAAW,GAAGL,KAAK,GAAG,GAAG,CAAC,CAAC;IACjC,MAAMM,UAAU,GAAG,CAAC,CAAC,CAAC;;IAEtB;IACA,MAAMC,aAAa,GAAGL,aAAa,GAAI/B,cAAc,IAAIkC,WAAW,GAAGH,aAAa,CAAE;IACtF,MAAMM,YAAY,GAAGL,YAAY,GAAIhC,cAAc,IAAImC,UAAU,GAAGH,YAAY,CAAE;;IAElF;IACA,MAAMM,OAAO,GAAGnB,IAAI,CAACC,GAAG,CAAC,GAAG,EAAE,CAAC,GAAIS,KAAK,GAAG,GAAI,GAAI7B,cAAc,GAAG,GAAI,CAAC;;IAEzE;IACA,MAAMuC,SAAS,GAAG,CAAC,CAAC,GAAGvC,cAAc,IAAI6B,KAAK,GAAG,CAAC;IAElD,OAAO;MACLW,SAAS,EAAE;AACjB;AACA,qBAAqBJ,aAAa;AAClC,gBAAgBC,YAAY;AAC5B,kBAAkBE,SAAS;AAC3B,qBAAqB,CAACV,KAAK,GAAG,EAAE;AAChC,OAAO;MACDY,MAAM,EAAEX,KAAK,GAAGD,KAAK;MACrBS,OAAO,EAAEA,OAAO;MAChBI,eAAe,EAAE;IACnB,CAAC;EACH,CAAC;;EAED;EACA,MAAMC,aAAa,GAAGzD,KAAK,CAAC0D,QAAQ,CAACC,OAAO,CAACpD,QAAQ,CAAC;EAEtD,oBACEF,OAAA;IACEuD,GAAG,EAAE5C,YAAa;IAClBR,SAAS,EAAE,gBAAgBA,SAAS,EAAG;IACvCC,KAAK,EAAEA,KAAM;IAAAF,QAAA,eAIbF,OAAA;MAAKG,SAAS,EAAC,wBAAwB;MAAAD,QAAA,EACpCkD,aAAa,CAACI,GAAG,CAAC,CAACC,KAAK,EAAEnB,KAAK,kBAC9BtC,OAAA;QAEEG,SAAS,EAAC,mBAAmB;QAC7BC,KAAK,EAAEiC,aAAa,CAACC,KAAK,EAAEc,aAAa,CAACM,MAAM,CAAE;QAAAxD,QAAA,EAEjDuD;MAAK,GAJDnB,KAAK;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKP,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtD,EAAA,CA7GIP,WAAW;AAAA8D,EAAA,GAAX9D,WAAW;AA+GjB,eAAeA,WAAW;AAAC,IAAA8D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}