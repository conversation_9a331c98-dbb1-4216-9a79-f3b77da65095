{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/TextPressure.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport './TextPressure.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TextPressure = ({\n  text = \"DevDoses\",\n  className = \"\",\n  style = {},\n  pressureIntensity = 0.4,\n  fontSize = \"4rem\",\n  initialOpacity = 0.1\n}) => {\n  _s();\n  const [mousePosition, setMousePosition] = useState({\n    x: 0,\n    y: 0\n  });\n  const [isHovering, setIsHovering] = useState(false);\n  const textRef = useRef(null);\n  useEffect(() => {\n    const handleMouseMove = e => {\n      if (textRef.current && isHovering) {\n        const rect = textRef.current.getBoundingClientRect();\n        const centerX = rect.left + rect.width / 2;\n        const centerY = rect.top + rect.height / 2;\n        setMousePosition({\n          x: (e.clientX - centerX) / rect.width,\n          y: (e.clientY - centerY) / rect.height\n        });\n      }\n    };\n    document.addEventListener('mousemove', handleMouseMove);\n    return () => document.removeEventListener('mousemove', handleMouseMove);\n  }, [isHovering]);\n  const getLetterStyle = (index, totalLetters) => {\n    const letterPosition = index / totalLetters - 0.5;\n    const distanceFromMouse = Math.abs(letterPosition - mousePosition.x);\n    const pressure = isHovering ? Math.max(0, 1 - distanceFromMouse * 2) : 0;\n\n    // Base opacity when not hovering\n    const baseOpacity = isHovering ? 1 : initialOpacity;\n    const finalOpacity = baseOpacity + pressure * (1 - baseOpacity);\n    return {\n      opacity: finalOpacity,\n      transform: `\n        translateY(${mousePosition.y * pressure * 20}px)\n        scale(${1 + pressure * pressureIntensity})\n      `,\n      textShadow: isHovering ? `0 0 ${pressure * 30}px rgba(255, 255, 255, ${pressure * 0.8})` : 'none',\n      transition: isHovering ? 'all 0.1s ease-out' : 'opacity 0.3s ease-out'\n    };\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: textRef,\n    className: `text-pressure ${className}`,\n    style: {\n      fontSize,\n      ...style\n    },\n    onMouseEnter: () => setIsHovering(true),\n    onMouseLeave: () => setIsHovering(false),\n    children: text.split('').map((letter, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"text-pressure-letter\",\n      style: getLetterStyle(index, text.length),\n      children: letter === ' ' ? '\\u00A0' : letter\n    }, index, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 55,\n    columnNumber: 5\n  }, this);\n};\n_s(TextPressure, \"iMwUf4W/IVkSPjQtyQr3d+LC08Y=\");\n_c = TextPressure;\nexport default TextPressure;\nvar _c;\n$RefreshReg$(_c, \"TextPressure\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "jsxDEV", "_jsxDEV", "TextPressure", "text", "className", "style", "pressureIntensity", "fontSize", "initialOpacity", "_s", "mousePosition", "setMousePosition", "x", "y", "isHovering", "setIsHovering", "textRef", "handleMouseMove", "e", "current", "rect", "getBoundingClientRect", "centerX", "left", "width", "centerY", "top", "height", "clientX", "clientY", "document", "addEventListener", "removeEventListener", "getLetterStyle", "index", "totalLetters", "letterPosition", "distanceFromMouse", "Math", "abs", "pressure", "max", "baseOpacity", "finalOpacity", "opacity", "transform", "textShadow", "transition", "ref", "onMouseEnter", "onMouseLeave", "children", "split", "map", "letter", "length", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/TextPressure.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport './TextPressure.css';\n\nconst TextPressure = ({\n  text = \"DevDoses\",\n  className = \"\",\n  style = {},\n  pressureIntensity = 0.4,\n  fontSize = \"4rem\",\n  initialOpacity = 0.1\n}) => {\n  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });\n  const [isHovering, setIsHovering] = useState(false);\n  const textRef = useRef(null);\n\n  useEffect(() => {\n    const handleMouseMove = (e) => {\n      if (textRef.current && isHovering) {\n        const rect = textRef.current.getBoundingClientRect();\n        const centerX = rect.left + rect.width / 2;\n        const centerY = rect.top + rect.height / 2;\n\n        setMousePosition({\n          x: (e.clientX - centerX) / rect.width,\n          y: (e.clientY - centerY) / rect.height\n        });\n      }\n    };\n\n    document.addEventListener('mousemove', handleMouseMove);\n    return () => document.removeEventListener('mousemove', handleMouseMove);\n  }, [isHovering]);\n\n  const getLetterStyle = (index, totalLetters) => {\n    const letterPosition = (index / totalLetters) - 0.5;\n    const distanceFromMouse = Math.abs(letterPosition - mousePosition.x);\n    const pressure = isHovering ? Math.max(0, 1 - distanceFromMouse * 2) : 0;\n\n    // Base opacity when not hovering\n    const baseOpacity = isHovering ? 1 : initialOpacity;\n    const finalOpacity = baseOpacity + (pressure * (1 - baseOpacity));\n\n    return {\n      opacity: finalOpacity,\n      transform: `\n        translateY(${mousePosition.y * pressure * 20}px)\n        scale(${1 + pressure * pressureIntensity})\n      `,\n      textShadow: isHovering ? `0 0 ${pressure * 30}px rgba(255, 255, 255, ${pressure * 0.8})` : 'none',\n      transition: isHovering ? 'all 0.1s ease-out' : 'opacity 0.3s ease-out'\n    };\n  };\n\n  return (\n    <div\n      ref={textRef}\n      className={`text-pressure ${className}`}\n      style={{ fontSize, ...style }}\n      onMouseEnter={() => setIsHovering(true)}\n      onMouseLeave={() => setIsHovering(false)}\n    >\n      {text.split('').map((letter, index) => (\n        <span\n          key={index}\n          className=\"text-pressure-letter\"\n          style={getLetterStyle(index, text.length)}\n        >\n          {letter === ' ' ? '\\u00A0' : letter}\n        </span>\n      ))}\n    </div>\n  );\n};\n\nexport default TextPressure;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAMC,YAAY,GAAGA,CAAC;EACpBC,IAAI,GAAG,UAAU;EACjBC,SAAS,GAAG,EAAE;EACdC,KAAK,GAAG,CAAC,CAAC;EACVC,iBAAiB,GAAG,GAAG;EACvBC,QAAQ,GAAG,MAAM;EACjBC,cAAc,GAAG;AACnB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGd,QAAQ,CAAC;IAAEe,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAClE,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMmB,OAAO,GAAGlB,MAAM,CAAC,IAAI,CAAC;EAE5BC,SAAS,CAAC,MAAM;IACd,MAAMkB,eAAe,GAAIC,CAAC,IAAK;MAC7B,IAAIF,OAAO,CAACG,OAAO,IAAIL,UAAU,EAAE;QACjC,MAAMM,IAAI,GAAGJ,OAAO,CAACG,OAAO,CAACE,qBAAqB,CAAC,CAAC;QACpD,MAAMC,OAAO,GAAGF,IAAI,CAACG,IAAI,GAAGH,IAAI,CAACI,KAAK,GAAG,CAAC;QAC1C,MAAMC,OAAO,GAAGL,IAAI,CAACM,GAAG,GAAGN,IAAI,CAACO,MAAM,GAAG,CAAC;QAE1ChB,gBAAgB,CAAC;UACfC,CAAC,EAAE,CAACM,CAAC,CAACU,OAAO,GAAGN,OAAO,IAAIF,IAAI,CAACI,KAAK;UACrCX,CAAC,EAAE,CAACK,CAAC,CAACW,OAAO,GAAGJ,OAAO,IAAIL,IAAI,CAACO;QAClC,CAAC,CAAC;MACJ;IACF,CAAC;IAEDG,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEd,eAAe,CAAC;IACvD,OAAO,MAAMa,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEf,eAAe,CAAC;EACzE,CAAC,EAAE,CAACH,UAAU,CAAC,CAAC;EAEhB,MAAMmB,cAAc,GAAGA,CAACC,KAAK,EAAEC,YAAY,KAAK;IAC9C,MAAMC,cAAc,GAAIF,KAAK,GAAGC,YAAY,GAAI,GAAG;IACnD,MAAME,iBAAiB,GAAGC,IAAI,CAACC,GAAG,CAACH,cAAc,GAAG1B,aAAa,CAACE,CAAC,CAAC;IACpE,MAAM4B,QAAQ,GAAG1B,UAAU,GAAGwB,IAAI,CAACG,GAAG,CAAC,CAAC,EAAE,CAAC,GAAGJ,iBAAiB,GAAG,CAAC,CAAC,GAAG,CAAC;;IAExE;IACA,MAAMK,WAAW,GAAG5B,UAAU,GAAG,CAAC,GAAGN,cAAc;IACnD,MAAMmC,YAAY,GAAGD,WAAW,GAAIF,QAAQ,IAAI,CAAC,GAAGE,WAAW,CAAE;IAEjE,OAAO;MACLE,OAAO,EAAED,YAAY;MACrBE,SAAS,EAAE;AACjB,qBAAqBnC,aAAa,CAACG,CAAC,GAAG2B,QAAQ,GAAG,EAAE;AACpD,gBAAgB,CAAC,GAAGA,QAAQ,GAAGlC,iBAAiB;AAChD,OAAO;MACDwC,UAAU,EAAEhC,UAAU,GAAG,OAAO0B,QAAQ,GAAG,EAAE,0BAA0BA,QAAQ,GAAG,GAAG,GAAG,GAAG,MAAM;MACjGO,UAAU,EAAEjC,UAAU,GAAG,mBAAmB,GAAG;IACjD,CAAC;EACH,CAAC;EAED,oBACEb,OAAA;IACE+C,GAAG,EAAEhC,OAAQ;IACbZ,SAAS,EAAE,iBAAiBA,SAAS,EAAG;IACxCC,KAAK,EAAE;MAAEE,QAAQ;MAAE,GAAGF;IAAM,CAAE;IAC9B4C,YAAY,EAAEA,CAAA,KAAMlC,aAAa,CAAC,IAAI,CAAE;IACxCmC,YAAY,EAAEA,CAAA,KAAMnC,aAAa,CAAC,KAAK,CAAE;IAAAoC,QAAA,EAExChD,IAAI,CAACiD,KAAK,CAAC,EAAE,CAAC,CAACC,GAAG,CAAC,CAACC,MAAM,EAAEpB,KAAK,kBAChCjC,OAAA;MAEEG,SAAS,EAAC,sBAAsB;MAChCC,KAAK,EAAE4B,cAAc,CAACC,KAAK,EAAE/B,IAAI,CAACoD,MAAM,CAAE;MAAAJ,QAAA,EAEzCG,MAAM,KAAK,GAAG,GAAG,QAAQ,GAAGA;IAAM,GAJ9BpB,KAAK;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAKN,CACP;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAClD,EAAA,CArEIP,YAAY;AAAA0D,EAAA,GAAZ1D,YAAY;AAuElB,eAAeA,YAAY;AAAC,IAAA0D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}