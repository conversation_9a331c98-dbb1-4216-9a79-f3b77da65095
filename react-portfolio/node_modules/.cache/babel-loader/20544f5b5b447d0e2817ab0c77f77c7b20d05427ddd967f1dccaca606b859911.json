{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Ribbons.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport './Ribbons.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Ribbons = ({\n  children,\n  className = \"\",\n  style = {},\n  ribbonColor = \"rgba(78, 205, 196, 0.2)\",\n  ribbonWidth = 2,\n  ribbonSpeed = 2000\n}) => {\n  _s();\n  const [mousePosition, setMousePosition] = useState({\n    x: 0,\n    y: 0\n  });\n  const [isHovering, setIsHovering] = useState(false);\n  const containerRef = useRef(null);\n  useEffect(() => {\n    if (!canvasRef.current) return;\n    const canvas = canvasRef.current;\n    const renderer = new Renderer({\n      canvas,\n      alpha: true\n    });\n    const gl = renderer.gl;\n\n    // Set canvas size\n    const resize = () => {\n      const rect = canvas.parentElement.getBoundingClientRect();\n      renderer.setSize(rect.width, rect.height);\n    };\n    resize();\n    window.addEventListener('resize', resize);\n    const camera = new Camera(gl);\n    camera.position.z = 1;\n    const scene = new Transform();\n\n    // Vertex shader\n    const vertex = `\n      attribute vec2 uv;\n      attribute vec2 position;\n      uniform mat4 modelViewMatrix;\n      uniform mat4 projectionMatrix;\n      uniform float uTime;\n      varying vec2 vUv;\n\n      void main() {\n        vUv = uv;\n        vec3 pos = vec3(position, 0.0);\n\n        // Add wave animation\n        pos.y += sin(pos.x * 3.0 + uTime * 0.001) * 0.1;\n        pos.x += cos(pos.y * 2.0 + uTime * 0.0015) * 0.05;\n\n        gl_Position = projectionMatrix * modelViewMatrix * vec4(pos, 1.0);\n      }\n    `;\n\n    // Fragment shader\n    const fragment = `\n      precision highp float;\n      uniform float uTime;\n      uniform vec3 uColor;\n      uniform vec2 uResolution;\n      varying vec2 vUv;\n\n      void main() {\n        vec2 uv = vUv;\n\n        // Create flowing ribbon effect\n        float wave1 = sin(uv.x * 10.0 + uTime * 0.002) * 0.5 + 0.5;\n        float wave2 = sin(uv.y * 8.0 + uTime * 0.003) * 0.5 + 0.5;\n        float wave3 = sin((uv.x + uv.y) * 6.0 + uTime * 0.0025) * 0.5 + 0.5;\n\n        float alpha = wave1 * wave2 * wave3 * 0.3;\n\n        // Add gradient\n        alpha *= (1.0 - distance(uv, vec2(0.5)) * 1.5);\n\n        gl_FragColor = vec4(uColor, alpha);\n      }\n    `;\n\n    // Parse color\n    const parseColor = colorStr => {\n      const match = colorStr.match(/rgba?\\(([^)]+)\\)/);\n      if (match) {\n        const values = match[1].split(',').map(v => parseFloat(v.trim()));\n        return [values[0] / 255, values[1] / 255, values[2] / 255];\n      }\n      return [1, 1, 1];\n    };\n    const color = parseColor(ribbonColor);\n\n    // Create ribbons\n    const ribbons = [];\n    for (let i = 0; i < ribbonCount; i++) {\n      const geometry = new Plane(gl, {\n        width: 2,\n        height: 2,\n        widthSegments: 32,\n        heightSegments: 32\n      });\n      const program = new Program(gl, {\n        vertex,\n        fragment,\n        uniforms: {\n          uTime: {\n            value: 0\n          },\n          uColor: {\n            value: color\n          },\n          uResolution: {\n            value: new Vec2(canvas.width, canvas.height)\n          }\n        },\n        transparent: true,\n        cullFace: null\n      });\n      const mesh = new Mesh(gl, {\n        geometry,\n        program\n      });\n      mesh.position.z = -i * 0.1;\n      mesh.setParent(scene);\n      ribbons.push({\n        mesh,\n        program,\n        offset: i * 1000\n      });\n    }\n    rendererRef.current = renderer;\n\n    // Animation loop\n    const animate = time => {\n      ribbons.forEach(({\n        program,\n        offset\n      }) => {\n        program.uniforms.uTime.value = (time + offset) * (ribbonSpeed / 2000);\n      });\n      renderer.render({\n        scene,\n        camera\n      });\n      animationRef.current = requestAnimationFrame(animate);\n    };\n    animate(0);\n    return () => {\n      window.removeEventListener('resize', resize);\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current);\n      }\n      if (rendererRef.current) {\n        var _rendererRef$current$;\n        (_rendererRef$current$ = rendererRef.current.gl.getExtension('WEBGL_lose_context')) === null || _rendererRef$current$ === void 0 ? void 0 : _rendererRef$current$.loseContext();\n      }\n    };\n  }, [ribbonColor, ribbonSpeed, ribbonCount]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `ribbons-container ${className}`,\n    style: style,\n    children: [/*#__PURE__*/_jsxDEV(\"canvas\", {\n      ref: canvasRef,\n      className: \"ribbons-canvas\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ribbons-content\",\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 150,\n    columnNumber: 5\n  }, this);\n};\n_s(Ribbons, \"4l6ztoc+CnxzOJRez4d+AEJzzLU=\");\n_c = Ribbons;\nexport default Ribbons;\nvar _c;\n$RefreshReg$(_c, \"Ribbons\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "jsxDEV", "_jsxDEV", "Ribbons", "children", "className", "style", "ribbonColor", "ribbonWidth", "ribbonSpeed", "_s", "mousePosition", "setMousePosition", "x", "y", "isHovering", "setIsHovering", "containerRef", "canvasRef", "current", "canvas", "renderer", "<PERSON><PERSON><PERSON>", "alpha", "gl", "resize", "rect", "parentElement", "getBoundingClientRect", "setSize", "width", "height", "window", "addEventListener", "camera", "Camera", "position", "z", "scene", "Transform", "vertex", "fragment", "parseColor", "colorStr", "match", "values", "split", "map", "v", "parseFloat", "trim", "color", "ribbons", "i", "ribbonCount", "geometry", "Plane", "widthSegments", "heightSegments", "program", "Program", "uniforms", "uTime", "value", "uColor", "uResolution", "Vec2", "transparent", "cullFace", "mesh", "<PERSON><PERSON>", "setParent", "push", "offset", "rendererRef", "animate", "time", "for<PERSON>ach", "render", "animationRef", "requestAnimationFrame", "removeEventListener", "cancelAnimationFrame", "_rendererRef$current$", "getExtension", "loseContext", "ref", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Ribbons.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport './Ribbons.css';\n\nconst Ribbons = ({\n  children,\n  className = \"\",\n  style = {},\n  ribbonColor = \"rgba(78, 205, 196, 0.2)\",\n  ribbonWidth = 2,\n  ribbonSpeed = 2000\n}) => {\n  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });\n  const [isHovering, setIsHovering] = useState(false);\n  const containerRef = useRef(null);\n\n  useEffect(() => {\n    if (!canvasRef.current) return;\n\n    const canvas = canvasRef.current;\n    const renderer = new Renderer({ canvas, alpha: true });\n    const gl = renderer.gl;\n\n    // Set canvas size\n    const resize = () => {\n      const rect = canvas.parentElement.getBoundingClientRect();\n      renderer.setSize(rect.width, rect.height);\n    };\n\n    resize();\n    window.addEventListener('resize', resize);\n\n    const camera = new Camera(gl);\n    camera.position.z = 1;\n\n    const scene = new Transform();\n\n    // Vertex shader\n    const vertex = `\n      attribute vec2 uv;\n      attribute vec2 position;\n      uniform mat4 modelViewMatrix;\n      uniform mat4 projectionMatrix;\n      uniform float uTime;\n      varying vec2 vUv;\n\n      void main() {\n        vUv = uv;\n        vec3 pos = vec3(position, 0.0);\n\n        // Add wave animation\n        pos.y += sin(pos.x * 3.0 + uTime * 0.001) * 0.1;\n        pos.x += cos(pos.y * 2.0 + uTime * 0.0015) * 0.05;\n\n        gl_Position = projectionMatrix * modelViewMatrix * vec4(pos, 1.0);\n      }\n    `;\n\n    // Fragment shader\n    const fragment = `\n      precision highp float;\n      uniform float uTime;\n      uniform vec3 uColor;\n      uniform vec2 uResolution;\n      varying vec2 vUv;\n\n      void main() {\n        vec2 uv = vUv;\n\n        // Create flowing ribbon effect\n        float wave1 = sin(uv.x * 10.0 + uTime * 0.002) * 0.5 + 0.5;\n        float wave2 = sin(uv.y * 8.0 + uTime * 0.003) * 0.5 + 0.5;\n        float wave3 = sin((uv.x + uv.y) * 6.0 + uTime * 0.0025) * 0.5 + 0.5;\n\n        float alpha = wave1 * wave2 * wave3 * 0.3;\n\n        // Add gradient\n        alpha *= (1.0 - distance(uv, vec2(0.5)) * 1.5);\n\n        gl_FragColor = vec4(uColor, alpha);\n      }\n    `;\n\n    // Parse color\n    const parseColor = (colorStr) => {\n      const match = colorStr.match(/rgba?\\(([^)]+)\\)/);\n      if (match) {\n        const values = match[1].split(',').map(v => parseFloat(v.trim()));\n        return [values[0] / 255, values[1] / 255, values[2] / 255];\n      }\n      return [1, 1, 1];\n    };\n\n    const color = parseColor(ribbonColor);\n\n    // Create ribbons\n    const ribbons = [];\n    for (let i = 0; i < ribbonCount; i++) {\n      const geometry = new Plane(gl, {\n        width: 2,\n        height: 2,\n        widthSegments: 32,\n        heightSegments: 32\n      });\n\n      const program = new Program(gl, {\n        vertex,\n        fragment,\n        uniforms: {\n          uTime: { value: 0 },\n          uColor: { value: color },\n          uResolution: { value: new Vec2(canvas.width, canvas.height) }\n        },\n        transparent: true,\n        cullFace: null\n      });\n\n      const mesh = new Mesh(gl, { geometry, program });\n      mesh.position.z = -i * 0.1;\n      mesh.setParent(scene);\n\n      ribbons.push({ mesh, program, offset: i * 1000 });\n    }\n\n    rendererRef.current = renderer;\n\n    // Animation loop\n    const animate = (time) => {\n      ribbons.forEach(({ program, offset }) => {\n        program.uniforms.uTime.value = (time + offset) * (ribbonSpeed / 2000);\n      });\n\n      renderer.render({ scene, camera });\n      animationRef.current = requestAnimationFrame(animate);\n    };\n\n    animate(0);\n\n    return () => {\n      window.removeEventListener('resize', resize);\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current);\n      }\n      if (rendererRef.current) {\n        rendererRef.current.gl.getExtension('WEBGL_lose_context')?.loseContext();\n      }\n    };\n  }, [ribbonColor, ribbonSpeed, ribbonCount]);\n\n  return (\n    <div className={`ribbons-container ${className}`} style={style}>\n      <canvas\n        ref={canvasRef}\n        className=\"ribbons-canvas\"\n      />\n      <div className=\"ribbons-content\">\n        {children}\n      </div>\n    </div>\n  );\n};\n\nexport default Ribbons;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,OAAO,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvB,MAAMC,OAAO,GAAGA,CAAC;EACfC,QAAQ;EACRC,SAAS,GAAG,EAAE;EACdC,KAAK,GAAG,CAAC,CAAC;EACVC,WAAW,GAAG,yBAAyB;EACvCC,WAAW,GAAG,CAAC;EACfC,WAAW,GAAG;AAChB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGd,QAAQ,CAAC;IAAEe,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAClE,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMmB,YAAY,GAAGlB,MAAM,CAAC,IAAI,CAAC;EAEjCC,SAAS,CAAC,MAAM;IACd,IAAI,CAACkB,SAAS,CAACC,OAAO,EAAE;IAExB,MAAMC,MAAM,GAAGF,SAAS,CAACC,OAAO;IAChC,MAAME,QAAQ,GAAG,IAAIC,QAAQ,CAAC;MAAEF,MAAM;MAAEG,KAAK,EAAE;IAAK,CAAC,CAAC;IACtD,MAAMC,EAAE,GAAGH,QAAQ,CAACG,EAAE;;IAEtB;IACA,MAAMC,MAAM,GAAGA,CAAA,KAAM;MACnB,MAAMC,IAAI,GAAGN,MAAM,CAACO,aAAa,CAACC,qBAAqB,CAAC,CAAC;MACzDP,QAAQ,CAACQ,OAAO,CAACH,IAAI,CAACI,KAAK,EAAEJ,IAAI,CAACK,MAAM,CAAC;IAC3C,CAAC;IAEDN,MAAM,CAAC,CAAC;IACRO,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAER,MAAM,CAAC;IAEzC,MAAMS,MAAM,GAAG,IAAIC,MAAM,CAACX,EAAE,CAAC;IAC7BU,MAAM,CAACE,QAAQ,CAACC,CAAC,GAAG,CAAC;IAErB,MAAMC,KAAK,GAAG,IAAIC,SAAS,CAAC,CAAC;;IAE7B;IACA,MAAMC,MAAM,GAAG;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;IAED;IACA,MAAMC,QAAQ,GAAG;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;IAED;IACA,MAAMC,UAAU,GAAIC,QAAQ,IAAK;MAC/B,MAAMC,KAAK,GAAGD,QAAQ,CAACC,KAAK,CAAC,kBAAkB,CAAC;MAChD,IAAIA,KAAK,EAAE;QACT,MAAMC,MAAM,GAAGD,KAAK,CAAC,CAAC,CAAC,CAACE,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIC,UAAU,CAACD,CAAC,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC;QACjE,OAAO,CAACL,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,EAAEA,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,EAAEA,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;MAC5D;MACA,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAClB,CAAC;IAED,MAAMM,KAAK,GAAGT,UAAU,CAACnC,WAAW,CAAC;;IAErC;IACA,MAAM6C,OAAO,GAAG,EAAE;IAClB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,WAAW,EAAED,CAAC,EAAE,EAAE;MACpC,MAAME,QAAQ,GAAG,IAAIC,KAAK,CAAChC,EAAE,EAAE;QAC7BM,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACT0B,aAAa,EAAE,EAAE;QACjBC,cAAc,EAAE;MAClB,CAAC,CAAC;MAEF,MAAMC,OAAO,GAAG,IAAIC,OAAO,CAACpC,EAAE,EAAE;QAC9BgB,MAAM;QACNC,QAAQ;QACRoB,QAAQ,EAAE;UACRC,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAE,CAAC;UACnBC,MAAM,EAAE;YAAED,KAAK,EAAEZ;UAAM,CAAC;UACxBc,WAAW,EAAE;YAAEF,KAAK,EAAE,IAAIG,IAAI,CAAC9C,MAAM,CAACU,KAAK,EAAEV,MAAM,CAACW,MAAM;UAAE;QAC9D,CAAC;QACDoC,WAAW,EAAE,IAAI;QACjBC,QAAQ,EAAE;MACZ,CAAC,CAAC;MAEF,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC9C,EAAE,EAAE;QAAE+B,QAAQ;QAAEI;MAAQ,CAAC,CAAC;MAChDU,IAAI,CAACjC,QAAQ,CAACC,CAAC,GAAG,CAACgB,CAAC,GAAG,GAAG;MAC1BgB,IAAI,CAACE,SAAS,CAACjC,KAAK,CAAC;MAErBc,OAAO,CAACoB,IAAI,CAAC;QAAEH,IAAI;QAAEV,OAAO;QAAEc,MAAM,EAAEpB,CAAC,GAAG;MAAK,CAAC,CAAC;IACnD;IAEAqB,WAAW,CAACvD,OAAO,GAAGE,QAAQ;;IAE9B;IACA,MAAMsD,OAAO,GAAIC,IAAI,IAAK;MACxBxB,OAAO,CAACyB,OAAO,CAAC,CAAC;QAAElB,OAAO;QAAEc;MAAO,CAAC,KAAK;QACvCd,OAAO,CAACE,QAAQ,CAACC,KAAK,CAACC,KAAK,GAAG,CAACa,IAAI,GAAGH,MAAM,KAAKhE,WAAW,GAAG,IAAI,CAAC;MACvE,CAAC,CAAC;MAEFY,QAAQ,CAACyD,MAAM,CAAC;QAAExC,KAAK;QAAEJ;MAAO,CAAC,CAAC;MAClC6C,YAAY,CAAC5D,OAAO,GAAG6D,qBAAqB,CAACL,OAAO,CAAC;IACvD,CAAC;IAEDA,OAAO,CAAC,CAAC,CAAC;IAEV,OAAO,MAAM;MACX3C,MAAM,CAACiD,mBAAmB,CAAC,QAAQ,EAAExD,MAAM,CAAC;MAC5C,IAAIsD,YAAY,CAAC5D,OAAO,EAAE;QACxB+D,oBAAoB,CAACH,YAAY,CAAC5D,OAAO,CAAC;MAC5C;MACA,IAAIuD,WAAW,CAACvD,OAAO,EAAE;QAAA,IAAAgE,qBAAA;QACvB,CAAAA,qBAAA,GAAAT,WAAW,CAACvD,OAAO,CAACK,EAAE,CAAC4D,YAAY,CAAC,oBAAoB,CAAC,cAAAD,qBAAA,uBAAzDA,qBAAA,CAA2DE,WAAW,CAAC,CAAC;MAC1E;IACF,CAAC;EACH,CAAC,EAAE,CAAC9E,WAAW,EAAEE,WAAW,EAAE6C,WAAW,CAAC,CAAC;EAE3C,oBACEpD,OAAA;IAAKG,SAAS,EAAE,qBAAqBA,SAAS,EAAG;IAACC,KAAK,EAAEA,KAAM;IAAAF,QAAA,gBAC7DF,OAAA;MACEoF,GAAG,EAAEpE,SAAU;MACfb,SAAS,EAAC;IAAgB;MAAAkF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC,eACFxF,OAAA;MAAKG,SAAS,EAAC,iBAAiB;MAAAD,QAAA,EAC7BA;IAAQ;MAAAmF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChF,EAAA,CA5JIP,OAAO;AAAAwF,EAAA,GAAPxF,OAAO;AA8Jb,eAAeA,OAAO;AAAC,IAAAwF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}