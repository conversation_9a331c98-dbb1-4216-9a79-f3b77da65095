{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ScrollStack.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport './ScrollStack.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ScrollStack = ({\n  children,\n  className = \"\",\n  style = {},\n  stackOffset = 20,\n  stackScale = 0.95,\n  threshold = 0.1\n}) => {\n  _s();\n  const [scrollProgress, setScrollProgress] = useState(0);\n  const containerRef = useRef(null);\n  useEffect(() => {\n    const handleScroll = () => {\n      if (!containerRef.current) return;\n      const rect = containerRef.current.getBoundingClientRect();\n      const windowHeight = window.innerHeight;\n      const containerTop = rect.top;\n      const containerHeight = rect.height;\n\n      // Calculate scroll progress - simpler approach\n      // Start animation when container is 80% visible\n      // End animation when container is 20% out of view\n      const startTrigger = windowHeight * 0.8;\n      const endTrigger = -containerHeight * 0.2;\n      let progress = 0;\n      if (containerTop <= startTrigger && containerTop >= endTrigger) {\n        // Container is in the animation range\n        const totalDistance = startTrigger - endTrigger;\n        const currentDistance = startTrigger - containerTop;\n        progress = currentDistance / totalDistance;\n        progress = Math.max(0, Math.min(1, progress));\n      } else if (containerTop < endTrigger) {\n        // Container has passed the end trigger\n        progress = 1;\n      }\n      // If containerTop > startTrigger, progress remains 0\n\n      setScrollProgress(progress);\n\n      // Debug log - only when progress changes significantly\n      if (Math.abs(progress - scrollProgress) > 0.05) {\n        console.log(`ScrollStack: containerTop=${containerTop.toFixed(0)}, progress=${progress.toFixed(2)}, cards=${React.Children.count(children)}`);\n      }\n    };\n    window.addEventListener('scroll', handleScroll, {\n      passive: true\n    });\n    handleScroll();\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n  const getStackStyle = (index, total) => {\n    // Base stacking values when progress = 0\n    const initialOffset = index * stackOffset; // Cards stacked with small offset\n    const initialScale = Math.pow(stackScale, index); // Each card slightly smaller\n\n    // Final values when progress = 1\n    const finalOffset = index * 150; // Cards spread out vertically\n    const finalScale = 1; // All cards full size\n\n    // Interpolate between initial and final states\n    const currentOffset = initialOffset + scrollProgress * (finalOffset - initialOffset);\n    const currentScale = initialScale + scrollProgress * (finalScale - initialScale);\n\n    // Opacity - start visible, stay visible\n    const opacity = Math.max(0.7, 1 - index * 0.1 + scrollProgress * 0.3);\n\n    // 3D rotation effect\n    const rotationX = (1 - scrollProgress) * index * 5;\n    return {\n      transform: `\n        translate(-50%, -50%)\n        translateY(${currentOffset}px)\n        scale(${currentScale})\n        rotateX(${rotationX}deg)\n        translateZ(${-index * 15}px)\n      `,\n      zIndex: total - index,\n      opacity: opacity,\n      transformOrigin: 'center center'\n    };\n  };\n\n  // Convert children to array if it's not already\n  const childrenArray = React.Children.toArray(children);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: containerRef,\n    className: `scroll-stack ${className}`,\n    style: style,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: '20px',\n        right: '20px',\n        background: 'rgba(0,0,0,0.8)',\n        color: 'white',\n        padding: '10px',\n        borderRadius: '5px',\n        fontSize: '14px',\n        zIndex: 10000,\n        fontFamily: 'monospace'\n      },\n      children: [\"ScrollStack Debug:\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 27\n      }, this), \"Progress: \", (scrollProgress * 100).toFixed(0), \"%\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 55\n      }, this), \"Cards: \", childrenArray.length]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"scroll-stack-container\",\n      children: childrenArray.map((child, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"scroll-stack-item\",\n        style: getStackStyle(index, childrenArray.length),\n        children: child\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 95,\n    columnNumber: 5\n  }, this);\n};\n_s(ScrollStack, \"s1hDQaLO+Y+X3sQDuGrxH3NJatY=\");\n_c = ScrollStack;\nexport default ScrollStack;\nvar _c;\n$RefreshReg$(_c, \"ScrollStack\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "ScrollStack", "children", "className", "style", "stackOffset", "stackScale", "threshold", "_s", "scrollProgress", "setScrollProgress", "containerRef", "handleScroll", "current", "rect", "getBoundingClientRect", "windowHeight", "window", "innerHeight", "containerTop", "top", "containerHeight", "height", "startTrigger", "endTrigger", "progress", "totalDistance", "currentDistance", "Math", "max", "min", "abs", "console", "log", "toFixed", "Children", "count", "addEventListener", "passive", "removeEventListener", "getStackStyle", "index", "total", "initialOffset", "initialScale", "pow", "finalOffset", "finalScale", "currentOffset", "currentScale", "opacity", "rotationX", "transform", "zIndex", "transform<PERSON><PERSON>in", "childrenA<PERSON>y", "toArray", "ref", "position", "right", "background", "color", "padding", "borderRadius", "fontSize", "fontFamily", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "map", "child", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ScrollStack.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport './ScrollStack.css';\n\nconst ScrollStack = ({\n  children,\n  className = \"\",\n  style = {},\n  stackOffset = 20,\n  stackScale = 0.95,\n  threshold = 0.1\n}) => {\n  const [scrollProgress, setScrollProgress] = useState(0);\n  const containerRef = useRef(null);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      if (!containerRef.current) return;\n\n      const rect = containerRef.current.getBoundingClientRect();\n      const windowHeight = window.innerHeight;\n      const containerTop = rect.top;\n      const containerHeight = rect.height;\n\n      // Calculate scroll progress - simpler approach\n      // Start animation when container is 80% visible\n      // End animation when container is 20% out of view\n      const startTrigger = windowHeight * 0.8;\n      const endTrigger = -containerHeight * 0.2;\n\n      let progress = 0;\n\n      if (containerTop <= startTrigger && containerTop >= endTrigger) {\n        // Container is in the animation range\n        const totalDistance = startTrigger - endTrigger;\n        const currentDistance = startTrigger - containerTop;\n        progress = currentDistance / totalDistance;\n        progress = Math.max(0, Math.min(1, progress));\n      } else if (containerTop < endTrigger) {\n        // Container has passed the end trigger\n        progress = 1;\n      }\n      // If containerTop > startTrigger, progress remains 0\n\n      setScrollProgress(progress);\n\n      // Debug log - only when progress changes significantly\n      if (Math.abs(progress - scrollProgress) > 0.05) {\n        console.log(`ScrollStack: containerTop=${containerTop.toFixed(0)}, progress=${progress.toFixed(2)}, cards=${React.Children.count(children)}`);\n      }\n    };\n\n    window.addEventListener('scroll', handleScroll, { passive: true });\n    handleScroll();\n\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const getStackStyle = (index, total) => {\n    // Base stacking values when progress = 0\n    const initialOffset = index * stackOffset; // Cards stacked with small offset\n    const initialScale = Math.pow(stackScale, index); // Each card slightly smaller\n\n    // Final values when progress = 1\n    const finalOffset = index * 150; // Cards spread out vertically\n    const finalScale = 1; // All cards full size\n\n    // Interpolate between initial and final states\n    const currentOffset = initialOffset + (scrollProgress * (finalOffset - initialOffset));\n    const currentScale = initialScale + (scrollProgress * (finalScale - initialScale));\n\n    // Opacity - start visible, stay visible\n    const opacity = Math.max(0.7, 1 - (index * 0.1) + (scrollProgress * 0.3));\n\n    // 3D rotation effect\n    const rotationX = (1 - scrollProgress) * index * 5;\n\n    return {\n      transform: `\n        translate(-50%, -50%)\n        translateY(${currentOffset}px)\n        scale(${currentScale})\n        rotateX(${rotationX}deg)\n        translateZ(${-index * 15}px)\n      `,\n      zIndex: total - index,\n      opacity: opacity,\n      transformOrigin: 'center center'\n    };\n  };\n\n  // Convert children to array if it's not already\n  const childrenArray = React.Children.toArray(children);\n\n  return (\n    <div\n      ref={containerRef}\n      className={`scroll-stack ${className}`}\n      style={style}\n    >\n      {/* Debug indicator */}\n      <div style={{\n        position: 'fixed',\n        top: '20px',\n        right: '20px',\n        background: 'rgba(0,0,0,0.8)',\n        color: 'white',\n        padding: '10px',\n        borderRadius: '5px',\n        fontSize: '14px',\n        zIndex: 10000,\n        fontFamily: 'monospace'\n      }}>\n        ScrollStack Debug:<br/>\n        Progress: {(scrollProgress * 100).toFixed(0)}%<br/>\n        Cards: {childrenArray.length}\n      </div>\n\n      <div className=\"scroll-stack-container\">\n        {childrenArray.map((child, index) => (\n          <div\n            key={index}\n            className=\"scroll-stack-item\"\n            style={getStackStyle(index, childrenArray.length)}\n          >\n            {child}\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n};\n\nexport default ScrollStack;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,WAAW,GAAGA,CAAC;EACnBC,QAAQ;EACRC,SAAS,GAAG,EAAE;EACdC,KAAK,GAAG,CAAC,CAAC;EACVC,WAAW,GAAG,EAAE;EAChBC,UAAU,GAAG,IAAI;EACjBC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGd,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAMe,YAAY,GAAGb,MAAM,CAAC,IAAI,CAAC;EAEjCD,SAAS,CAAC,MAAM;IACd,MAAMe,YAAY,GAAGA,CAAA,KAAM;MACzB,IAAI,CAACD,YAAY,CAACE,OAAO,EAAE;MAE3B,MAAMC,IAAI,GAAGH,YAAY,CAACE,OAAO,CAACE,qBAAqB,CAAC,CAAC;MACzD,MAAMC,YAAY,GAAGC,MAAM,CAACC,WAAW;MACvC,MAAMC,YAAY,GAAGL,IAAI,CAACM,GAAG;MAC7B,MAAMC,eAAe,GAAGP,IAAI,CAACQ,MAAM;;MAEnC;MACA;MACA;MACA,MAAMC,YAAY,GAAGP,YAAY,GAAG,GAAG;MACvC,MAAMQ,UAAU,GAAG,CAACH,eAAe,GAAG,GAAG;MAEzC,IAAII,QAAQ,GAAG,CAAC;MAEhB,IAAIN,YAAY,IAAII,YAAY,IAAIJ,YAAY,IAAIK,UAAU,EAAE;QAC9D;QACA,MAAME,aAAa,GAAGH,YAAY,GAAGC,UAAU;QAC/C,MAAMG,eAAe,GAAGJ,YAAY,GAAGJ,YAAY;QACnDM,QAAQ,GAAGE,eAAe,GAAGD,aAAa;QAC1CD,QAAQ,GAAGG,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEL,QAAQ,CAAC,CAAC;MAC/C,CAAC,MAAM,IAAIN,YAAY,GAAGK,UAAU,EAAE;QACpC;QACAC,QAAQ,GAAG,CAAC;MACd;MACA;;MAEAf,iBAAiB,CAACe,QAAQ,CAAC;;MAE3B;MACA,IAAIG,IAAI,CAACG,GAAG,CAACN,QAAQ,GAAGhB,cAAc,CAAC,GAAG,IAAI,EAAE;QAC9CuB,OAAO,CAACC,GAAG,CAAC,6BAA6Bd,YAAY,CAACe,OAAO,CAAC,CAAC,CAAC,cAAcT,QAAQ,CAACS,OAAO,CAAC,CAAC,CAAC,WAAWvC,KAAK,CAACwC,QAAQ,CAACC,KAAK,CAAClC,QAAQ,CAAC,EAAE,CAAC;MAC/I;IACF,CAAC;IAEDe,MAAM,CAACoB,gBAAgB,CAAC,QAAQ,EAAEzB,YAAY,EAAE;MAAE0B,OAAO,EAAE;IAAK,CAAC,CAAC;IAClE1B,YAAY,CAAC,CAAC;IAEd,OAAO,MAAMK,MAAM,CAACsB,mBAAmB,CAAC,QAAQ,EAAE3B,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM4B,aAAa,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACtC;IACA,MAAMC,aAAa,GAAGF,KAAK,GAAGpC,WAAW,CAAC,CAAC;IAC3C,MAAMuC,YAAY,GAAGhB,IAAI,CAACiB,GAAG,CAACvC,UAAU,EAAEmC,KAAK,CAAC,CAAC,CAAC;;IAElD;IACA,MAAMK,WAAW,GAAGL,KAAK,GAAG,GAAG,CAAC,CAAC;IACjC,MAAMM,UAAU,GAAG,CAAC,CAAC,CAAC;;IAEtB;IACA,MAAMC,aAAa,GAAGL,aAAa,GAAIlC,cAAc,IAAIqC,WAAW,GAAGH,aAAa,CAAE;IACtF,MAAMM,YAAY,GAAGL,YAAY,GAAInC,cAAc,IAAIsC,UAAU,GAAGH,YAAY,CAAE;;IAElF;IACA,MAAMM,OAAO,GAAGtB,IAAI,CAACC,GAAG,CAAC,GAAG,EAAE,CAAC,GAAIY,KAAK,GAAG,GAAI,GAAIhC,cAAc,GAAG,GAAI,CAAC;;IAEzE;IACA,MAAM0C,SAAS,GAAG,CAAC,CAAC,GAAG1C,cAAc,IAAIgC,KAAK,GAAG,CAAC;IAElD,OAAO;MACLW,SAAS,EAAE;AACjB;AACA,qBAAqBJ,aAAa;AAClC,gBAAgBC,YAAY;AAC5B,kBAAkBE,SAAS;AAC3B,qBAAqB,CAACV,KAAK,GAAG,EAAE;AAChC,OAAO;MACDY,MAAM,EAAEX,KAAK,GAAGD,KAAK;MACrBS,OAAO,EAAEA,OAAO;MAChBI,eAAe,EAAE;IACnB,CAAC;EACH,CAAC;;EAED;EACA,MAAMC,aAAa,GAAG5D,KAAK,CAACwC,QAAQ,CAACqB,OAAO,CAACtD,QAAQ,CAAC;EAEtD,oBACEF,OAAA;IACEyD,GAAG,EAAE9C,YAAa;IAClBR,SAAS,EAAE,gBAAgBA,SAAS,EAAG;IACvCC,KAAK,EAAEA,KAAM;IAAAF,QAAA,gBAGbF,OAAA;MAAKI,KAAK,EAAE;QACVsD,QAAQ,EAAE,OAAO;QACjBtC,GAAG,EAAE,MAAM;QACXuC,KAAK,EAAE,MAAM;QACbC,UAAU,EAAE,iBAAiB;QAC7BC,KAAK,EAAE,OAAO;QACdC,OAAO,EAAE,MAAM;QACfC,YAAY,EAAE,KAAK;QACnBC,QAAQ,EAAE,MAAM;QAChBX,MAAM,EAAE,KAAK;QACbY,UAAU,EAAE;MACd,CAAE;MAAA/D,QAAA,GAAC,oBACiB,eAAAF,OAAA;QAAAkE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,cACb,EAAC,CAAC5D,cAAc,GAAG,GAAG,EAAEyB,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC,eAAAlC,OAAA;QAAAkE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,WAC5C,EAACd,aAAa,CAACe,MAAM;IAAA;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eAENrE,OAAA;MAAKG,SAAS,EAAC,wBAAwB;MAAAD,QAAA,EACpCqD,aAAa,CAACgB,GAAG,CAAC,CAACC,KAAK,EAAE/B,KAAK,kBAC9BzC,OAAA;QAEEG,SAAS,EAAC,mBAAmB;QAC7BC,KAAK,EAAEoC,aAAa,CAACC,KAAK,EAAEc,aAAa,CAACe,MAAM,CAAE;QAAApE,QAAA,EAEjDsE;MAAK,GAJD/B,KAAK;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKP,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC7D,EAAA,CA/HIP,WAAW;AAAAwE,EAAA,GAAXxE,WAAW;AAiIjB,eAAeA,WAAW;AAAC,IAAAwE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}