{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Hero.js\";\nimport React from 'react';\nimport TextPressure from './TextPressure';\nimport ShinyText from './ShinyText';\nimport CircularText from './CircularText';\nimport './Hero.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Hero = () => {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"hero\",\n    children: [/*#__PURE__*/_jsxDEV(\"nav\", {\n      className: \"hero-nav\",\n      children: /*#__PURE__*/_jsxDEV(CircularText, {\n        text: \"\\u2022 HOME \\u2022 ABOUT \\u2022 PROJECTS \\u2022 CONTACT \\u2022 BLOG \\u2022 SERVICES \",\n        radius: 100,\n        fontSize: \"12px\",\n        rotationSpeed: 25\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hero-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-title\",\n        children: /*#__PURE__*/_jsxDEV(TextPressure, {\n          text: \"DevDoses\",\n          fontSize: \"6rem\",\n          pressureIntensity: 0.4,\n          className: \"main-title\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-subtitle\",\n        children: /*#__PURE__*/_jsxDEV(ShinyText, {\n          text: \"Creative Developer Portfolio\",\n          fontSize: \"1.5rem\",\n          shimmerDuration: 3000,\n          className: \"subtitle\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-description\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Building amazing digital experiences with modern web technologies\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hero-bg\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"gradient-orb orb-1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"gradient-orb orb-2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"gradient-orb orb-3\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n};\n_c = Hero;\nexport default Hero;\nvar _c;\n$RefreshReg$(_c, \"Hero\");", "map": {"version": 3, "names": ["React", "TextPressure", "ShinyText", "CircularText", "jsxDEV", "_jsxDEV", "Hero", "className", "children", "text", "radius", "fontSize", "rotationSpeed", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "pressureIntensity", "shimmerDuration", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Hero.js"], "sourcesContent": ["import React from 'react';\nimport TextPressure from './TextPressure';\nimport ShinyText from './ShinyText';\nimport CircularText from './CircularText';\nimport './Hero.css';\n\nconst Hero = () => {\n  return (\n    <section className=\"hero\">\n      {/* Navigation Menu */}\n      <nav className=\"hero-nav\">\n        <CircularText \n          text=\"• HOME • ABOUT • PROJECTS • CONTACT • BLOG • SERVICES \"\n          radius={100}\n          fontSize=\"12px\"\n          rotationSpeed={25}\n        />\n      </nav>\n\n      {/* Main Content */}\n      <div className=\"hero-content\">\n        <div className=\"hero-title\">\n          <TextPressure \n            text=\"DevDoses\"\n            fontSize=\"6rem\"\n            pressureIntensity={0.4}\n            className=\"main-title\"\n          />\n        </div>\n        \n        <div className=\"hero-subtitle\">\n          <ShinyText \n            text=\"Creative Developer Portfolio\"\n            fontSize=\"1.5rem\"\n            shimmerDuration={3000}\n            className=\"subtitle\"\n          />\n        </div>\n\n        <div className=\"hero-description\">\n          <p>Building amazing digital experiences with modern web technologies</p>\n        </div>\n      </div>\n\n      {/* Background Elements */}\n      <div className=\"hero-bg\">\n        <div className=\"gradient-orb orb-1\"></div>\n        <div className=\"gradient-orb orb-2\"></div>\n        <div className=\"gradient-orb orb-3\"></div>\n      </div>\n    </section>\n  );\n};\n\nexport default Hero;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAO,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpB,MAAMC,IAAI,GAAGA,CAAA,KAAM;EACjB,oBACED,OAAA;IAASE,SAAS,EAAC,MAAM;IAAAC,QAAA,gBAEvBH,OAAA;MAAKE,SAAS,EAAC,UAAU;MAAAC,QAAA,eACvBH,OAAA,CAACF,YAAY;QACXM,IAAI,EAAC,sFAAwD;QAC7DC,MAAM,EAAE,GAAI;QACZC,QAAQ,EAAC,MAAM;QACfC,aAAa,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNX,OAAA;MAAKE,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BH,OAAA;QAAKE,SAAS,EAAC,YAAY;QAAAC,QAAA,eACzBH,OAAA,CAACJ,YAAY;UACXQ,IAAI,EAAC,UAAU;UACfE,QAAQ,EAAC,MAAM;UACfM,iBAAiB,EAAE,GAAI;UACvBV,SAAS,EAAC;QAAY;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENX,OAAA;QAAKE,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5BH,OAAA,CAACH,SAAS;UACRO,IAAI,EAAC,8BAA8B;UACnCE,QAAQ,EAAC,QAAQ;UACjBO,eAAe,EAAE,IAAK;UACtBX,SAAS,EAAC;QAAU;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENX,OAAA;QAAKE,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/BH,OAAA;UAAAG,QAAA,EAAG;QAAiE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNX,OAAA;MAAKE,SAAS,EAAC,SAAS;MAAAC,QAAA,gBACtBH,OAAA;QAAKE,SAAS,EAAC;MAAoB;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC1CX,OAAA;QAAKE,SAAS,EAAC;MAAoB;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC1CX,OAAA;QAAKE,SAAS,EAAC;MAAoB;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACG,EAAA,GA9CIb,IAAI;AAgDV,eAAeA,IAAI;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}