{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/CircularText.js\",\n  _s = $RefreshSig$();\nimport React, { useRef, useEffect } from 'react';\nimport './CircularText.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CircularText = ({\n  text = \"• HOME • ABOUT • PROJECTS • CONTACT \",\n  className = \"\",\n  style = {},\n  radius = 80,\n  fontSize = \"14px\",\n  rotationSpeed = 20\n}) => {\n  _s();\n  const textRef = useRef(null);\n  useEffect(() => {\n    const element = textRef.current;\n    if (!element) return;\n    element.style.setProperty('--radius', `${radius}px`);\n    element.style.setProperty('--rotation-speed', `${rotationSpeed}s`);\n    const letters = text.split('');\n    const angleStep = 360 / letters.length;\n    element.innerHTML = '';\n    letters.forEach((letter, index) => {\n      const span = document.createElement('span');\n      span.textContent = letter;\n      span.style.transform = `rotate(${index * angleStep}deg)`;\n      span.style.transformOrigin = `0 ${radius}px`;\n      element.appendChild(span);\n    });\n  }, [text, radius, rotationSpeed]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `circular-text-container ${className}`,\n    style: style,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: textRef,\n      className: \"circular-text\",\n      style: {\n        fontSize\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 5\n  }, this);\n};\n_s(CircularText, \"03a/W9zemG2g1Vmhz2i1tulxw6U=\");\n_c = CircularText;\nexport default CircularText;\nvar _c;\n$RefreshReg$(_c, \"CircularText\");", "map": {"version": 3, "names": ["React", "useRef", "useEffect", "jsxDEV", "_jsxDEV", "CircularText", "text", "className", "style", "radius", "fontSize", "rotationSpeed", "_s", "textRef", "element", "current", "setProperty", "letters", "split", "angleStep", "length", "innerHTML", "for<PERSON>ach", "letter", "index", "span", "document", "createElement", "textContent", "transform", "transform<PERSON><PERSON>in", "append<PERSON><PERSON><PERSON>", "children", "ref", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/CircularText.js"], "sourcesContent": ["import React, { useRef, useEffect } from 'react';\nimport './CircularText.css';\n\nconst CircularText = ({ \n  text = \"• HOME • ABOUT • PROJECTS • CONTACT \", \n  className = \"\",\n  style = {},\n  radius = 80,\n  fontSize = \"14px\",\n  rotationSpeed = 20\n}) => {\n  const textRef = useRef(null);\n\n  useEffect(() => {\n    const element = textRef.current;\n    if (!element) return;\n\n    element.style.setProperty('--radius', `${radius}px`);\n    element.style.setProperty('--rotation-speed', `${rotationSpeed}s`);\n    \n    const letters = text.split('');\n    const angleStep = 360 / letters.length;\n    \n    element.innerHTML = '';\n    \n    letters.forEach((letter, index) => {\n      const span = document.createElement('span');\n      span.textContent = letter;\n      span.style.transform = `rotate(${index * angleStep}deg)`;\n      span.style.transformOrigin = `0 ${radius}px`;\n      element.appendChild(span);\n    });\n  }, [text, radius, rotationSpeed]);\n\n  return (\n    <div \n      className={`circular-text-container ${className}`}\n      style={style}\n    >\n      <div \n        ref={textRef}\n        className=\"circular-text\"\n        style={{ fontSize }}\n      />\n    </div>\n  );\n};\n\nexport default CircularText;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAChD,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAMC,YAAY,GAAGA,CAAC;EACpBC,IAAI,GAAG,sCAAsC;EAC7CC,SAAS,GAAG,EAAE;EACdC,KAAK,GAAG,CAAC,CAAC;EACVC,MAAM,GAAG,EAAE;EACXC,QAAQ,GAAG,MAAM;EACjBC,aAAa,GAAG;AAClB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,OAAO,GAAGZ,MAAM,CAAC,IAAI,CAAC;EAE5BC,SAAS,CAAC,MAAM;IACd,MAAMY,OAAO,GAAGD,OAAO,CAACE,OAAO;IAC/B,IAAI,CAACD,OAAO,EAAE;IAEdA,OAAO,CAACN,KAAK,CAACQ,WAAW,CAAC,UAAU,EAAE,GAAGP,MAAM,IAAI,CAAC;IACpDK,OAAO,CAACN,KAAK,CAACQ,WAAW,CAAC,kBAAkB,EAAE,GAAGL,aAAa,GAAG,CAAC;IAElE,MAAMM,OAAO,GAAGX,IAAI,CAACY,KAAK,CAAC,EAAE,CAAC;IAC9B,MAAMC,SAAS,GAAG,GAAG,GAAGF,OAAO,CAACG,MAAM;IAEtCN,OAAO,CAACO,SAAS,GAAG,EAAE;IAEtBJ,OAAO,CAACK,OAAO,CAAC,CAACC,MAAM,EAAEC,KAAK,KAAK;MACjC,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;MAC3CF,IAAI,CAACG,WAAW,GAAGL,MAAM;MACzBE,IAAI,CAACjB,KAAK,CAACqB,SAAS,GAAG,UAAUL,KAAK,GAAGL,SAAS,MAAM;MACxDM,IAAI,CAACjB,KAAK,CAACsB,eAAe,GAAG,KAAKrB,MAAM,IAAI;MAC5CK,OAAO,CAACiB,WAAW,CAACN,IAAI,CAAC;IAC3B,CAAC,CAAC;EACJ,CAAC,EAAE,CAACnB,IAAI,EAAEG,MAAM,EAAEE,aAAa,CAAC,CAAC;EAEjC,oBACEP,OAAA;IACEG,SAAS,EAAE,2BAA2BA,SAAS,EAAG;IAClDC,KAAK,EAAEA,KAAM;IAAAwB,QAAA,eAEb5B,OAAA;MACE6B,GAAG,EAAEpB,OAAQ;MACbN,SAAS,EAAC,eAAe;MACzBC,KAAK,EAAE;QAAEE;MAAS;IAAE;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACzB,EAAA,CA3CIP,YAAY;AAAAiC,EAAA,GAAZjC,YAAY;AA6ClB,eAAeA,YAAY;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}