{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Projects.js\";\nimport React from 'react';\nimport Ribbons from './Ribbons';\nimport ScrollStack from './ScrollStack';\nimport ScrollFloat from './ScrollFloat';\nimport './Projects.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Projects = () => {\n  const projects = [{\n    id: 1,\n    title: 'E-Commerce Platform',\n    description: 'Modern e-commerce solution built with React, Node.js, and MongoDB. Features include real-time inventory, payment processing, and admin dashboard.',\n    technologies: ['React', 'Node.js', 'MongoDB', 'Stripe', 'Socket.io'],\n    image: '🛒',\n    status: 'Completed',\n    link: '#'\n  }, {\n    id: 2,\n    title: 'Task Management App',\n    description: 'Collaborative task management application with real-time updates, team collaboration, and advanced project tracking capabilities.',\n    technologies: ['Vue.js', 'Express', 'PostgreSQL', 'WebSocket', 'Docker'],\n    image: '📋',\n    status: 'In Progress',\n    link: '#'\n  }, {\n    id: 3,\n    title: 'AI Chat Assistant',\n    description: 'Intelligent chatbot powered by machine learning algorithms. Provides customer support automation and natural language processing.',\n    technologies: ['Python', 'TensorFlow', 'FastAPI', 'React', 'Redis'],\n    image: '🤖',\n    status: 'Completed',\n    link: '#'\n  }, {\n    id: 4,\n    title: 'Social Media Dashboard',\n    description: 'Comprehensive social media management platform with analytics, scheduling, and multi-platform integration capabilities.',\n    technologies: ['Next.js', 'TypeScript', 'Prisma', 'GraphQL', 'AWS'],\n    image: '📱',\n    status: 'Completed',\n    link: '#'\n  }, {\n    id: 5,\n    title: 'Crypto Trading Bot',\n    description: 'Automated cryptocurrency trading bot with advanced algorithms, risk management, and real-time market analysis.',\n    technologies: ['Python', 'Django', 'Celery', 'PostgreSQL', 'Docker'],\n    image: '₿',\n    status: 'In Development',\n    link: '#'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"projects\",\n    id: \"projects\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"projects-container\",\n      children: [/*#__PURE__*/_jsxDEV(ScrollFloat, {\n        direction: \"up\",\n        duration: 0.8,\n        delay: 0.2,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"projects-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"projects-title\",\n            children: \"Featured Projects\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"projects-subtitle\",\n            children: \"Showcasing innovative solutions and cutting-edge technologies\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ScrollStack, {\n        stackOffset: 30,\n        stackScale: 0.95,\n        children: projects.map((project, index) => /*#__PURE__*/_jsxDEV(ScrollFloat, {\n          direction: \"up\",\n          duration: 0.7,\n          delay: 0.4 + index * 0.1,\n          children: /*#__PURE__*/_jsxDEV(Ribbons, {\n            className: \"project-card\",\n            ribbonColor: `rgba(${100 + index * 30}, ${150 + index * 20}, ${200 + index * 15}, 0.3)`,\n            ribbonSpeed: 2000 + index * 500,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"project-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"project-icon\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"project-emoji\",\n                  children: project.image\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 83,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `project-status ${project.status.toLowerCase().replace(' ', '-')}`,\n                  children: project.status\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 84,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 82,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"project-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"project-title\",\n                  children: project.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 90,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"project-description\",\n                  children: project.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 91,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"project-technologies\",\n                  children: project.technologies.map((tech, techIndex) => /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"tech-tag\",\n                    children: tech\n                  }, techIndex, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 95,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 93,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"project-actions\",\n                  children: /*#__PURE__*/_jsxDEV(\"a\", {\n                    href: project.link,\n                    className: \"project-link\",\n                    children: [\"View Project\", /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"link-arrow\",\n                      children: \"\\u2192\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 104,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 102,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 101,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 15\n          }, this)\n        }, project.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 57,\n    columnNumber: 5\n  }, this);\n};\n_c = Projects;\nexport default Projects;\nvar _c;\n$RefreshReg$(_c, \"Projects\");", "map": {"version": 3, "names": ["React", "Ribbons", "ScrollStack", "ScrollFloat", "jsxDEV", "_jsxDEV", "Projects", "projects", "id", "title", "description", "technologies", "image", "status", "link", "className", "children", "direction", "duration", "delay", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "stackOffset", "stackScale", "map", "project", "index", "ribbonColor", "ribbonSpeed", "toLowerCase", "replace", "tech", "techIndex", "href", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Projects.js"], "sourcesContent": ["import React from 'react';\nimport Ribbons from './Ribbons';\nimport ScrollStack from './ScrollStack';\nimport ScrollFloat from './ScrollFloat';\nimport './Projects.css';\n\nconst Projects = () => {\n  const projects = [\n    {\n      id: 1,\n      title: 'E-Commerce Platform',\n      description: 'Modern e-commerce solution built with React, Node.js, and MongoDB. Features include real-time inventory, payment processing, and admin dashboard.',\n      technologies: ['React', 'Node.js', 'MongoDB', 'Stripe', 'Socket.io'],\n      image: '🛒',\n      status: 'Completed',\n      link: '#'\n    },\n    {\n      id: 2,\n      title: 'Task Management App',\n      description: 'Collaborative task management application with real-time updates, team collaboration, and advanced project tracking capabilities.',\n      technologies: ['Vue.js', 'Express', 'PostgreSQL', 'WebSocket', 'Docker'],\n      image: '📋',\n      status: 'In Progress',\n      link: '#'\n    },\n    {\n      id: 3,\n      title: 'AI Chat Assistant',\n      description: 'Intelligent chatbot powered by machine learning algorithms. Provides customer support automation and natural language processing.',\n      technologies: ['Python', 'TensorFlow', 'FastAPI', 'React', 'Redis'],\n      image: '🤖',\n      status: 'Completed',\n      link: '#'\n    },\n    {\n      id: 4,\n      title: 'Social Media Dashboard',\n      description: 'Comprehensive social media management platform with analytics, scheduling, and multi-platform integration capabilities.',\n      technologies: ['Next.js', 'TypeScript', 'Prisma', 'GraphQL', 'AWS'],\n      image: '📱',\n      status: 'Completed',\n      link: '#'\n    },\n    {\n      id: 5,\n      title: 'Crypto Trading Bot',\n      description: 'Automated cryptocurrency trading bot with advanced algorithms, risk management, and real-time market analysis.',\n      technologies: ['Python', 'Django', 'Celery', 'PostgreSQL', 'Docker'],\n      image: '₿',\n      status: 'In Development',\n      link: '#'\n    }\n  ];\n\n  return (\n    <section className=\"projects\" id=\"projects\">\n      <div className=\"projects-container\">\n        <ScrollFloat direction=\"up\" duration={0.8} delay={0.2}>\n          <div className=\"projects-header\">\n            <h2 className=\"projects-title\">Featured Projects</h2>\n            <p className=\"projects-subtitle\">\n              Showcasing innovative solutions and cutting-edge technologies\n            </p>\n          </div>\n        </ScrollFloat>\n\n        <ScrollStack stackOffset={30} stackScale={0.95}>\n          {projects.map((project, index) => (\n            <ScrollFloat \n              key={project.id} \n              direction=\"up\" \n              duration={0.7} \n              delay={0.4 + (index * 0.1)}\n            >\n              <Ribbons \n                className=\"project-card\"\n                ribbonColor={`rgba(${100 + index * 30}, ${150 + index * 20}, ${200 + index * 15}, 0.3)`}\n                ribbonSpeed={2000 + index * 500}\n              >\n                <div className=\"project-content\">\n                  <div className=\"project-icon\">\n                    <span className=\"project-emoji\">{project.image}</span>\n                    <div className={`project-status ${project.status.toLowerCase().replace(' ', '-')}`}>\n                      {project.status}\n                    </div>\n                  </div>\n                  \n                  <div className=\"project-info\">\n                    <h3 className=\"project-title\">{project.title}</h3>\n                    <p className=\"project-description\">{project.description}</p>\n                    \n                    <div className=\"project-technologies\">\n                      {project.technologies.map((tech, techIndex) => (\n                        <span key={techIndex} className=\"tech-tag\">\n                          {tech}\n                        </span>\n                      ))}\n                    </div>\n                    \n                    <div className=\"project-actions\">\n                      <a href={project.link} className=\"project-link\">\n                        View Project\n                        <span className=\"link-arrow\">→</span>\n                      </a>\n                    </div>\n                  </div>\n                </div>\n              </Ribbons>\n            </ScrollFloat>\n          ))}\n        </ScrollStack>\n      </div>\n    </section>\n  );\n};\n\nexport default Projects;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EACrB,MAAMC,QAAQ,GAAG,CACf;IACEC,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,qBAAqB;IAC5BC,WAAW,EAAE,mJAAmJ;IAChKC,YAAY,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,CAAC;IACpEC,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE,WAAW;IACnBC,IAAI,EAAE;EACR,CAAC,EACD;IACEN,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,qBAAqB;IAC5BC,WAAW,EAAE,mIAAmI;IAChJC,YAAY,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,YAAY,EAAE,WAAW,EAAE,QAAQ,CAAC;IACxEC,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE,aAAa;IACrBC,IAAI,EAAE;EACR,CAAC,EACD;IACEN,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE,mIAAmI;IAChJC,YAAY,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC;IACnEC,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE,WAAW;IACnBC,IAAI,EAAE;EACR,CAAC,EACD;IACEN,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,wBAAwB;IAC/BC,WAAW,EAAE,yHAAyH;IACtIC,YAAY,EAAE,CAAC,SAAS,EAAE,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,CAAC;IACnEC,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE,WAAW;IACnBC,IAAI,EAAE;EACR,CAAC,EACD;IACEN,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,gHAAgH;IAC7HC,YAAY,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAAE,QAAQ,CAAC;IACpEC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAE,gBAAgB;IACxBC,IAAI,EAAE;EACR,CAAC,CACF;EAED,oBACET,OAAA;IAASU,SAAS,EAAC,UAAU;IAACP,EAAE,EAAC,UAAU;IAAAQ,QAAA,eACzCX,OAAA;MAAKU,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjCX,OAAA,CAACF,WAAW;QAACc,SAAS,EAAC,IAAI;QAACC,QAAQ,EAAE,GAAI;QAACC,KAAK,EAAE,GAAI;QAAAH,QAAA,eACpDX,OAAA;UAAKU,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BX,OAAA;YAAIU,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAiB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrDlB,OAAA;YAAGU,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAEjC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAEdlB,OAAA,CAACH,WAAW;QAACsB,WAAW,EAAE,EAAG;QAACC,UAAU,EAAE,IAAK;QAAAT,QAAA,EAC5CT,QAAQ,CAACmB,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC3BvB,OAAA,CAACF,WAAW;UAEVc,SAAS,EAAC,IAAI;UACdC,QAAQ,EAAE,GAAI;UACdC,KAAK,EAAE,GAAG,GAAIS,KAAK,GAAG,GAAK;UAAAZ,QAAA,eAE3BX,OAAA,CAACJ,OAAO;YACNc,SAAS,EAAC,cAAc;YACxBc,WAAW,EAAE,QAAQ,GAAG,GAAGD,KAAK,GAAG,EAAE,KAAK,GAAG,GAAGA,KAAK,GAAG,EAAE,KAAK,GAAG,GAAGA,KAAK,GAAG,EAAE,QAAS;YACxFE,WAAW,EAAE,IAAI,GAAGF,KAAK,GAAG,GAAI;YAAAZ,QAAA,eAEhCX,OAAA;cAAKU,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BX,OAAA;gBAAKU,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3BX,OAAA;kBAAMU,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAEW,OAAO,CAACf;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtDlB,OAAA;kBAAKU,SAAS,EAAE,kBAAkBY,OAAO,CAACd,MAAM,CAACkB,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAG;kBAAAhB,QAAA,EAChFW,OAAO,CAACd;gBAAM;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENlB,OAAA;gBAAKU,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3BX,OAAA;kBAAIU,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAEW,OAAO,CAAClB;gBAAK;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAClDlB,OAAA;kBAAGU,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAEW,OAAO,CAACjB;gBAAW;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAE5DlB,OAAA;kBAAKU,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,EAClCW,OAAO,CAAChB,YAAY,CAACe,GAAG,CAAC,CAACO,IAAI,EAAEC,SAAS,kBACxC7B,OAAA;oBAAsBU,SAAS,EAAC,UAAU;oBAAAC,QAAA,EACvCiB;kBAAI,GADIC,SAAS;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEd,CACP;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAENlB,OAAA;kBAAKU,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,eAC9BX,OAAA;oBAAG8B,IAAI,EAAER,OAAO,CAACb,IAAK;oBAACC,SAAS,EAAC,cAAc;oBAAAC,QAAA,GAAC,cAE9C,eAAAX,OAAA;sBAAMU,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAC;oBAAC;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC,GAtCLI,OAAO,CAACnB,EAAE;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAuCJ,CACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACa,EAAA,GA7GI9B,QAAQ;AA+Gd,eAAeA,QAAQ;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}