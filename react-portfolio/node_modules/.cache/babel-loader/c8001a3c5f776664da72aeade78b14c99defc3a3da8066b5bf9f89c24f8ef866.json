{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ScrollStack.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport './ScrollStack.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ScrollStack = ({\n  children,\n  className = \"\",\n  style = {},\n  stackOffset = 20,\n  stackScale = 0.95,\n  threshold = 0.1\n}) => {\n  _s();\n  const [scrollProgress, setScrollProgress] = useState(0);\n  const [isVisible, setIsVisible] = useState(false);\n  const containerRef = useRef(null);\n  useEffect(() => {\n    const handleScroll = () => {\n      if (!containerRef.current) return;\n      const rect = containerRef.current.getBoundingClientRect();\n      const windowHeight = window.innerHeight;\n      const elementTop = rect.top;\n      const elementHeight = rect.height;\n\n      // Calculate visibility\n      const visibleTop = Math.max(0, -elementTop);\n      const visibleBottom = Math.min(elementHeight, windowHeight - elementTop);\n      const visibleHeight = Math.max(0, visibleBottom - visibleTop);\n      const visibilityRatio = visibleHeight / elementHeight;\n      setIsVisible(visibilityRatio > threshold);\n\n      // Calculate scroll progress (0 to 1) - improved calculation\n      const scrollStart = windowHeight;\n      const scrollEnd = -elementHeight;\n      const currentPosition = elementTop;\n      const totalDistance = scrollStart - scrollEnd;\n      const currentDistance = scrollStart - currentPosition;\n      const progress = Math.max(0, Math.min(1, currentDistance / totalDistance));\n      setScrollProgress(progress);\n    };\n    window.addEventListener('scroll', handleScroll, {\n      passive: true\n    });\n    handleScroll(); // Initial call\n\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, [threshold]);\n  const getStackStyle = (index, total) => {\n    const baseOffset = stackOffset * index;\n    const baseScale = Math.pow(stackScale, index);\n\n    // Apply scroll-based transformations\n    const scrollOffset = scrollProgress * baseOffset * 3;\n    const scrollScale = baseScale + scrollProgress * (1 - baseScale);\n    const scrollRotation = scrollProgress * index * 2;\n    return {\n      transform: `\n        translateY(${baseOffset - scrollOffset}px)\n        scale(${scrollScale})\n        rotateX(${scrollRotation}deg)\n        translateZ(${-index * 20}px)\n      `,\n      zIndex: total - index,\n      opacity: isVisible ? Math.max(0.3, 1 - index * 0.15) : 0.1,\n      transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',\n      transformOrigin: 'center center'\n    };\n  };\n\n  // Convert children to array if it's not already\n  const childrenArray = React.Children.toArray(children);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: containerRef,\n    className: `scroll-stack ${className}`,\n    style: style,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"scroll-stack-container\",\n      children: childrenArray.map((child, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"scroll-stack-item\",\n        style: getStackStyle(index, childrenArray.length),\n        children: child\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 78,\n    columnNumber: 5\n  }, this);\n};\n_s(ScrollStack, \"ZJ9NqbTuOD8Nvn1O9a2CJWWXESw=\");\n_c = ScrollStack;\nexport default ScrollStack;\nvar _c;\n$RefreshReg$(_c, \"ScrollStack\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "ScrollStack", "children", "className", "style", "stackOffset", "stackScale", "threshold", "_s", "scrollProgress", "setScrollProgress", "isVisible", "setIsVisible", "containerRef", "handleScroll", "current", "rect", "getBoundingClientRect", "windowHeight", "window", "innerHeight", "elementTop", "top", "elementHeight", "height", "visibleTop", "Math", "max", "visibleBottom", "min", "visibleHeight", "visibilityRatio", "scrollStart", "scrollEnd", "currentPosition", "totalDistance", "currentDistance", "progress", "addEventListener", "passive", "removeEventListener", "getStackStyle", "index", "total", "baseOffset", "baseScale", "pow", "scrollOffset", "scrollScale", "scrollRotation", "transform", "zIndex", "opacity", "transition", "transform<PERSON><PERSON>in", "childrenA<PERSON>y", "Children", "toArray", "ref", "map", "child", "length", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ScrollStack.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport './ScrollStack.css';\n\nconst ScrollStack = ({ \n  children, \n  className = \"\", \n  style = {},\n  stackOffset = 20,\n  stackScale = 0.95,\n  threshold = 0.1\n}) => {\n  const [scrollProgress, setScrollProgress] = useState(0);\n  const [isVisible, setIsVisible] = useState(false);\n  const containerRef = useRef(null);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      if (!containerRef.current) return;\n\n      const rect = containerRef.current.getBoundingClientRect();\n      const windowHeight = window.innerHeight;\n      const elementTop = rect.top;\n      const elementHeight = rect.height;\n\n      // Calculate visibility\n      const visibleTop = Math.max(0, -elementTop);\n      const visibleBottom = Math.min(elementHeight, windowHeight - elementTop);\n      const visibleHeight = Math.max(0, visibleBottom - visibleTop);\n      const visibilityRatio = visibleHeight / elementHeight;\n\n      setIsVisible(visibilityRatio > threshold);\n\n      // Calculate scroll progress (0 to 1) - improved calculation\n      const scrollStart = windowHeight;\n      const scrollEnd = -elementHeight;\n      const currentPosition = elementTop;\n\n      const totalDistance = scrollStart - scrollEnd;\n      const currentDistance = scrollStart - currentPosition;\n      const progress = Math.max(0, Math.min(1, currentDistance / totalDistance));\n\n      setScrollProgress(progress);\n    };\n\n    window.addEventListener('scroll', handleScroll, { passive: true });\n    handleScroll(); // Initial call\n\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, [threshold]);\n\n  const getStackStyle = (index, total) => {\n    const baseOffset = stackOffset * index;\n    const baseScale = Math.pow(stackScale, index);\n\n    // Apply scroll-based transformations\n    const scrollOffset = scrollProgress * baseOffset * 3;\n    const scrollScale = baseScale + (scrollProgress * (1 - baseScale));\n    const scrollRotation = scrollProgress * index * 2;\n\n    return {\n      transform: `\n        translateY(${baseOffset - scrollOffset}px)\n        scale(${scrollScale})\n        rotateX(${scrollRotation}deg)\n        translateZ(${-index * 20}px)\n      `,\n      zIndex: total - index,\n      opacity: isVisible ? Math.max(0.3, 1 - (index * 0.15)) : 0.1,\n      transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',\n      transformOrigin: 'center center'\n    };\n  };\n\n  // Convert children to array if it's not already\n  const childrenArray = React.Children.toArray(children);\n\n  return (\n    <div\n      ref={containerRef}\n      className={`scroll-stack ${className}`}\n      style={style}\n    >\n      <div className=\"scroll-stack-container\">\n        {childrenArray.map((child, index) => (\n          <div\n            key={index}\n            className=\"scroll-stack-item\"\n            style={getStackStyle(index, childrenArray.length)}\n          >\n            {child}\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n};\n\nexport default ScrollStack;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,WAAW,GAAGA,CAAC;EACnBC,QAAQ;EACRC,SAAS,GAAG,EAAE;EACdC,KAAK,GAAG,CAAC,CAAC;EACVC,WAAW,GAAG,EAAE;EAChBC,UAAU,GAAG,IAAI;EACjBC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGd,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAMiB,YAAY,GAAGf,MAAM,CAAC,IAAI,CAAC;EAEjCD,SAAS,CAAC,MAAM;IACd,MAAMiB,YAAY,GAAGA,CAAA,KAAM;MACzB,IAAI,CAACD,YAAY,CAACE,OAAO,EAAE;MAE3B,MAAMC,IAAI,GAAGH,YAAY,CAACE,OAAO,CAACE,qBAAqB,CAAC,CAAC;MACzD,MAAMC,YAAY,GAAGC,MAAM,CAACC,WAAW;MACvC,MAAMC,UAAU,GAAGL,IAAI,CAACM,GAAG;MAC3B,MAAMC,aAAa,GAAGP,IAAI,CAACQ,MAAM;;MAEjC;MACA,MAAMC,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAACN,UAAU,CAAC;MAC3C,MAAMO,aAAa,GAAGF,IAAI,CAACG,GAAG,CAACN,aAAa,EAAEL,YAAY,GAAGG,UAAU,CAAC;MACxE,MAAMS,aAAa,GAAGJ,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEC,aAAa,GAAGH,UAAU,CAAC;MAC7D,MAAMM,eAAe,GAAGD,aAAa,GAAGP,aAAa;MAErDX,YAAY,CAACmB,eAAe,GAAGxB,SAAS,CAAC;;MAEzC;MACA,MAAMyB,WAAW,GAAGd,YAAY;MAChC,MAAMe,SAAS,GAAG,CAACV,aAAa;MAChC,MAAMW,eAAe,GAAGb,UAAU;MAElC,MAAMc,aAAa,GAAGH,WAAW,GAAGC,SAAS;MAC7C,MAAMG,eAAe,GAAGJ,WAAW,GAAGE,eAAe;MACrD,MAAMG,QAAQ,GAAGX,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEO,eAAe,GAAGD,aAAa,CAAC,CAAC;MAE1EzB,iBAAiB,CAAC2B,QAAQ,CAAC;IAC7B,CAAC;IAEDlB,MAAM,CAACmB,gBAAgB,CAAC,QAAQ,EAAExB,YAAY,EAAE;MAAEyB,OAAO,EAAE;IAAK,CAAC,CAAC;IAClEzB,YAAY,CAAC,CAAC,CAAC,CAAC;;IAEhB,OAAO,MAAMK,MAAM,CAACqB,mBAAmB,CAAC,QAAQ,EAAE1B,YAAY,CAAC;EACjE,CAAC,EAAE,CAACP,SAAS,CAAC,CAAC;EAEf,MAAMkC,aAAa,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACtC,MAAMC,UAAU,GAAGvC,WAAW,GAAGqC,KAAK;IACtC,MAAMG,SAAS,GAAGnB,IAAI,CAACoB,GAAG,CAACxC,UAAU,EAAEoC,KAAK,CAAC;;IAE7C;IACA,MAAMK,YAAY,GAAGtC,cAAc,GAAGmC,UAAU,GAAG,CAAC;IACpD,MAAMI,WAAW,GAAGH,SAAS,GAAIpC,cAAc,IAAI,CAAC,GAAGoC,SAAS,CAAE;IAClE,MAAMI,cAAc,GAAGxC,cAAc,GAAGiC,KAAK,GAAG,CAAC;IAEjD,OAAO;MACLQ,SAAS,EAAE;AACjB,qBAAqBN,UAAU,GAAGG,YAAY;AAC9C,gBAAgBC,WAAW;AAC3B,kBAAkBC,cAAc;AAChC,qBAAqB,CAACP,KAAK,GAAG,EAAE;AAChC,OAAO;MACDS,MAAM,EAAER,KAAK,GAAGD,KAAK;MACrBU,OAAO,EAAEzC,SAAS,GAAGe,IAAI,CAACC,GAAG,CAAC,GAAG,EAAE,CAAC,GAAIe,KAAK,GAAG,IAAK,CAAC,GAAG,GAAG;MAC5DW,UAAU,EAAE,+CAA+C;MAC3DC,eAAe,EAAE;IACnB,CAAC;EACH,CAAC;;EAED;EACA,MAAMC,aAAa,GAAG5D,KAAK,CAAC6D,QAAQ,CAACC,OAAO,CAACvD,QAAQ,CAAC;EAEtD,oBACEF,OAAA;IACE0D,GAAG,EAAE7C,YAAa;IAClBV,SAAS,EAAE,gBAAgBA,SAAS,EAAG;IACvCC,KAAK,EAAEA,KAAM;IAAAF,QAAA,eAEbF,OAAA;MAAKG,SAAS,EAAC,wBAAwB;MAAAD,QAAA,EACpCqD,aAAa,CAACI,GAAG,CAAC,CAACC,KAAK,EAAElB,KAAK,kBAC9B1C,OAAA;QAEEG,SAAS,EAAC,mBAAmB;QAC7BC,KAAK,EAAEqC,aAAa,CAACC,KAAK,EAAEa,aAAa,CAACM,MAAM,CAAE;QAAA3D,QAAA,EAEjD0D;MAAK,GAJDlB,KAAK;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKP,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzD,EAAA,CA5FIP,WAAW;AAAAiE,EAAA,GAAXjE,WAAW;AA8FjB,eAAeA,WAAW;AAAC,IAAAiE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}