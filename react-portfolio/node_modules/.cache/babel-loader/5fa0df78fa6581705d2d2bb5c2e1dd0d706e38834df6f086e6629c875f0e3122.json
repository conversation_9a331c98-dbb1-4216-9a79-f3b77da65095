{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Ribbons.js\",\n  _s = $RefreshSig$();\nimport { useRef, useEffect } from 'react';\nimport { Renderer, Camera, Transform, Plane, Program, Mesh, Vec2 } from 'ogl';\nimport './Ribbons.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Ribbons = ({\n  children,\n  className = '',\n  style = {},\n  ribbonColor = 'rgba(78, 205, 196, 0.2)',\n  ribbonSpeed = 2000,\n  ribbonCount = 3\n}) => {\n  _s();\n  const canvasRef = useRef(null);\n  const rendererRef = useRef(null);\n  const animationRef = useRef(null);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: containerRef,\n    className: `ribbons-container ${className}`,\n    style: style,\n    onMouseEnter: handleMouseEnter,\n    onMouseLeave: handleMouseLeave,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ribbons-wrapper\",\n      children: [...Array(5)].map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"ribbon\",\n        style: {\n          '--ribbon-delay': `${index * 0.5}s`,\n          '--ribbon-color': ribbonColor,\n          '--ribbon-width': `${ribbonWidth}px`,\n          '--ribbon-speed': `${ribbonSpeed}ms`,\n          opacity: isHovering ? 0.8 : 0.3,\n          transform: isHovering ? `translate(${mousePosition.x * 0.1}px, ${mousePosition.y * 0.1}px) rotate(${index * 15}deg)` : `rotate(${index * 15}deg)`\n        }\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ribbons-content\",\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 5\n  }, this);\n};\n_s(Ribbons, \"+V7oPx/wbE84dggp3cQxjm2jfEI=\");\n_c = Ribbons;\nexport default Ribbons;\nvar _c;\n$RefreshReg$(_c, \"Ribbons\");", "map": {"version": 3, "names": ["useRef", "useEffect", "<PERSON><PERSON><PERSON>", "Camera", "Transform", "Plane", "Program", "<PERSON><PERSON>", "Vec2", "jsxDEV", "_jsxDEV", "Ribbons", "children", "className", "style", "ribbonColor", "ribbonSpeed", "ribbonCount", "_s", "canvasRef", "rendererRef", "animationRef", "ref", "containerRef", "onMouseEnter", "handleMouseEnter", "onMouseLeave", "handleMouseLeave", "Array", "map", "_", "index", "ribbonWidth", "opacity", "isHovering", "transform", "mousePosition", "x", "y", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Ribbons.js"], "sourcesContent": ["import { useRef, useEffect } from 'react';\nimport { Ren<PERSON>er, Camera, Transform, Plane, Program, Mesh, Vec2 } from 'ogl';\nimport './Ribbons.css';\n\nconst Ribbons = ({\n  children,\n  className = '',\n  style = {},\n  ribbonColor = 'rgba(78, 205, 196, 0.2)',\n  ribbonSpeed = 2000,\n  ribbonCount = 3\n}) => {\n  const canvasRef = useRef(null);\n  const rendererRef = useRef(null);\n  const animationRef = useRef(null);\n\n  return (\n    <div\n      ref={containerRef}\n      className={`ribbons-container ${className}`}\n      style={style}\n      onMouseEnter={handleMouseEnter}\n      onMouseLeave={handleMouseLeave}\n    >\n      {/* Animated ribbons */}\n      <div className=\"ribbons-wrapper\">\n        {[...Array(5)].map((_, index) => (\n          <div\n            key={index}\n            className=\"ribbon\"\n            style={{\n              '--ribbon-delay': `${index * 0.5}s`,\n              '--ribbon-color': ribbonColor,\n              '--ribbon-width': `${ribbonWidth}px`,\n              '--ribbon-speed': `${ribbonSpeed}ms`,\n              opacity: isHovering ? 0.8 : 0.3,\n              transform: isHovering \n                ? `translate(${mousePosition.x * 0.1}px, ${mousePosition.y * 0.1}px) rotate(${index * 15}deg)`\n                : `rotate(${index * 15}deg)`\n            }}\n          />\n        ))}\n      </div>\n\n      {/* Content */}\n      <div className=\"ribbons-content\">\n        {children}\n      </div>\n    </div>\n  );\n};\n\nexport default Ribbons;\n"], "mappings": ";;AAAA,SAASA,MAAM,EAAEC,SAAS,QAAQ,OAAO;AACzC,SAASC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,KAAK,EAAEC,OAAO,EAAEC,IAAI,EAAEC,IAAI,QAAQ,KAAK;AAC7E,OAAO,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvB,MAAMC,OAAO,GAAGA,CAAC;EACfC,QAAQ;EACRC,SAAS,GAAG,EAAE;EACdC,KAAK,GAAG,CAAC,CAAC;EACVC,WAAW,GAAG,yBAAyB;EACvCC,WAAW,GAAG,IAAI;EAClBC,WAAW,GAAG;AAChB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,SAAS,GAAGnB,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMoB,WAAW,GAAGpB,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMqB,YAAY,GAAGrB,MAAM,CAAC,IAAI,CAAC;EAEjC,oBACEU,OAAA;IACEY,GAAG,EAAEC,YAAa;IAClBV,SAAS,EAAE,qBAAqBA,SAAS,EAAG;IAC5CC,KAAK,EAAEA,KAAM;IACbU,YAAY,EAAEC,gBAAiB;IAC/BC,YAAY,EAAEC,gBAAiB;IAAAf,QAAA,gBAG/BF,OAAA;MAAKG,SAAS,EAAC,iBAAiB;MAAAD,QAAA,EAC7B,CAAC,GAAGgB,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,kBAC1BrB,OAAA;QAEEG,SAAS,EAAC,QAAQ;QAClBC,KAAK,EAAE;UACL,gBAAgB,EAAE,GAAGiB,KAAK,GAAG,GAAG,GAAG;UACnC,gBAAgB,EAAEhB,WAAW;UAC7B,gBAAgB,EAAE,GAAGiB,WAAW,IAAI;UACpC,gBAAgB,EAAE,GAAGhB,WAAW,IAAI;UACpCiB,OAAO,EAAEC,UAAU,GAAG,GAAG,GAAG,GAAG;UAC/BC,SAAS,EAAED,UAAU,GACjB,aAAaE,aAAa,CAACC,CAAC,GAAG,GAAG,OAAOD,aAAa,CAACE,CAAC,GAAG,GAAG,cAAcP,KAAK,GAAG,EAAE,MAAM,GAC5F,UAAUA,KAAK,GAAG,EAAE;QAC1B;MAAE,GAXGA,KAAK;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAYX,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNhC,OAAA;MAAKG,SAAS,EAAC,iBAAiB;MAAAD,QAAA,EAC7BA;IAAQ;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxB,EAAA,CA9CIP,OAAO;AAAAgC,EAAA,GAAPhC,OAAO;AAgDb,eAAeA,OAAO;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}