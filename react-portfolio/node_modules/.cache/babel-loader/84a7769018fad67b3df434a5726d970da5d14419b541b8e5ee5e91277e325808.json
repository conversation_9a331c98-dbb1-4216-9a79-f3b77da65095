{"ast": null, "code": "/**\n * @license React\n * react.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function () {\n    'use strict';\n\n    /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\n    if (typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' && typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart === 'function') {\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error());\n    }\n    var ReactVersion = '18.3.1';\n\n    // ATTENTION\n    // When adding new symbols to this file,\n    // Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n    // The Symbol used to tag the ReactElement-like types.\n    var REACT_ELEMENT_TYPE = Symbol.for('react.element');\n    var REACT_PORTAL_TYPE = Symbol.for('react.portal');\n    var REACT_FRAGMENT_TYPE = Symbol.for('react.fragment');\n    var REACT_STRICT_MODE_TYPE = Symbol.for('react.strict_mode');\n    var REACT_PROFILER_TYPE = Symbol.for('react.profiler');\n    var REACT_PROVIDER_TYPE = Symbol.for('react.provider');\n    var REACT_CONTEXT_TYPE = Symbol.for('react.context');\n    var REACT_FORWARD_REF_TYPE = Symbol.for('react.forward_ref');\n    var REACT_SUSPENSE_TYPE = Symbol.for('react.suspense');\n    var REACT_SUSPENSE_LIST_TYPE = Symbol.for('react.suspense_list');\n    var REACT_MEMO_TYPE = Symbol.for('react.memo');\n    var REACT_LAZY_TYPE = Symbol.for('react.lazy');\n    var REACT_OFFSCREEN_TYPE = Symbol.for('react.offscreen');\n    var MAYBE_ITERATOR_SYMBOL = Symbol.iterator;\n    var FAUX_ITERATOR_SYMBOL = '@@iterator';\n    function getIteratorFn(maybeIterable) {\n      if (maybeIterable === null || typeof maybeIterable !== 'object') {\n        return null;\n      }\n      var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n      if (typeof maybeIterator === 'function') {\n        return maybeIterator;\n      }\n      return null;\n    }\n\n    /**\n     * Keeps track of the current dispatcher.\n     */\n    var ReactCurrentDispatcher = {\n      /**\n       * @internal\n       * @type {ReactComponent}\n       */\n      current: null\n    };\n\n    /**\n     * Keeps track of the current batch's configuration such as how long an update\n     * should suspend for if it needs to.\n     */\n    var ReactCurrentBatchConfig = {\n      transition: null\n    };\n    var ReactCurrentActQueue = {\n      current: null,\n      // Used to reproduce behavior of `batchedUpdates` in legacy mode.\n      isBatchingLegacy: false,\n      didScheduleLegacyUpdate: false\n    };\n\n    /**\n     * Keeps track of the current owner.\n     *\n     * The current owner is the component who should own any components that are\n     * currently being constructed.\n     */\n    var ReactCurrentOwner = {\n      /**\n       * @internal\n       * @type {ReactComponent}\n       */\n      current: null\n    };\n    var ReactDebugCurrentFrame = {};\n    var currentExtraStackFrame = null;\n    function setExtraStackFrame(stack) {\n      {\n        currentExtraStackFrame = stack;\n      }\n    }\n    {\n      ReactDebugCurrentFrame.setExtraStackFrame = function (stack) {\n        {\n          currentExtraStackFrame = stack;\n        }\n      }; // Stack implementation injected by the current renderer.\n\n      ReactDebugCurrentFrame.getCurrentStack = null;\n      ReactDebugCurrentFrame.getStackAddendum = function () {\n        var stack = ''; // Add an extra top frame while an element is being validated\n\n        if (currentExtraStackFrame) {\n          stack += currentExtraStackFrame;\n        } // Delegate to the injected renderer-specific implementation\n\n        var impl = ReactDebugCurrentFrame.getCurrentStack;\n        if (impl) {\n          stack += impl() || '';\n        }\n        return stack;\n      };\n    }\n\n    // -----------------------------------------------------------------------------\n\n    var enableScopeAPI = false; // Experimental Create Event Handle API.\n    var enableCacheElement = false;\n    var enableTransitionTracing = false; // No known bugs, but needs performance testing\n\n    var enableLegacyHidden = false; // Enables unstable_avoidThisFallback feature in Fiber\n    // stuff. Intended to enable React core members to more easily debug scheduling\n    // issues in DEV builds.\n\n    var enableDebugTracing = false; // Track which Fiber(s) schedule render work.\n\n    var ReactSharedInternals = {\n      ReactCurrentDispatcher: ReactCurrentDispatcher,\n      ReactCurrentBatchConfig: ReactCurrentBatchConfig,\n      ReactCurrentOwner: ReactCurrentOwner\n    };\n    {\n      ReactSharedInternals.ReactDebugCurrentFrame = ReactDebugCurrentFrame;\n      ReactSharedInternals.ReactCurrentActQueue = ReactCurrentActQueue;\n    }\n\n    // by calls to these methods by a Babel plugin.\n    //\n    // In PROD (or in packages without access to React internals),\n    // they are left as they are instead.\n\n    function warn(format) {\n      {\n        {\n          for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n            args[_key - 1] = arguments[_key];\n          }\n          printWarning('warn', format, args);\n        }\n      }\n    }\n    function error(format) {\n      {\n        {\n          for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n            args[_key2 - 1] = arguments[_key2];\n          }\n          printWarning('error', format, args);\n        }\n      }\n    }\n    function printWarning(level, format, args) {\n      // When changing this logic, you might want to also\n      // update consoleWithStackDev.www.js as well.\n      {\n        var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n        var stack = ReactDebugCurrentFrame.getStackAddendum();\n        if (stack !== '') {\n          format += '%s';\n          args = args.concat([stack]);\n        } // eslint-disable-next-line react-internal/safe-string-coercion\n\n        var argsWithFormat = args.map(function (item) {\n          return String(item);\n        }); // Careful: RN currently depends on this prefix\n\n        argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n        // breaks IE9: https://github.com/facebook/react/issues/13610\n        // eslint-disable-next-line react-internal/no-production-logging\n\n        Function.prototype.apply.call(console[level], console, argsWithFormat);\n      }\n    }\n    var didWarnStateUpdateForUnmountedComponent = {};\n    function warnNoop(publicInstance, callerName) {\n      {\n        var _constructor = publicInstance.constructor;\n        var componentName = _constructor && (_constructor.displayName || _constructor.name) || 'ReactClass';\n        var warningKey = componentName + \".\" + callerName;\n        if (didWarnStateUpdateForUnmountedComponent[warningKey]) {\n          return;\n        }\n        error(\"Can't call %s on a component that is not yet mounted. \" + 'This is a no-op, but it might indicate a bug in your application. ' + 'Instead, assign to `this.state` directly or define a `state = {};` ' + 'class property with the desired state in the %s component.', callerName, componentName);\n        didWarnStateUpdateForUnmountedComponent[warningKey] = true;\n      }\n    }\n    /**\n     * This is the abstract API for an update queue.\n     */\n\n    var ReactNoopUpdateQueue = {\n      /**\n       * Checks whether or not this composite component is mounted.\n       * @param {ReactClass} publicInstance The instance we want to test.\n       * @return {boolean} True if mounted, false otherwise.\n       * @protected\n       * @final\n       */\n      isMounted: function (publicInstance) {\n        return false;\n      },\n      /**\n       * Forces an update. This should only be invoked when it is known with\n       * certainty that we are **not** in a DOM transaction.\n       *\n       * You may want to call this when you know that some deeper aspect of the\n       * component's state has changed but `setState` was not called.\n       *\n       * This will not invoke `shouldComponentUpdate`, but it will invoke\n       * `componentWillUpdate` and `componentDidUpdate`.\n       *\n       * @param {ReactClass} publicInstance The instance that should rerender.\n       * @param {?function} callback Called after component is updated.\n       * @param {?string} callerName name of the calling function in the public API.\n       * @internal\n       */\n      enqueueForceUpdate: function (publicInstance, callback, callerName) {\n        warnNoop(publicInstance, 'forceUpdate');\n      },\n      /**\n       * Replaces all of the state. Always use this or `setState` to mutate state.\n       * You should treat `this.state` as immutable.\n       *\n       * There is no guarantee that `this.state` will be immediately updated, so\n       * accessing `this.state` after calling this method may return the old value.\n       *\n       * @param {ReactClass} publicInstance The instance that should rerender.\n       * @param {object} completeState Next state.\n       * @param {?function} callback Called after component is updated.\n       * @param {?string} callerName name of the calling function in the public API.\n       * @internal\n       */\n      enqueueReplaceState: function (publicInstance, completeState, callback, callerName) {\n        warnNoop(publicInstance, 'replaceState');\n      },\n      /**\n       * Sets a subset of the state. This only exists because _pendingState is\n       * internal. This provides a merging strategy that is not available to deep\n       * properties which is confusing. TODO: Expose pendingState or don't use it\n       * during the merge.\n       *\n       * @param {ReactClass} publicInstance The instance that should rerender.\n       * @param {object} partialState Next partial state to be merged with state.\n       * @param {?function} callback Called after component is updated.\n       * @param {?string} Name of the calling function in the public API.\n       * @internal\n       */\n      enqueueSetState: function (publicInstance, partialState, callback, callerName) {\n        warnNoop(publicInstance, 'setState');\n      }\n    };\n    var assign = Object.assign;\n    var emptyObject = {};\n    {\n      Object.freeze(emptyObject);\n    }\n    /**\n     * Base class helpers for the updating state of a component.\n     */\n\n    function Component(props, context, updater) {\n      this.props = props;\n      this.context = context; // If a component has string refs, we will assign a different object later.\n\n      this.refs = emptyObject; // We initialize the default updater but the real one gets injected by the\n      // renderer.\n\n      this.updater = updater || ReactNoopUpdateQueue;\n    }\n    Component.prototype.isReactComponent = {};\n    /**\n     * Sets a subset of the state. Always use this to mutate\n     * state. You should treat `this.state` as immutable.\n     *\n     * There is no guarantee that `this.state` will be immediately updated, so\n     * accessing `this.state` after calling this method may return the old value.\n     *\n     * There is no guarantee that calls to `setState` will run synchronously,\n     * as they may eventually be batched together.  You can provide an optional\n     * callback that will be executed when the call to setState is actually\n     * completed.\n     *\n     * When a function is provided to setState, it will be called at some point in\n     * the future (not synchronously). It will be called with the up to date\n     * component arguments (state, props, context). These values can be different\n     * from this.* because your function may be called after receiveProps but before\n     * shouldComponentUpdate, and this new state, props, and context will not yet be\n     * assigned to this.\n     *\n     * @param {object|function} partialState Next partial state or function to\n     *        produce next partial state to be merged with current state.\n     * @param {?function} callback Called after state is updated.\n     * @final\n     * @protected\n     */\n\n    Component.prototype.setState = function (partialState, callback) {\n      if (typeof partialState !== 'object' && typeof partialState !== 'function' && partialState != null) {\n        throw new Error('setState(...): takes an object of state variables to update or a ' + 'function which returns an object of state variables.');\n      }\n      this.updater.enqueueSetState(this, partialState, callback, 'setState');\n    };\n    /**\n     * Forces an update. This should only be invoked when it is known with\n     * certainty that we are **not** in a DOM transaction.\n     *\n     * You may want to call this when you know that some deeper aspect of the\n     * component's state has changed but `setState` was not called.\n     *\n     * This will not invoke `shouldComponentUpdate`, but it will invoke\n     * `componentWillUpdate` and `componentDidUpdate`.\n     *\n     * @param {?function} callback Called after update is complete.\n     * @final\n     * @protected\n     */\n\n    Component.prototype.forceUpdate = function (callback) {\n      this.updater.enqueueForceUpdate(this, callback, 'forceUpdate');\n    };\n    /**\n     * Deprecated APIs. These APIs used to exist on classic React classes but since\n     * we would like to deprecate them, we're not going to move them over to this\n     * modern base class. Instead, we define a getter that warns if it's accessed.\n     */\n\n    {\n      var deprecatedAPIs = {\n        isMounted: ['isMounted', 'Instead, make sure to clean up subscriptions and pending requests in ' + 'componentWillUnmount to prevent memory leaks.'],\n        replaceState: ['replaceState', 'Refactor your code to use setState instead (see ' + 'https://github.com/facebook/react/issues/3236).']\n      };\n      var defineDeprecationWarning = function (methodName, info) {\n        Object.defineProperty(Component.prototype, methodName, {\n          get: function () {\n            warn('%s(...) is deprecated in plain JavaScript React classes. %s', info[0], info[1]);\n            return undefined;\n          }\n        });\n      };\n      for (var fnName in deprecatedAPIs) {\n        if (deprecatedAPIs.hasOwnProperty(fnName)) {\n          defineDeprecationWarning(fnName, deprecatedAPIs[fnName]);\n        }\n      }\n    }\n    function ComponentDummy() {}\n    ComponentDummy.prototype = Component.prototype;\n    /**\n     * Convenience component with default shallow equality check for sCU.\n     */\n\n    function PureComponent(props, context, updater) {\n      this.props = props;\n      this.context = context; // If a component has string refs, we will assign a different object later.\n\n      this.refs = emptyObject;\n      this.updater = updater || ReactNoopUpdateQueue;\n    }\n    var pureComponentPrototype = PureComponent.prototype = new ComponentDummy();\n    pureComponentPrototype.constructor = PureComponent; // Avoid an extra prototype jump for these methods.\n\n    assign(pureComponentPrototype, Component.prototype);\n    pureComponentPrototype.isPureReactComponent = true;\n\n    // an immutable object with a single mutable value\n    function createRef() {\n      var refObject = {\n        current: null\n      };\n      {\n        Object.seal(refObject);\n      }\n      return refObject;\n    }\n    var isArrayImpl = Array.isArray; // eslint-disable-next-line no-redeclare\n\n    function isArray(a) {\n      return isArrayImpl(a);\n    }\n\n    /*\n     * The `'' + value` pattern (used in in perf-sensitive code) throws for Symbol\n     * and Temporal.* types. See https://github.com/facebook/react/pull/22064.\n     *\n     * The functions in this module will throw an easier-to-understand,\n     * easier-to-debug exception with a clear errors message message explaining the\n     * problem. (Instead of a confusing exception thrown inside the implementation\n     * of the `value` object).\n     */\n    // $FlowFixMe only called in DEV, so void return is not possible.\n    function typeName(value) {\n      {\n        // toStringTag is needed for namespaced types like Temporal.Instant\n        var hasToStringTag = typeof Symbol === 'function' && Symbol.toStringTag;\n        var type = hasToStringTag && value[Symbol.toStringTag] || value.constructor.name || 'Object';\n        return type;\n      }\n    } // $FlowFixMe only called in DEV, so void return is not possible.\n\n    function willCoercionThrow(value) {\n      {\n        try {\n          testStringCoercion(value);\n          return false;\n        } catch (e) {\n          return true;\n        }\n      }\n    }\n    function testStringCoercion(value) {\n      // If you ended up here by following an exception call stack, here's what's\n      // happened: you supplied an object or symbol value to React (as a prop, key,\n      // DOM attribute, CSS property, string ref, etc.) and when React tried to\n      // coerce it to a string using `'' + value`, an exception was thrown.\n      //\n      // The most common types that will cause this exception are `Symbol` instances\n      // and Temporal objects like `Temporal.Instant`. But any object that has a\n      // `valueOf` or `[Symbol.toPrimitive]` method that throws will also cause this\n      // exception. (Library authors do this to prevent users from using built-in\n      // numeric operators like `+` or comparison operators like `>=` because custom\n      // methods are needed to perform accurate arithmetic or comparison.)\n      //\n      // To fix the problem, coerce this object or symbol value to a string before\n      // passing it to React. The most reliable way is usually `String(value)`.\n      //\n      // To find which value is throwing, check the browser or debugger console.\n      // Before this exception was thrown, there should be `console.error` output\n      // that shows the type (Symbol, Temporal.PlainDate, etc.) that caused the\n      // problem and how that type was used: key, atrribute, input value prop, etc.\n      // In most cases, this console output also shows the component and its\n      // ancestor components where the exception happened.\n      //\n      // eslint-disable-next-line react-internal/safe-string-coercion\n      return '' + value;\n    }\n    function checkKeyStringCoercion(value) {\n      {\n        if (willCoercionThrow(value)) {\n          error('The provided key is an unsupported type %s.' + ' This value must be coerced to a string before before using it here.', typeName(value));\n          return testStringCoercion(value); // throw (to help callers find troubleshooting comments)\n        }\n      }\n    }\n    function getWrappedName(outerType, innerType, wrapperName) {\n      var displayName = outerType.displayName;\n      if (displayName) {\n        return displayName;\n      }\n      var functionName = innerType.displayName || innerType.name || '';\n      return functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName;\n    } // Keep in sync with react-reconciler/getComponentNameFromFiber\n\n    function getContextName(type) {\n      return type.displayName || 'Context';\n    } // Note that the reconciler package should generally prefer to use getComponentNameFromFiber() instead.\n\n    function getComponentNameFromType(type) {\n      if (type == null) {\n        // Host root, text node or just invalid type.\n        return null;\n      }\n      {\n        if (typeof type.tag === 'number') {\n          error('Received an unexpected object in getComponentNameFromType(). ' + 'This is likely a bug in React. Please file an issue.');\n        }\n      }\n      if (typeof type === 'function') {\n        return type.displayName || type.name || null;\n      }\n      if (typeof type === 'string') {\n        return type;\n      }\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return 'Fragment';\n        case REACT_PORTAL_TYPE:\n          return 'Portal';\n        case REACT_PROFILER_TYPE:\n          return 'Profiler';\n        case REACT_STRICT_MODE_TYPE:\n          return 'StrictMode';\n        case REACT_SUSPENSE_TYPE:\n          return 'Suspense';\n        case REACT_SUSPENSE_LIST_TYPE:\n          return 'SuspenseList';\n      }\n      if (typeof type === 'object') {\n        switch (type.$$typeof) {\n          case REACT_CONTEXT_TYPE:\n            var context = type;\n            return getContextName(context) + '.Consumer';\n          case REACT_PROVIDER_TYPE:\n            var provider = type;\n            return getContextName(provider._context) + '.Provider';\n          case REACT_FORWARD_REF_TYPE:\n            return getWrappedName(type, type.render, 'ForwardRef');\n          case REACT_MEMO_TYPE:\n            var outerName = type.displayName || null;\n            if (outerName !== null) {\n              return outerName;\n            }\n            return getComponentNameFromType(type.type) || 'Memo';\n          case REACT_LAZY_TYPE:\n            {\n              var lazyComponent = type;\n              var payload = lazyComponent._payload;\n              var init = lazyComponent._init;\n              try {\n                return getComponentNameFromType(init(payload));\n              } catch (x) {\n                return null;\n              }\n            }\n\n          // eslint-disable-next-line no-fallthrough\n        }\n      }\n      return null;\n    }\n    var hasOwnProperty = Object.prototype.hasOwnProperty;\n    var RESERVED_PROPS = {\n      key: true,\n      ref: true,\n      __self: true,\n      __source: true\n    };\n    var specialPropKeyWarningShown, specialPropRefWarningShown, didWarnAboutStringRefs;\n    {\n      didWarnAboutStringRefs = {};\n    }\n    function hasValidRef(config) {\n      {\n        if (hasOwnProperty.call(config, 'ref')) {\n          var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\n          if (getter && getter.isReactWarning) {\n            return false;\n          }\n        }\n      }\n      return config.ref !== undefined;\n    }\n    function hasValidKey(config) {\n      {\n        if (hasOwnProperty.call(config, 'key')) {\n          var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\n          if (getter && getter.isReactWarning) {\n            return false;\n          }\n        }\n      }\n      return config.key !== undefined;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      var warnAboutAccessingKey = function () {\n        {\n          if (!specialPropKeyWarningShown) {\n            specialPropKeyWarningShown = true;\n            error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n          }\n        }\n      };\n      warnAboutAccessingKey.isReactWarning = true;\n      Object.defineProperty(props, 'key', {\n        get: warnAboutAccessingKey,\n        configurable: true\n      });\n    }\n    function defineRefPropWarningGetter(props, displayName) {\n      var warnAboutAccessingRef = function () {\n        {\n          if (!specialPropRefWarningShown) {\n            specialPropRefWarningShown = true;\n            error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n          }\n        }\n      };\n      warnAboutAccessingRef.isReactWarning = true;\n      Object.defineProperty(props, 'ref', {\n        get: warnAboutAccessingRef,\n        configurable: true\n      });\n    }\n    function warnIfStringRefCannotBeAutoConverted(config) {\n      {\n        if (typeof config.ref === 'string' && ReactCurrentOwner.current && config.__self && ReactCurrentOwner.current.stateNode !== config.__self) {\n          var componentName = getComponentNameFromType(ReactCurrentOwner.current.type);\n          if (!didWarnAboutStringRefs[componentName]) {\n            error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', componentName, config.ref);\n            didWarnAboutStringRefs[componentName] = true;\n          }\n        }\n      }\n    }\n    /**\n     * Factory method to create a new React element. This no longer adheres to\n     * the class pattern, so do not use new to call it. Also, instanceof check\n     * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n     * if something is a React Element.\n     *\n     * @param {*} type\n     * @param {*} props\n     * @param {*} key\n     * @param {string|object} ref\n     * @param {*} owner\n     * @param {*} self A *temporary* helper to detect places where `this` is\n     * different from the `owner` when React.createElement is called, so that we\n     * can warn. We want to get rid of owner and replace string `ref`s with arrow\n     * functions, and as long as `this` and owner are the same, there will be no\n     * change in behavior.\n     * @param {*} source An annotation object (added by a transpiler or otherwise)\n     * indicating filename, line number, and/or other information.\n     * @internal\n     */\n\n    var ReactElement = function (type, key, ref, self, source, owner, props) {\n      var element = {\n        // This tag allows us to uniquely identify this as a React Element\n        $$typeof: REACT_ELEMENT_TYPE,\n        // Built-in properties that belong on the element\n        type: type,\n        key: key,\n        ref: ref,\n        props: props,\n        // Record the component responsible for creating this element.\n        _owner: owner\n      };\n      {\n        // The validation flag is currently mutative. We put it on\n        // an external backing store so that we can freeze the whole object.\n        // This can be replaced with a WeakMap once they are implemented in\n        // commonly used development environments.\n        element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n        // the validation flag non-enumerable (where possible, which should\n        // include every environment we run tests in), so the test framework\n        // ignores it.\n\n        Object.defineProperty(element._store, 'validated', {\n          configurable: false,\n          enumerable: false,\n          writable: true,\n          value: false\n        }); // self and source are DEV only properties.\n\n        Object.defineProperty(element, '_self', {\n          configurable: false,\n          enumerable: false,\n          writable: false,\n          value: self\n        }); // Two elements created in two different places should be considered\n        // equal for testing purposes and therefore we hide it from enumeration.\n\n        Object.defineProperty(element, '_source', {\n          configurable: false,\n          enumerable: false,\n          writable: false,\n          value: source\n        });\n        if (Object.freeze) {\n          Object.freeze(element.props);\n          Object.freeze(element);\n        }\n      }\n      return element;\n    };\n    /**\n     * Create and return a new ReactElement of the given type.\n     * See https://reactjs.org/docs/react-api.html#createelement\n     */\n\n    function createElement(type, config, children) {\n      var propName; // Reserved names are extracted\n\n      var props = {};\n      var key = null;\n      var ref = null;\n      var self = null;\n      var source = null;\n      if (config != null) {\n        if (hasValidRef(config)) {\n          ref = config.ref;\n          {\n            warnIfStringRefCannotBeAutoConverted(config);\n          }\n        }\n        if (hasValidKey(config)) {\n          {\n            checkKeyStringCoercion(config.key);\n          }\n          key = '' + config.key;\n        }\n        self = config.__self === undefined ? null : config.__self;\n        source = config.__source === undefined ? null : config.__source; // Remaining properties are added to a new props object\n\n        for (propName in config) {\n          if (hasOwnProperty.call(config, propName) && !RESERVED_PROPS.hasOwnProperty(propName)) {\n            props[propName] = config[propName];\n          }\n        }\n      } // Children can be more than one argument, and those are transferred onto\n      // the newly allocated props object.\n\n      var childrenLength = arguments.length - 2;\n      if (childrenLength === 1) {\n        props.children = children;\n      } else if (childrenLength > 1) {\n        var childArray = Array(childrenLength);\n        for (var i = 0; i < childrenLength; i++) {\n          childArray[i] = arguments[i + 2];\n        }\n        {\n          if (Object.freeze) {\n            Object.freeze(childArray);\n          }\n        }\n        props.children = childArray;\n      } // Resolve default props\n\n      if (type && type.defaultProps) {\n        var defaultProps = type.defaultProps;\n        for (propName in defaultProps) {\n          if (props[propName] === undefined) {\n            props[propName] = defaultProps[propName];\n          }\n        }\n      }\n      {\n        if (key || ref) {\n          var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\n          if (key) {\n            defineKeyPropWarningGetter(props, displayName);\n          }\n          if (ref) {\n            defineRefPropWarningGetter(props, displayName);\n          }\n        }\n      }\n      return ReactElement(type, key, ref, self, source, ReactCurrentOwner.current, props);\n    }\n    function cloneAndReplaceKey(oldElement, newKey) {\n      var newElement = ReactElement(oldElement.type, newKey, oldElement.ref, oldElement._self, oldElement._source, oldElement._owner, oldElement.props);\n      return newElement;\n    }\n    /**\n     * Clone and return a new ReactElement using element as the starting point.\n     * See https://reactjs.org/docs/react-api.html#cloneelement\n     */\n\n    function cloneElement(element, config, children) {\n      if (element === null || element === undefined) {\n        throw new Error(\"React.cloneElement(...): The argument must be a React element, but you passed \" + element + \".\");\n      }\n      var propName; // Original props are copied\n\n      var props = assign({}, element.props); // Reserved names are extracted\n\n      var key = element.key;\n      var ref = element.ref; // Self is preserved since the owner is preserved.\n\n      var self = element._self; // Source is preserved since cloneElement is unlikely to be targeted by a\n      // transpiler, and the original source is probably a better indicator of the\n      // true owner.\n\n      var source = element._source; // Owner will be preserved, unless ref is overridden\n\n      var owner = element._owner;\n      if (config != null) {\n        if (hasValidRef(config)) {\n          // Silently steal the ref from the parent.\n          ref = config.ref;\n          owner = ReactCurrentOwner.current;\n        }\n        if (hasValidKey(config)) {\n          {\n            checkKeyStringCoercion(config.key);\n          }\n          key = '' + config.key;\n        } // Remaining properties override existing props\n\n        var defaultProps;\n        if (element.type && element.type.defaultProps) {\n          defaultProps = element.type.defaultProps;\n        }\n        for (propName in config) {\n          if (hasOwnProperty.call(config, propName) && !RESERVED_PROPS.hasOwnProperty(propName)) {\n            if (config[propName] === undefined && defaultProps !== undefined) {\n              // Resolve default props\n              props[propName] = defaultProps[propName];\n            } else {\n              props[propName] = config[propName];\n            }\n          }\n        }\n      } // Children can be more than one argument, and those are transferred onto\n      // the newly allocated props object.\n\n      var childrenLength = arguments.length - 2;\n      if (childrenLength === 1) {\n        props.children = children;\n      } else if (childrenLength > 1) {\n        var childArray = Array(childrenLength);\n        for (var i = 0; i < childrenLength; i++) {\n          childArray[i] = arguments[i + 2];\n        }\n        props.children = childArray;\n      }\n      return ReactElement(element.type, key, ref, self, source, owner, props);\n    }\n    /**\n     * Verifies the object is a ReactElement.\n     * See https://reactjs.org/docs/react-api.html#isvalidelement\n     * @param {?object} object\n     * @return {boolean} True if `object` is a ReactElement.\n     * @final\n     */\n\n    function isValidElement(object) {\n      return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n    }\n    var SEPARATOR = '.';\n    var SUBSEPARATOR = ':';\n    /**\n     * Escape and wrap key so it is safe to use as a reactid\n     *\n     * @param {string} key to be escaped.\n     * @return {string} the escaped key.\n     */\n\n    function escape(key) {\n      var escapeRegex = /[=:]/g;\n      var escaperLookup = {\n        '=': '=0',\n        ':': '=2'\n      };\n      var escapedString = key.replace(escapeRegex, function (match) {\n        return escaperLookup[match];\n      });\n      return '$' + escapedString;\n    }\n    /**\n     * TODO: Test that a single child and an array with one item have the same key\n     * pattern.\n     */\n\n    var didWarnAboutMaps = false;\n    var userProvidedKeyEscapeRegex = /\\/+/g;\n    function escapeUserProvidedKey(text) {\n      return text.replace(userProvidedKeyEscapeRegex, '$&/');\n    }\n    /**\n     * Generate a key string that identifies a element within a set.\n     *\n     * @param {*} element A element that could contain a manual key.\n     * @param {number} index Index that is used if a manual key is not provided.\n     * @return {string}\n     */\n\n    function getElementKey(element, index) {\n      // Do some typechecking here since we call this blindly. We want to ensure\n      // that we don't block potential future ES APIs.\n      if (typeof element === 'object' && element !== null && element.key != null) {\n        // Explicit key\n        {\n          checkKeyStringCoercion(element.key);\n        }\n        return escape('' + element.key);\n      } // Implicit key determined by the index in the set\n\n      return index.toString(36);\n    }\n    function mapIntoArray(children, array, escapedPrefix, nameSoFar, callback) {\n      var type = typeof children;\n      if (type === 'undefined' || type === 'boolean') {\n        // All of the above are perceived as null.\n        children = null;\n      }\n      var invokeCallback = false;\n      if (children === null) {\n        invokeCallback = true;\n      } else {\n        switch (type) {\n          case 'string':\n          case 'number':\n            invokeCallback = true;\n            break;\n          case 'object':\n            switch (children.$$typeof) {\n              case REACT_ELEMENT_TYPE:\n              case REACT_PORTAL_TYPE:\n                invokeCallback = true;\n            }\n        }\n      }\n      if (invokeCallback) {\n        var _child = children;\n        var mappedChild = callback(_child); // If it's the only child, treat the name as if it was wrapped in an array\n        // so that it's consistent if the number of children grows:\n\n        var childKey = nameSoFar === '' ? SEPARATOR + getElementKey(_child, 0) : nameSoFar;\n        if (isArray(mappedChild)) {\n          var escapedChildKey = '';\n          if (childKey != null) {\n            escapedChildKey = escapeUserProvidedKey(childKey) + '/';\n          }\n          mapIntoArray(mappedChild, array, escapedChildKey, '', function (c) {\n            return c;\n          });\n        } else if (mappedChild != null) {\n          if (isValidElement(mappedChild)) {\n            {\n              // The `if` statement here prevents auto-disabling of the safe\n              // coercion ESLint rule, so we must manually disable it below.\n              // $FlowFixMe Flow incorrectly thinks React.Portal doesn't have a key\n              if (mappedChild.key && (!_child || _child.key !== mappedChild.key)) {\n                checkKeyStringCoercion(mappedChild.key);\n              }\n            }\n            mappedChild = cloneAndReplaceKey(mappedChild,\n            // Keep both the (mapped) and old keys if they differ, just as\n            // traverseAllChildren used to do for objects as children\n            escapedPrefix + (\n            // $FlowFixMe Flow incorrectly thinks React.Portal doesn't have a key\n            mappedChild.key && (!_child || _child.key !== mappedChild.key) ?\n            // $FlowFixMe Flow incorrectly thinks existing element's key can be a number\n            // eslint-disable-next-line react-internal/safe-string-coercion\n            escapeUserProvidedKey('' + mappedChild.key) + '/' : '') + childKey);\n          }\n          array.push(mappedChild);\n        }\n        return 1;\n      }\n      var child;\n      var nextName;\n      var subtreeCount = 0; // Count of children found in the current subtree.\n\n      var nextNamePrefix = nameSoFar === '' ? SEPARATOR : nameSoFar + SUBSEPARATOR;\n      if (isArray(children)) {\n        for (var i = 0; i < children.length; i++) {\n          child = children[i];\n          nextName = nextNamePrefix + getElementKey(child, i);\n          subtreeCount += mapIntoArray(child, array, escapedPrefix, nextName, callback);\n        }\n      } else {\n        var iteratorFn = getIteratorFn(children);\n        if (typeof iteratorFn === 'function') {\n          var iterableChildren = children;\n          {\n            // Warn about using Maps as children\n            if (iteratorFn === iterableChildren.entries) {\n              if (!didWarnAboutMaps) {\n                warn('Using Maps as children is not supported. ' + 'Use an array of keyed ReactElements instead.');\n              }\n              didWarnAboutMaps = true;\n            }\n          }\n          var iterator = iteratorFn.call(iterableChildren);\n          var step;\n          var ii = 0;\n          while (!(step = iterator.next()).done) {\n            child = step.value;\n            nextName = nextNamePrefix + getElementKey(child, ii++);\n            subtreeCount += mapIntoArray(child, array, escapedPrefix, nextName, callback);\n          }\n        } else if (type === 'object') {\n          // eslint-disable-next-line react-internal/safe-string-coercion\n          var childrenString = String(children);\n          throw new Error(\"Objects are not valid as a React child (found: \" + (childrenString === '[object Object]' ? 'object with keys {' + Object.keys(children).join(', ') + '}' : childrenString) + \"). \" + 'If you meant to render a collection of children, use an array ' + 'instead.');\n        }\n      }\n      return subtreeCount;\n    }\n\n    /**\n     * Maps children that are typically specified as `props.children`.\n     *\n     * See https://reactjs.org/docs/react-api.html#reactchildrenmap\n     *\n     * The provided mapFunction(child, index) will be called for each\n     * leaf child.\n     *\n     * @param {?*} children Children tree container.\n     * @param {function(*, int)} func The map function.\n     * @param {*} context Context for mapFunction.\n     * @return {object} Object containing the ordered map of results.\n     */\n    function mapChildren(children, func, context) {\n      if (children == null) {\n        return children;\n      }\n      var result = [];\n      var count = 0;\n      mapIntoArray(children, result, '', '', function (child) {\n        return func.call(context, child, count++);\n      });\n      return result;\n    }\n    /**\n     * Count the number of children that are typically specified as\n     * `props.children`.\n     *\n     * See https://reactjs.org/docs/react-api.html#reactchildrencount\n     *\n     * @param {?*} children Children tree container.\n     * @return {number} The number of children.\n     */\n\n    function countChildren(children) {\n      var n = 0;\n      mapChildren(children, function () {\n        n++; // Don't return anything\n      });\n      return n;\n    }\n\n    /**\n     * Iterates through children that are typically specified as `props.children`.\n     *\n     * See https://reactjs.org/docs/react-api.html#reactchildrenforeach\n     *\n     * The provided forEachFunc(child, index) will be called for each\n     * leaf child.\n     *\n     * @param {?*} children Children tree container.\n     * @param {function(*, int)} forEachFunc\n     * @param {*} forEachContext Context for forEachContext.\n     */\n    function forEachChildren(children, forEachFunc, forEachContext) {\n      mapChildren(children, function () {\n        forEachFunc.apply(this, arguments); // Don't return anything.\n      }, forEachContext);\n    }\n    /**\n     * Flatten a children object (typically specified as `props.children`) and\n     * return an array with appropriately re-keyed children.\n     *\n     * See https://reactjs.org/docs/react-api.html#reactchildrentoarray\n     */\n\n    function toArray(children) {\n      return mapChildren(children, function (child) {\n        return child;\n      }) || [];\n    }\n    /**\n     * Returns the first child in a collection of children and verifies that there\n     * is only one child in the collection.\n     *\n     * See https://reactjs.org/docs/react-api.html#reactchildrenonly\n     *\n     * The current implementation of this function assumes that a single child gets\n     * passed without a wrapper, but the purpose of this helper function is to\n     * abstract away the particular structure of children.\n     *\n     * @param {?object} children Child collection structure.\n     * @return {ReactElement} The first and only `ReactElement` contained in the\n     * structure.\n     */\n\n    function onlyChild(children) {\n      if (!isValidElement(children)) {\n        throw new Error('React.Children.only expected to receive a single React element child.');\n      }\n      return children;\n    }\n    function createContext(defaultValue) {\n      // TODO: Second argument used to be an optional `calculateChangedBits`\n      // function. Warn to reserve for future use?\n      var context = {\n        $$typeof: REACT_CONTEXT_TYPE,\n        // As a workaround to support multiple concurrent renderers, we categorize\n        // some renderers as primary and others as secondary. We only expect\n        // there to be two concurrent renderers at most: React Native (primary) and\n        // Fabric (secondary); React DOM (primary) and React ART (secondary).\n        // Secondary renderers store their context values on separate fields.\n        _currentValue: defaultValue,\n        _currentValue2: defaultValue,\n        // Used to track how many concurrent renderers this context currently\n        // supports within in a single renderer. Such as parallel server rendering.\n        _threadCount: 0,\n        // These are circular\n        Provider: null,\n        Consumer: null,\n        // Add these to use same hidden class in VM as ServerContext\n        _defaultValue: null,\n        _globalName: null\n      };\n      context.Provider = {\n        $$typeof: REACT_PROVIDER_TYPE,\n        _context: context\n      };\n      var hasWarnedAboutUsingNestedContextConsumers = false;\n      var hasWarnedAboutUsingConsumerProvider = false;\n      var hasWarnedAboutDisplayNameOnConsumer = false;\n      {\n        // A separate object, but proxies back to the original context object for\n        // backwards compatibility. It has a different $$typeof, so we can properly\n        // warn for the incorrect usage of Context as a Consumer.\n        var Consumer = {\n          $$typeof: REACT_CONTEXT_TYPE,\n          _context: context\n        }; // $FlowFixMe: Flow complains about not setting a value, which is intentional here\n\n        Object.defineProperties(Consumer, {\n          Provider: {\n            get: function () {\n              if (!hasWarnedAboutUsingConsumerProvider) {\n                hasWarnedAboutUsingConsumerProvider = true;\n                error('Rendering <Context.Consumer.Provider> is not supported and will be removed in ' + 'a future major release. Did you mean to render <Context.Provider> instead?');\n              }\n              return context.Provider;\n            },\n            set: function (_Provider) {\n              context.Provider = _Provider;\n            }\n          },\n          _currentValue: {\n            get: function () {\n              return context._currentValue;\n            },\n            set: function (_currentValue) {\n              context._currentValue = _currentValue;\n            }\n          },\n          _currentValue2: {\n            get: function () {\n              return context._currentValue2;\n            },\n            set: function (_currentValue2) {\n              context._currentValue2 = _currentValue2;\n            }\n          },\n          _threadCount: {\n            get: function () {\n              return context._threadCount;\n            },\n            set: function (_threadCount) {\n              context._threadCount = _threadCount;\n            }\n          },\n          Consumer: {\n            get: function () {\n              if (!hasWarnedAboutUsingNestedContextConsumers) {\n                hasWarnedAboutUsingNestedContextConsumers = true;\n                error('Rendering <Context.Consumer.Consumer> is not supported and will be removed in ' + 'a future major release. Did you mean to render <Context.Consumer> instead?');\n              }\n              return context.Consumer;\n            }\n          },\n          displayName: {\n            get: function () {\n              return context.displayName;\n            },\n            set: function (displayName) {\n              if (!hasWarnedAboutDisplayNameOnConsumer) {\n                warn('Setting `displayName` on Context.Consumer has no effect. ' + \"You should set it directly on the context with Context.displayName = '%s'.\", displayName);\n                hasWarnedAboutDisplayNameOnConsumer = true;\n              }\n            }\n          }\n        }); // $FlowFixMe: Flow complains about missing properties because it doesn't understand defineProperty\n\n        context.Consumer = Consumer;\n      }\n      {\n        context._currentRenderer = null;\n        context._currentRenderer2 = null;\n      }\n      return context;\n    }\n    var Uninitialized = -1;\n    var Pending = 0;\n    var Resolved = 1;\n    var Rejected = 2;\n    function lazyInitializer(payload) {\n      if (payload._status === Uninitialized) {\n        var ctor = payload._result;\n        var thenable = ctor(); // Transition to the next state.\n        // This might throw either because it's missing or throws. If so, we treat it\n        // as still uninitialized and try again next time. Which is the same as what\n        // happens if the ctor or any wrappers processing the ctor throws. This might\n        // end up fixing it if the resolution was a concurrency bug.\n\n        thenable.then(function (moduleObject) {\n          if (payload._status === Pending || payload._status === Uninitialized) {\n            // Transition to the next state.\n            var resolved = payload;\n            resolved._status = Resolved;\n            resolved._result = moduleObject;\n          }\n        }, function (error) {\n          if (payload._status === Pending || payload._status === Uninitialized) {\n            // Transition to the next state.\n            var rejected = payload;\n            rejected._status = Rejected;\n            rejected._result = error;\n          }\n        });\n        if (payload._status === Uninitialized) {\n          // In case, we're still uninitialized, then we're waiting for the thenable\n          // to resolve. Set it as pending in the meantime.\n          var pending = payload;\n          pending._status = Pending;\n          pending._result = thenable;\n        }\n      }\n      if (payload._status === Resolved) {\n        var moduleObject = payload._result;\n        {\n          if (moduleObject === undefined) {\n            error('lazy: Expected the result of a dynamic imp' + 'ort() call. ' + 'Instead received: %s\\n\\nYour code should look like: \\n  ' +\n            // Break up imports to avoid accidentally parsing them as dependencies.\n            'const MyComponent = lazy(() => imp' + \"ort('./MyComponent'))\\n\\n\" + 'Did you accidentally put curly braces around the import?', moduleObject);\n          }\n        }\n        {\n          if (!('default' in moduleObject)) {\n            error('lazy: Expected the result of a dynamic imp' + 'ort() call. ' + 'Instead received: %s\\n\\nYour code should look like: \\n  ' +\n            // Break up imports to avoid accidentally parsing them as dependencies.\n            'const MyComponent = lazy(() => imp' + \"ort('./MyComponent'))\", moduleObject);\n          }\n        }\n        return moduleObject.default;\n      } else {\n        throw payload._result;\n      }\n    }\n    function lazy(ctor) {\n      var payload = {\n        // We use these fields to store the result.\n        _status: Uninitialized,\n        _result: ctor\n      };\n      var lazyType = {\n        $$typeof: REACT_LAZY_TYPE,\n        _payload: payload,\n        _init: lazyInitializer\n      };\n      {\n        // In production, this would just set it on the object.\n        var defaultProps;\n        var propTypes; // $FlowFixMe\n\n        Object.defineProperties(lazyType, {\n          defaultProps: {\n            configurable: true,\n            get: function () {\n              return defaultProps;\n            },\n            set: function (newDefaultProps) {\n              error('React.lazy(...): It is not supported to assign `defaultProps` to ' + 'a lazy component import. Either specify them where the component ' + 'is defined, or create a wrapping component around it.');\n              defaultProps = newDefaultProps; // Match production behavior more closely:\n              // $FlowFixMe\n\n              Object.defineProperty(lazyType, 'defaultProps', {\n                enumerable: true\n              });\n            }\n          },\n          propTypes: {\n            configurable: true,\n            get: function () {\n              return propTypes;\n            },\n            set: function (newPropTypes) {\n              error('React.lazy(...): It is not supported to assign `propTypes` to ' + 'a lazy component import. Either specify them where the component ' + 'is defined, or create a wrapping component around it.');\n              propTypes = newPropTypes; // Match production behavior more closely:\n              // $FlowFixMe\n\n              Object.defineProperty(lazyType, 'propTypes', {\n                enumerable: true\n              });\n            }\n          }\n        });\n      }\n      return lazyType;\n    }\n    function forwardRef(render) {\n      {\n        if (render != null && render.$$typeof === REACT_MEMO_TYPE) {\n          error('forwardRef requires a render function but received a `memo` ' + 'component. Instead of forwardRef(memo(...)), use ' + 'memo(forwardRef(...)).');\n        } else if (typeof render !== 'function') {\n          error('forwardRef requires a render function but was given %s.', render === null ? 'null' : typeof render);\n        } else {\n          if (render.length !== 0 && render.length !== 2) {\n            error('forwardRef render functions accept exactly two parameters: props and ref. %s', render.length === 1 ? 'Did you forget to use the ref parameter?' : 'Any additional parameter will be undefined.');\n          }\n        }\n        if (render != null) {\n          if (render.defaultProps != null || render.propTypes != null) {\n            error('forwardRef render functions do not support propTypes or defaultProps. ' + 'Did you accidentally pass a React component?');\n          }\n        }\n      }\n      var elementType = {\n        $$typeof: REACT_FORWARD_REF_TYPE,\n        render: render\n      };\n      {\n        var ownName;\n        Object.defineProperty(elementType, 'displayName', {\n          enumerable: false,\n          configurable: true,\n          get: function () {\n            return ownName;\n          },\n          set: function (name) {\n            ownName = name; // The inner component shouldn't inherit this display name in most cases,\n            // because the component may be used elsewhere.\n            // But it's nice for anonymous functions to inherit the name,\n            // so that our component-stack generation logic will display their frames.\n            // An anonymous function generally suggests a pattern like:\n            //   React.forwardRef((props, ref) => {...});\n            // This kind of inner function is not used elsewhere so the side effect is okay.\n\n            if (!render.name && !render.displayName) {\n              render.displayName = name;\n            }\n          }\n        });\n      }\n      return elementType;\n    }\n    var REACT_MODULE_REFERENCE;\n    {\n      REACT_MODULE_REFERENCE = Symbol.for('react.module.reference');\n    }\n    function isValidElementType(type) {\n      if (typeof type === 'string' || typeof type === 'function') {\n        return true;\n      } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n      if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || enableDebugTracing || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || enableLegacyHidden || type === REACT_OFFSCREEN_TYPE || enableScopeAPI || enableCacheElement || enableTransitionTracing) {\n        return true;\n      }\n      if (typeof type === 'object' && type !== null) {\n        if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE ||\n        // This needs to include all possible module reference object\n        // types supported by any Flight configuration anywhere since\n        // we don't know which Flight build this will end up being used\n        // with.\n        type.$$typeof === REACT_MODULE_REFERENCE || type.getModuleId !== undefined) {\n          return true;\n        }\n      }\n      return false;\n    }\n    function memo(type, compare) {\n      {\n        if (!isValidElementType(type)) {\n          error('memo: The first argument must be a component. Instead ' + 'received: %s', type === null ? 'null' : typeof type);\n        }\n      }\n      var elementType = {\n        $$typeof: REACT_MEMO_TYPE,\n        type: type,\n        compare: compare === undefined ? null : compare\n      };\n      {\n        var ownName;\n        Object.defineProperty(elementType, 'displayName', {\n          enumerable: false,\n          configurable: true,\n          get: function () {\n            return ownName;\n          },\n          set: function (name) {\n            ownName = name; // The inner component shouldn't inherit this display name in most cases,\n            // because the component may be used elsewhere.\n            // But it's nice for anonymous functions to inherit the name,\n            // so that our component-stack generation logic will display their frames.\n            // An anonymous function generally suggests a pattern like:\n            //   React.memo((props) => {...});\n            // This kind of inner function is not used elsewhere so the side effect is okay.\n\n            if (!type.name && !type.displayName) {\n              type.displayName = name;\n            }\n          }\n        });\n      }\n      return elementType;\n    }\n    function resolveDispatcher() {\n      var dispatcher = ReactCurrentDispatcher.current;\n      {\n        if (dispatcher === null) {\n          error('Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for' + ' one of the following reasons:\\n' + '1. You might have mismatching versions of React and the renderer (such as React DOM)\\n' + '2. You might be breaking the Rules of Hooks\\n' + '3. You might have more than one copy of React in the same app\\n' + 'See https://reactjs.org/link/invalid-hook-call for tips about how to debug and fix this problem.');\n        }\n      } // Will result in a null access error if accessed outside render phase. We\n      // intentionally don't throw our own error because this is in a hot path.\n      // Also helps ensure this is inlined.\n\n      return dispatcher;\n    }\n    function useContext(Context) {\n      var dispatcher = resolveDispatcher();\n      {\n        // TODO: add a more generic warning for invalid values.\n        if (Context._context !== undefined) {\n          var realContext = Context._context; // Don't deduplicate because this legitimately causes bugs\n          // and nobody should be using this in existing code.\n\n          if (realContext.Consumer === Context) {\n            error('Calling useContext(Context.Consumer) is not supported, may cause bugs, and will be ' + 'removed in a future major release. Did you mean to call useContext(Context) instead?');\n          } else if (realContext.Provider === Context) {\n            error('Calling useContext(Context.Provider) is not supported. ' + 'Did you mean to call useContext(Context) instead?');\n          }\n        }\n      }\n      return dispatcher.useContext(Context);\n    }\n    function useState(initialState) {\n      var dispatcher = resolveDispatcher();\n      return dispatcher.useState(initialState);\n    }\n    function useReducer(reducer, initialArg, init) {\n      var dispatcher = resolveDispatcher();\n      return dispatcher.useReducer(reducer, initialArg, init);\n    }\n    function useRef(initialValue) {\n      var dispatcher = resolveDispatcher();\n      return dispatcher.useRef(initialValue);\n    }\n    function useEffect(create, deps) {\n      var dispatcher = resolveDispatcher();\n      return dispatcher.useEffect(create, deps);\n    }\n    function useInsertionEffect(create, deps) {\n      var dispatcher = resolveDispatcher();\n      return dispatcher.useInsertionEffect(create, deps);\n    }\n    function useLayoutEffect(create, deps) {\n      var dispatcher = resolveDispatcher();\n      return dispatcher.useLayoutEffect(create, deps);\n    }\n    function useCallback(callback, deps) {\n      var dispatcher = resolveDispatcher();\n      return dispatcher.useCallback(callback, deps);\n    }\n    function useMemo(create, deps) {\n      var dispatcher = resolveDispatcher();\n      return dispatcher.useMemo(create, deps);\n    }\n    function useImperativeHandle(ref, create, deps) {\n      var dispatcher = resolveDispatcher();\n      return dispatcher.useImperativeHandle(ref, create, deps);\n    }\n    function useDebugValue(value, formatterFn) {\n      {\n        var dispatcher = resolveDispatcher();\n        return dispatcher.useDebugValue(value, formatterFn);\n      }\n    }\n    function useTransition() {\n      var dispatcher = resolveDispatcher();\n      return dispatcher.useTransition();\n    }\n    function useDeferredValue(value) {\n      var dispatcher = resolveDispatcher();\n      return dispatcher.useDeferredValue(value);\n    }\n    function useId() {\n      var dispatcher = resolveDispatcher();\n      return dispatcher.useId();\n    }\n    function useSyncExternalStore(subscribe, getSnapshot, getServerSnapshot) {\n      var dispatcher = resolveDispatcher();\n      return dispatcher.useSyncExternalStore(subscribe, getSnapshot, getServerSnapshot);\n    }\n\n    // Helpers to patch console.logs to avoid logging during side-effect free\n    // replaying on render function. This currently only patches the object\n    // lazily which won't cover if the log function was extracted eagerly.\n    // We could also eagerly patch the method.\n    var disabledDepth = 0;\n    var prevLog;\n    var prevInfo;\n    var prevWarn;\n    var prevError;\n    var prevGroup;\n    var prevGroupCollapsed;\n    var prevGroupEnd;\n    function disabledLog() {}\n    disabledLog.__reactDisabledLog = true;\n    function disableLogs() {\n      {\n        if (disabledDepth === 0) {\n          /* eslint-disable react-internal/no-production-logging */\n          prevLog = console.log;\n          prevInfo = console.info;\n          prevWarn = console.warn;\n          prevError = console.error;\n          prevGroup = console.group;\n          prevGroupCollapsed = console.groupCollapsed;\n          prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n\n          var props = {\n            configurable: true,\n            enumerable: true,\n            value: disabledLog,\n            writable: true\n          }; // $FlowFixMe Flow thinks console is immutable.\n\n          Object.defineProperties(console, {\n            info: props,\n            log: props,\n            warn: props,\n            error: props,\n            group: props,\n            groupCollapsed: props,\n            groupEnd: props\n          });\n          /* eslint-enable react-internal/no-production-logging */\n        }\n        disabledDepth++;\n      }\n    }\n    function reenableLogs() {\n      {\n        disabledDepth--;\n        if (disabledDepth === 0) {\n          /* eslint-disable react-internal/no-production-logging */\n          var props = {\n            configurable: true,\n            enumerable: true,\n            writable: true\n          }; // $FlowFixMe Flow thinks console is immutable.\n\n          Object.defineProperties(console, {\n            log: assign({}, props, {\n              value: prevLog\n            }),\n            info: assign({}, props, {\n              value: prevInfo\n            }),\n            warn: assign({}, props, {\n              value: prevWarn\n            }),\n            error: assign({}, props, {\n              value: prevError\n            }),\n            group: assign({}, props, {\n              value: prevGroup\n            }),\n            groupCollapsed: assign({}, props, {\n              value: prevGroupCollapsed\n            }),\n            groupEnd: assign({}, props, {\n              value: prevGroupEnd\n            })\n          });\n          /* eslint-enable react-internal/no-production-logging */\n        }\n        if (disabledDepth < 0) {\n          error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\n        }\n      }\n    }\n    var ReactCurrentDispatcher$1 = ReactSharedInternals.ReactCurrentDispatcher;\n    var prefix;\n    function describeBuiltInComponentFrame(name, source, ownerFn) {\n      {\n        if (prefix === undefined) {\n          // Extract the VM specific prefix used by each line.\n          try {\n            throw Error();\n          } catch (x) {\n            var match = x.stack.trim().match(/\\n( *(at )?)/);\n            prefix = match && match[1] || '';\n          }\n        } // We use the prefix to ensure our stacks line up with native stack frames.\n\n        return '\\n' + prefix + name;\n      }\n    }\n    var reentry = false;\n    var componentFrameCache;\n    {\n      var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\n      componentFrameCache = new PossiblyWeakMap();\n    }\n    function describeNativeComponentFrame(fn, construct) {\n      // If something asked for a stack inside a fake render, it should get ignored.\n      if (!fn || reentry) {\n        return '';\n      }\n      {\n        var frame = componentFrameCache.get(fn);\n        if (frame !== undefined) {\n          return frame;\n        }\n      }\n      var control;\n      reentry = true;\n      var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe It does accept undefined.\n\n      Error.prepareStackTrace = undefined;\n      var previousDispatcher;\n      {\n        previousDispatcher = ReactCurrentDispatcher$1.current; // Set the dispatcher in DEV because this might be call in the render function\n        // for warnings.\n\n        ReactCurrentDispatcher$1.current = null;\n        disableLogs();\n      }\n      try {\n        // This should throw.\n        if (construct) {\n          // Something should be setting the props in the constructor.\n          var Fake = function () {\n            throw Error();\n          }; // $FlowFixMe\n\n          Object.defineProperty(Fake.prototype, 'props', {\n            set: function () {\n              // We use a throwing setter instead of frozen or non-writable props\n              // because that won't throw in a non-strict mode function.\n              throw Error();\n            }\n          });\n          if (typeof Reflect === 'object' && Reflect.construct) {\n            // We construct a different control for this case to include any extra\n            // frames added by the construct call.\n            try {\n              Reflect.construct(Fake, []);\n            } catch (x) {\n              control = x;\n            }\n            Reflect.construct(fn, [], Fake);\n          } else {\n            try {\n              Fake.call();\n            } catch (x) {\n              control = x;\n            }\n            fn.call(Fake.prototype);\n          }\n        } else {\n          try {\n            throw Error();\n          } catch (x) {\n            control = x;\n          }\n          fn();\n        }\n      } catch (sample) {\n        // This is inlined manually because closure doesn't do it for us.\n        if (sample && control && typeof sample.stack === 'string') {\n          // This extracts the first frame from the sample that isn't also in the control.\n          // Skipping one frame that we assume is the frame that calls the two.\n          var sampleLines = sample.stack.split('\\n');\n          var controlLines = control.stack.split('\\n');\n          var s = sampleLines.length - 1;\n          var c = controlLines.length - 1;\n          while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\n            // We expect at least one stack frame to be shared.\n            // Typically this will be the root most one. However, stack frames may be\n            // cut off due to maximum stack limits. In this case, one maybe cut off\n            // earlier than the other. We assume that the sample is longer or the same\n            // and there for cut off earlier. So we should find the root most frame in\n            // the sample somewhere in the control.\n            c--;\n          }\n          for (; s >= 1 && c >= 0; s--, c--) {\n            // Next we find the first one that isn't the same which should be the\n            // frame that called our sample function and the control.\n            if (sampleLines[s] !== controlLines[c]) {\n              // In V8, the first line is describing the message but other VMs don't.\n              // If we're about to return the first line, and the control is also on the same\n              // line, that's a pretty good indicator that our sample threw at same line as\n              // the control. I.e. before we entered the sample frame. So we ignore this result.\n              // This can happen if you passed a class to function component, or non-function.\n              if (s !== 1 || c !== 1) {\n                do {\n                  s--;\n                  c--; // We may still have similar intermediate frames from the construct call.\n                  // The next one that isn't the same should be our match though.\n\n                  if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                    // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                    var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at '); // If our component frame is labeled \"<anonymous>\"\n                    // but we have a user-provided \"displayName\"\n                    // splice it in to make the stack more readable.\n\n                    if (fn.displayName && _frame.includes('<anonymous>')) {\n                      _frame = _frame.replace('<anonymous>', fn.displayName);\n                    }\n                    {\n                      if (typeof fn === 'function') {\n                        componentFrameCache.set(fn, _frame);\n                      }\n                    } // Return the line we found.\n\n                    return _frame;\n                  }\n                } while (s >= 1 && c >= 0);\n              }\n              break;\n            }\n          }\n        }\n      } finally {\n        reentry = false;\n        {\n          ReactCurrentDispatcher$1.current = previousDispatcher;\n          reenableLogs();\n        }\n        Error.prepareStackTrace = previousPrepareStackTrace;\n      } // Fallback to just using the name if we couldn't make it throw.\n\n      var name = fn ? fn.displayName || fn.name : '';\n      var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\n      {\n        if (typeof fn === 'function') {\n          componentFrameCache.set(fn, syntheticFrame);\n        }\n      }\n      return syntheticFrame;\n    }\n    function describeFunctionComponentFrame(fn, source, ownerFn) {\n      {\n        return describeNativeComponentFrame(fn, false);\n      }\n    }\n    function shouldConstruct(Component) {\n      var prototype = Component.prototype;\n      return !!(prototype && prototype.isReactComponent);\n    }\n    function describeUnknownElementTypeFrameInDEV(type, source, ownerFn) {\n      if (type == null) {\n        return '';\n      }\n      if (typeof type === 'function') {\n        {\n          return describeNativeComponentFrame(type, shouldConstruct(type));\n        }\n      }\n      if (typeof type === 'string') {\n        return describeBuiltInComponentFrame(type);\n      }\n      switch (type) {\n        case REACT_SUSPENSE_TYPE:\n          return describeBuiltInComponentFrame('Suspense');\n        case REACT_SUSPENSE_LIST_TYPE:\n          return describeBuiltInComponentFrame('SuspenseList');\n      }\n      if (typeof type === 'object') {\n        switch (type.$$typeof) {\n          case REACT_FORWARD_REF_TYPE:\n            return describeFunctionComponentFrame(type.render);\n          case REACT_MEMO_TYPE:\n            // Memo may contain any component type so we recursively resolve it.\n            return describeUnknownElementTypeFrameInDEV(type.type, source, ownerFn);\n          case REACT_LAZY_TYPE:\n            {\n              var lazyComponent = type;\n              var payload = lazyComponent._payload;\n              var init = lazyComponent._init;\n              try {\n                // Lazy may contain any component type so we recursively resolve it.\n                return describeUnknownElementTypeFrameInDEV(init(payload), source, ownerFn);\n              } catch (x) {}\n            }\n        }\n      }\n      return '';\n    }\n    var loggedTypeFailures = {};\n    var ReactDebugCurrentFrame$1 = ReactSharedInternals.ReactDebugCurrentFrame;\n    function setCurrentlyValidatingElement(element) {\n      {\n        if (element) {\n          var owner = element._owner;\n          var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n          ReactDebugCurrentFrame$1.setExtraStackFrame(stack);\n        } else {\n          ReactDebugCurrentFrame$1.setExtraStackFrame(null);\n        }\n      }\n    }\n    function checkPropTypes(typeSpecs, values, location, componentName, element) {\n      {\n        // $FlowFixMe This is okay but Flow doesn't know it.\n        var has = Function.call.bind(hasOwnProperty);\n        for (var typeSpecName in typeSpecs) {\n          if (has(typeSpecs, typeSpecName)) {\n            var error$1 = void 0; // Prop type validation may throw. In case they do, we don't want to\n            // fail the render phase where it didn't fail before. So we log it.\n            // After these have been cleaned up, we'll let them throw.\n\n            try {\n              // This is intentionally an invariant that gets caught. It's the same\n              // behavior as without this statement except with a better message.\n              if (typeof typeSpecs[typeSpecName] !== 'function') {\n                // eslint-disable-next-line react-internal/prod-error-codes\n                var err = Error((componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' + 'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' + 'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.');\n                err.name = 'Invariant Violation';\n                throw err;\n              }\n              error$1 = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED');\n            } catch (ex) {\n              error$1 = ex;\n            }\n            if (error$1 && !(error$1 instanceof Error)) {\n              setCurrentlyValidatingElement(element);\n              error('%s: type specification of %s' + ' `%s` is invalid; the type checker ' + 'function must return `null` or an `Error` but returned a %s. ' + 'You may have forgotten to pass an argument to the type checker ' + 'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' + 'shape all require an argument).', componentName || 'React class', location, typeSpecName, typeof error$1);\n              setCurrentlyValidatingElement(null);\n            }\n            if (error$1 instanceof Error && !(error$1.message in loggedTypeFailures)) {\n              // Only monitor this failure once because there tends to be a lot of the\n              // same error.\n              loggedTypeFailures[error$1.message] = true;\n              setCurrentlyValidatingElement(element);\n              error('Failed %s type: %s', location, error$1.message);\n              setCurrentlyValidatingElement(null);\n            }\n          }\n        }\n      }\n    }\n    function setCurrentlyValidatingElement$1(element) {\n      {\n        if (element) {\n          var owner = element._owner;\n          var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n          setExtraStackFrame(stack);\n        } else {\n          setExtraStackFrame(null);\n        }\n      }\n    }\n    var propTypesMisspellWarningShown;\n    {\n      propTypesMisspellWarningShown = false;\n    }\n    function getDeclarationErrorAddendum() {\n      if (ReactCurrentOwner.current) {\n        var name = getComponentNameFromType(ReactCurrentOwner.current.type);\n        if (name) {\n          return '\\n\\nCheck the render method of `' + name + '`.';\n        }\n      }\n      return '';\n    }\n    function getSourceInfoErrorAddendum(source) {\n      if (source !== undefined) {\n        var fileName = source.fileName.replace(/^.*[\\\\\\/]/, '');\n        var lineNumber = source.lineNumber;\n        return '\\n\\nCheck your code at ' + fileName + ':' + lineNumber + '.';\n      }\n      return '';\n    }\n    function getSourceInfoErrorAddendumForProps(elementProps) {\n      if (elementProps !== null && elementProps !== undefined) {\n        return getSourceInfoErrorAddendum(elementProps.__source);\n      }\n      return '';\n    }\n    /**\n     * Warn if there's no key explicitly set on dynamic arrays of children or\n     * object keys are not valid. This allows us to keep track of children between\n     * updates.\n     */\n\n    var ownerHasKeyUseWarning = {};\n    function getCurrentComponentErrorInfo(parentType) {\n      var info = getDeclarationErrorAddendum();\n      if (!info) {\n        var parentName = typeof parentType === 'string' ? parentType : parentType.displayName || parentType.name;\n        if (parentName) {\n          info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n        }\n      }\n      return info;\n    }\n    /**\n     * Warn if the element doesn't have an explicit key assigned to it.\n     * This element is in an array. The array could grow and shrink or be\n     * reordered. All children that haven't already been validated are required to\n     * have a \"key\" property assigned to it. Error statuses are cached so a warning\n     * will only be shown once.\n     *\n     * @internal\n     * @param {ReactElement} element Element that requires a key.\n     * @param {*} parentType element's parent's type.\n     */\n\n    function validateExplicitKey(element, parentType) {\n      if (!element._store || element._store.validated || element.key != null) {\n        return;\n      }\n      element._store.validated = true;\n      var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n      if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n        return;\n      }\n      ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n      // property, it may be the creator of the child that's responsible for\n      // assigning it a key.\n\n      var childOwner = '';\n      if (element && element._owner && element._owner !== ReactCurrentOwner.current) {\n        // Give the component that originally created this child.\n        childOwner = \" It was passed a child from \" + getComponentNameFromType(element._owner.type) + \".\";\n      }\n      {\n        setCurrentlyValidatingElement$1(element);\n        error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\n        setCurrentlyValidatingElement$1(null);\n      }\n    }\n    /**\n     * Ensure that every element either is passed in a static location, in an\n     * array with an explicit keys property defined, or in an object literal\n     * with valid key property.\n     *\n     * @internal\n     * @param {ReactNode} node Statically passed child of any type.\n     * @param {*} parentType node's parent's type.\n     */\n\n    function validateChildKeys(node, parentType) {\n      if (typeof node !== 'object') {\n        return;\n      }\n      if (isArray(node)) {\n        for (var i = 0; i < node.length; i++) {\n          var child = node[i];\n          if (isValidElement(child)) {\n            validateExplicitKey(child, parentType);\n          }\n        }\n      } else if (isValidElement(node)) {\n        // This element was passed in a valid location.\n        if (node._store) {\n          node._store.validated = true;\n        }\n      } else if (node) {\n        var iteratorFn = getIteratorFn(node);\n        if (typeof iteratorFn === 'function') {\n          // Entry iterators used to provide implicit keys,\n          // but now we print a separate warning for them later.\n          if (iteratorFn !== node.entries) {\n            var iterator = iteratorFn.call(node);\n            var step;\n            while (!(step = iterator.next()).done) {\n              if (isValidElement(step.value)) {\n                validateExplicitKey(step.value, parentType);\n              }\n            }\n          }\n        }\n      }\n    }\n    /**\n     * Given an element, validate that its props follow the propTypes definition,\n     * provided by the type.\n     *\n     * @param {ReactElement} element\n     */\n\n    function validatePropTypes(element) {\n      {\n        var type = element.type;\n        if (type === null || type === undefined || typeof type === 'string') {\n          return;\n        }\n        var propTypes;\n        if (typeof type === 'function') {\n          propTypes = type.propTypes;\n        } else if (typeof type === 'object' && (type.$$typeof === REACT_FORWARD_REF_TYPE ||\n        // Note: Memo only checks outer props here.\n        // Inner props are checked in the reconciler.\n        type.$$typeof === REACT_MEMO_TYPE)) {\n          propTypes = type.propTypes;\n        } else {\n          return;\n        }\n        if (propTypes) {\n          // Intentionally inside to avoid triggering lazy initializers:\n          var name = getComponentNameFromType(type);\n          checkPropTypes(propTypes, element.props, 'prop', name, element);\n        } else if (type.PropTypes !== undefined && !propTypesMisspellWarningShown) {\n          propTypesMisspellWarningShown = true; // Intentionally inside to avoid triggering lazy initializers:\n\n          var _name = getComponentNameFromType(type);\n          error('Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?', _name || 'Unknown');\n        }\n        if (typeof type.getDefaultProps === 'function' && !type.getDefaultProps.isReactClassApproved) {\n          error('getDefaultProps is only used on classic React.createClass ' + 'definitions. Use a static property named `defaultProps` instead.');\n        }\n      }\n    }\n    /**\n     * Given a fragment, validate that it can only be provided with fragment props\n     * @param {ReactElement} fragment\n     */\n\n    function validateFragmentProps(fragment) {\n      {\n        var keys = Object.keys(fragment.props);\n        for (var i = 0; i < keys.length; i++) {\n          var key = keys[i];\n          if (key !== 'children' && key !== 'key') {\n            setCurrentlyValidatingElement$1(fragment);\n            error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\n            setCurrentlyValidatingElement$1(null);\n            break;\n          }\n        }\n        if (fragment.ref !== null) {\n          setCurrentlyValidatingElement$1(fragment);\n          error('Invalid attribute `ref` supplied to `React.Fragment`.');\n          setCurrentlyValidatingElement$1(null);\n        }\n      }\n    }\n    function createElementWithValidation(type, props, children) {\n      var validType = isValidElementType(type); // We warn in this case but don't throw. We expect the element creation to\n      // succeed and there will likely be errors in render.\n\n      if (!validType) {\n        var info = '';\n        if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\n          info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\n        }\n        var sourceInfo = getSourceInfoErrorAddendumForProps(props);\n        if (sourceInfo) {\n          info += sourceInfo;\n        } else {\n          info += getDeclarationErrorAddendum();\n        }\n        var typeString;\n        if (type === null) {\n          typeString = 'null';\n        } else if (isArray(type)) {\n          typeString = 'array';\n        } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n          typeString = \"<\" + (getComponentNameFromType(type.type) || 'Unknown') + \" />\";\n          info = ' Did you accidentally export a JSX literal instead of a component?';\n        } else {\n          typeString = typeof type;\n        }\n        {\n          error('React.createElement: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\n        }\n      }\n      var element = createElement.apply(this, arguments); // The result can be nullish if a mock or a custom function is used.\n      // TODO: Drop this when these are no longer allowed as the type argument.\n\n      if (element == null) {\n        return element;\n      } // Skip key warning if the type isn't valid since our key validation logic\n      // doesn't expect a non-string/function type and can throw confusing errors.\n      // We don't want exception behavior to differ between dev and prod.\n      // (Rendering will throw with a helpful message and as soon as the type is\n      // fixed, the key warnings will appear.)\n\n      if (validType) {\n        for (var i = 2; i < arguments.length; i++) {\n          validateChildKeys(arguments[i], type);\n        }\n      }\n      if (type === REACT_FRAGMENT_TYPE) {\n        validateFragmentProps(element);\n      } else {\n        validatePropTypes(element);\n      }\n      return element;\n    }\n    var didWarnAboutDeprecatedCreateFactory = false;\n    function createFactoryWithValidation(type) {\n      var validatedFactory = createElementWithValidation.bind(null, type);\n      validatedFactory.type = type;\n      {\n        if (!didWarnAboutDeprecatedCreateFactory) {\n          didWarnAboutDeprecatedCreateFactory = true;\n          warn('React.createFactory() is deprecated and will be removed in ' + 'a future major release. Consider using JSX ' + 'or use React.createElement() directly instead.');\n        } // Legacy hook: remove it\n\n        Object.defineProperty(validatedFactory, 'type', {\n          enumerable: false,\n          get: function () {\n            warn('Factory.type is deprecated. Access the class directly ' + 'before passing it to createFactory.');\n            Object.defineProperty(this, 'type', {\n              value: type\n            });\n            return type;\n          }\n        });\n      }\n      return validatedFactory;\n    }\n    function cloneElementWithValidation(element, props, children) {\n      var newElement = cloneElement.apply(this, arguments);\n      for (var i = 2; i < arguments.length; i++) {\n        validateChildKeys(arguments[i], newElement.type);\n      }\n      validatePropTypes(newElement);\n      return newElement;\n    }\n    function startTransition(scope, options) {\n      var prevTransition = ReactCurrentBatchConfig.transition;\n      ReactCurrentBatchConfig.transition = {};\n      var currentTransition = ReactCurrentBatchConfig.transition;\n      {\n        ReactCurrentBatchConfig.transition._updatedFibers = new Set();\n      }\n      try {\n        scope();\n      } finally {\n        ReactCurrentBatchConfig.transition = prevTransition;\n        {\n          if (prevTransition === null && currentTransition._updatedFibers) {\n            var updatedFibersCount = currentTransition._updatedFibers.size;\n            if (updatedFibersCount > 10) {\n              warn('Detected a large number of updates inside startTransition. ' + 'If this is due to a subscription please re-write it to use React provided hooks. ' + 'Otherwise concurrent mode guarantees are off the table.');\n            }\n            currentTransition._updatedFibers.clear();\n          }\n        }\n      }\n    }\n    var didWarnAboutMessageChannel = false;\n    var enqueueTaskImpl = null;\n    function enqueueTask(task) {\n      if (enqueueTaskImpl === null) {\n        try {\n          // read require off the module object to get around the bundlers.\n          // we don't want them to detect a require and bundle a Node polyfill.\n          var requireString = ('require' + Math.random()).slice(0, 7);\n          var nodeRequire = module && module[requireString]; // assuming we're in node, let's try to get node's\n          // version of setImmediate, bypassing fake timers if any.\n\n          enqueueTaskImpl = nodeRequire.call(module, 'timers').setImmediate;\n        } catch (_err) {\n          // we're in a browser\n          // we can't use regular timers because they may still be faked\n          // so we try MessageChannel+postMessage instead\n          enqueueTaskImpl = function (callback) {\n            {\n              if (didWarnAboutMessageChannel === false) {\n                didWarnAboutMessageChannel = true;\n                if (typeof MessageChannel === 'undefined') {\n                  error('This browser does not have a MessageChannel implementation, ' + 'so enqueuing tasks via await act(async () => ...) will fail. ' + 'Please file an issue at https://github.com/facebook/react/issues ' + 'if you encounter this warning.');\n                }\n              }\n            }\n            var channel = new MessageChannel();\n            channel.port1.onmessage = callback;\n            channel.port2.postMessage(undefined);\n          };\n        }\n      }\n      return enqueueTaskImpl(task);\n    }\n    var actScopeDepth = 0;\n    var didWarnNoAwaitAct = false;\n    function act(callback) {\n      {\n        // `act` calls can be nested, so we track the depth. This represents the\n        // number of `act` scopes on the stack.\n        var prevActScopeDepth = actScopeDepth;\n        actScopeDepth++;\n        if (ReactCurrentActQueue.current === null) {\n          // This is the outermost `act` scope. Initialize the queue. The reconciler\n          // will detect the queue and use it instead of Scheduler.\n          ReactCurrentActQueue.current = [];\n        }\n        var prevIsBatchingLegacy = ReactCurrentActQueue.isBatchingLegacy;\n        var result;\n        try {\n          // Used to reproduce behavior of `batchedUpdates` in legacy mode. Only\n          // set to `true` while the given callback is executed, not for updates\n          // triggered during an async event, because this is how the legacy\n          // implementation of `act` behaved.\n          ReactCurrentActQueue.isBatchingLegacy = true;\n          result = callback(); // Replicate behavior of original `act` implementation in legacy mode,\n          // which flushed updates immediately after the scope function exits, even\n          // if it's an async function.\n\n          if (!prevIsBatchingLegacy && ReactCurrentActQueue.didScheduleLegacyUpdate) {\n            var queue = ReactCurrentActQueue.current;\n            if (queue !== null) {\n              ReactCurrentActQueue.didScheduleLegacyUpdate = false;\n              flushActQueue(queue);\n            }\n          }\n        } catch (error) {\n          popActScope(prevActScopeDepth);\n          throw error;\n        } finally {\n          ReactCurrentActQueue.isBatchingLegacy = prevIsBatchingLegacy;\n        }\n        if (result !== null && typeof result === 'object' && typeof result.then === 'function') {\n          var thenableResult = result; // The callback is an async function (i.e. returned a promise). Wait\n          // for it to resolve before exiting the current scope.\n\n          var wasAwaited = false;\n          var thenable = {\n            then: function (resolve, reject) {\n              wasAwaited = true;\n              thenableResult.then(function (returnValue) {\n                popActScope(prevActScopeDepth);\n                if (actScopeDepth === 0) {\n                  // We've exited the outermost act scope. Recursively flush the\n                  // queue until there's no remaining work.\n                  recursivelyFlushAsyncActWork(returnValue, resolve, reject);\n                } else {\n                  resolve(returnValue);\n                }\n              }, function (error) {\n                // The callback threw an error.\n                popActScope(prevActScopeDepth);\n                reject(error);\n              });\n            }\n          };\n          {\n            if (!didWarnNoAwaitAct && typeof Promise !== 'undefined') {\n              // eslint-disable-next-line no-undef\n              Promise.resolve().then(function () {}).then(function () {\n                if (!wasAwaited) {\n                  didWarnNoAwaitAct = true;\n                  error('You called act(async () => ...) without await. ' + 'This could lead to unexpected testing behaviour, ' + 'interleaving multiple act calls and mixing their ' + 'scopes. ' + 'You should - await act(async () => ...);');\n                }\n              });\n            }\n          }\n          return thenable;\n        } else {\n          var returnValue = result; // The callback is not an async function. Exit the current scope\n          // immediately, without awaiting.\n\n          popActScope(prevActScopeDepth);\n          if (actScopeDepth === 0) {\n            // Exiting the outermost act scope. Flush the queue.\n            var _queue = ReactCurrentActQueue.current;\n            if (_queue !== null) {\n              flushActQueue(_queue);\n              ReactCurrentActQueue.current = null;\n            } // Return a thenable. If the user awaits it, we'll flush again in\n            // case additional work was scheduled by a microtask.\n\n            var _thenable = {\n              then: function (resolve, reject) {\n                // Confirm we haven't re-entered another `act` scope, in case\n                // the user does something weird like await the thenable\n                // multiple times.\n                if (ReactCurrentActQueue.current === null) {\n                  // Recursively flush the queue until there's no remaining work.\n                  ReactCurrentActQueue.current = [];\n                  recursivelyFlushAsyncActWork(returnValue, resolve, reject);\n                } else {\n                  resolve(returnValue);\n                }\n              }\n            };\n            return _thenable;\n          } else {\n            // Since we're inside a nested `act` scope, the returned thenable\n            // immediately resolves. The outer scope will flush the queue.\n            var _thenable2 = {\n              then: function (resolve, reject) {\n                resolve(returnValue);\n              }\n            };\n            return _thenable2;\n          }\n        }\n      }\n    }\n    function popActScope(prevActScopeDepth) {\n      {\n        if (prevActScopeDepth !== actScopeDepth - 1) {\n          error('You seem to have overlapping act() calls, this is not supported. ' + 'Be sure to await previous act() calls before making a new one. ');\n        }\n        actScopeDepth = prevActScopeDepth;\n      }\n    }\n    function recursivelyFlushAsyncActWork(returnValue, resolve, reject) {\n      {\n        var queue = ReactCurrentActQueue.current;\n        if (queue !== null) {\n          try {\n            flushActQueue(queue);\n            enqueueTask(function () {\n              if (queue.length === 0) {\n                // No additional work was scheduled. Finish.\n                ReactCurrentActQueue.current = null;\n                resolve(returnValue);\n              } else {\n                // Keep flushing work until there's none left.\n                recursivelyFlushAsyncActWork(returnValue, resolve, reject);\n              }\n            });\n          } catch (error) {\n            reject(error);\n          }\n        } else {\n          resolve(returnValue);\n        }\n      }\n    }\n    var isFlushing = false;\n    function flushActQueue(queue) {\n      {\n        if (!isFlushing) {\n          // Prevent re-entrance.\n          isFlushing = true;\n          var i = 0;\n          try {\n            for (; i < queue.length; i++) {\n              var callback = queue[i];\n              do {\n                callback = callback(true);\n              } while (callback !== null);\n            }\n            queue.length = 0;\n          } catch (error) {\n            // If something throws, leave the remaining callbacks on the queue.\n            queue = queue.slice(i + 1);\n            throw error;\n          } finally {\n            isFlushing = false;\n          }\n        }\n      }\n    }\n    var createElement$1 = createElementWithValidation;\n    var cloneElement$1 = cloneElementWithValidation;\n    var createFactory = createFactoryWithValidation;\n    var Children = {\n      map: mapChildren,\n      forEach: forEachChildren,\n      count: countChildren,\n      toArray: toArray,\n      only: onlyChild\n    };\n    exports.Children = Children;\n    exports.Component = Component;\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.Profiler = REACT_PROFILER_TYPE;\n    exports.PureComponent = PureComponent;\n    exports.StrictMode = REACT_STRICT_MODE_TYPE;\n    exports.Suspense = REACT_SUSPENSE_TYPE;\n    exports.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = ReactSharedInternals;\n    exports.act = act;\n    exports.cloneElement = cloneElement$1;\n    exports.createContext = createContext;\n    exports.createElement = createElement$1;\n    exports.createFactory = createFactory;\n    exports.createRef = createRef;\n    exports.forwardRef = forwardRef;\n    exports.isValidElement = isValidElement;\n    exports.lazy = lazy;\n    exports.memo = memo;\n    exports.startTransition = startTransition;\n    exports.unstable_act = act;\n    exports.useCallback = useCallback;\n    exports.useContext = useContext;\n    exports.useDebugValue = useDebugValue;\n    exports.useDeferredValue = useDeferredValue;\n    exports.useEffect = useEffect;\n    exports.useId = useId;\n    exports.useImperativeHandle = useImperativeHandle;\n    exports.useInsertionEffect = useInsertionEffect;\n    exports.useLayoutEffect = useLayoutEffect;\n    exports.useMemo = useMemo;\n    exports.useReducer = useReducer;\n    exports.useRef = useRef;\n    exports.useState = useState;\n    exports.useSyncExternalStore = useSyncExternalStore;\n    exports.useTransition = useTransition;\n    exports.version = ReactVersion;\n    /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\n    if (typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' && typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop === 'function') {\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error());\n    }\n  })();\n}", "map": {"version": 3, "names": ["process", "env", "NODE_ENV", "__REACT_DEVTOOLS_GLOBAL_HOOK__", "registerInternalModuleStart", "Error", "ReactVersion", "REACT_ELEMENT_TYPE", "Symbol", "for", "REACT_PORTAL_TYPE", "REACT_FRAGMENT_TYPE", "REACT_STRICT_MODE_TYPE", "REACT_PROFILER_TYPE", "REACT_PROVIDER_TYPE", "REACT_CONTEXT_TYPE", "REACT_FORWARD_REF_TYPE", "REACT_SUSPENSE_TYPE", "REACT_SUSPENSE_LIST_TYPE", "REACT_MEMO_TYPE", "REACT_LAZY_TYPE", "REACT_OFFSCREEN_TYPE", "MAYBE_ITERATOR_SYMBOL", "iterator", "FAUX_ITERATOR_SYMBOL", "getIteratorFn", "maybeIterable", "maybeIterator", "ReactCur<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "current", "ReactCurrentBatchConfig", "transition", "ReactCurrentActQueue", "isBatchingLegacy", "didScheduleLegacyUpdate", "ReactCurrentOwner", "ReactDebugCurrentFrame", "currentExtraStackFrame", "setExtraStackFrame", "stack", "getCurrentStack", "getStackAddendum", "impl", "enableScopeAPI", "enableCacheElement", "enableTransitionTracing", "enableLegacyHidden", "enableDebugTracing", "ReactSharedInternals", "warn", "format", "_len", "arguments", "length", "args", "Array", "_key", "printWarning", "error", "_len2", "_key2", "level", "concat", "argsWithFormat", "map", "item", "String", "unshift", "Function", "prototype", "apply", "call", "console", "didWarnStateUpdateForUnmountedComponent", "warnNoop", "publicInstance", "callerName", "_constructor", "constructor", "componentName", "displayName", "name", "<PERSON><PERSON><PERSON>", "ReactNoopUpdateQueue", "isMounted", "enqueueForceUpdate", "callback", "enqueueReplaceState", "completeState", "enqueueSetState", "partialState", "assign", "Object", "emptyObject", "freeze", "Component", "props", "context", "updater", "refs", "isReactComponent", "setState", "forceUpdate", "deprecatedAPIs", "replaceState", "defineDeprecationWarning", "methodName", "info", "defineProperty", "get", "undefined", "fnName", "hasOwnProperty", "ComponentDummy", "PureComponent", "pureComponentPrototype", "isPureReactComponent", "createRef", "refObject", "seal", "isArrayImpl", "isArray", "a", "typeName", "value", "hasToStringTag", "toStringTag", "type", "willCoercionThrow", "testStringCoercion", "e", "checkKeyStringCoercion", "getWrappedName", "outerType", "innerType", "wrapperName", "functionName", "getContextName", "getComponentNameFromType", "tag", "$$typeof", "provider", "_context", "render", "outerName", "lazyComponent", "payload", "_payload", "init", "_init", "x", "RESERVED_PROPS", "key", "ref", "__self", "__source", "specialPropKeyWarningShown", "specialPropRefWarningShown", "didWarnAboutStringRefs", "hasValidRef", "config", "getter", "getOwnPropertyDescriptor", "isReactWarning", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defineKeyPropWarningGetter", "warnAboutAccessingKey", "configurable", "defineRefPropWarningGetter", "warnAboutAccessingRef", "warnIfStringRefCannotBeAutoConverted", "stateNode", "ReactElement", "self", "source", "owner", "element", "_owner", "_store", "enumerable", "writable", "createElement", "children", "propName", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "i", "defaultProps", "cloneAndReplaceKey", "oldElement", "new<PERSON>ey", "newElement", "_self", "_source", "cloneElement", "isValidElement", "object", "SEPARATOR", "SUBSEPARATOR", "escape", "escapeRegex", "escaper<PERSON><PERSON><PERSON>", "escapedString", "replace", "match", "didWarnAboutMaps", "userProvidedKeyEscapeRegex", "escapeUserProvidedKey", "text", "get<PERSON><PERSON><PERSON><PERSON>", "index", "toString", "mapIntoArray", "array", "escapedPrefix", "nameSoFar", "invokeCallback", "_child", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "c", "push", "child", "nextName", "subtreeCount", "nextNamePrefix", "iteratorFn", "iterable<PERSON><PERSON><PERSON>n", "entries", "step", "ii", "next", "done", "childrenString", "keys", "join", "mapChildren", "func", "result", "count", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "n", "forEachChildren", "forEachFunc", "forEachContext", "toArray", "<PERSON><PERSON><PERSON><PERSON>", "createContext", "defaultValue", "_currentValue", "_currentValue2", "_threadCount", "Provider", "Consumer", "_defaultValue", "_globalName", "hasWarnedAboutUsingNestedContextConsumers", "hasWarnedAboutUsingConsumerProvider", "hasWarnedAboutDisplayNameOnConsumer", "defineProperties", "set", "_Provider", "_current<PERSON><PERSON><PERSON>", "_currentRenderer2", "Uninitialized", "Pending", "Resolved", "Rejected", "lazyInitializer", "_status", "ctor", "_result", "thenable", "then", "moduleObject", "resolved", "rejected", "pending", "default", "lazy", "lazyType", "propTypes", "newDefaultProps", "newPropTypes", "forwardRef", "elementType", "ownName", "REACT_MODULE_REFERENCE", "isValidElementType", "getModuleId", "memo", "compare", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dispatcher", "useContext", "Context", "realContext", "useState", "initialState", "useReducer", "reducer", "initialArg", "useRef", "initialValue", "useEffect", "create", "deps", "useInsertionEffect", "useLayoutEffect", "useCallback", "useMemo", "useImperativeHandle", "useDebugValue", "formatterFn", "useTransition", "useDeferredValue", "useId", "useSyncExternalStore", "subscribe", "getSnapshot", "getServerSnapshot", "<PERSON><PERSON><PERSON><PERSON>", "prevLog", "prevInfo", "prev<PERSON>arn", "prevError", "prevGroup", "prevGroupCollapsed", "prevGroupEnd", "disabledLog", "__reactDisabledLog", "disableLogs", "log", "group", "groupCollapsed", "groupEnd", "reenableLogs", "ReactCurrentDispatcher$1", "prefix", "describeBuiltInComponentFrame", "ownerFn", "trim", "reentry", "componentFrameCache", "PossiblyWeakMap", "WeakMap", "Map", "describeNativeComponentFrame", "fn", "construct", "frame", "control", "previousPrepareStackTrace", "prepareStackTrace", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Fake", "Reflect", "sample", "sampleLines", "split", "controlLines", "s", "_frame", "includes", "syntheticFrame", "describeFunctionComponentFrame", "shouldConstruct", "describeUnknownElementTypeFrameInDEV", "loggedTypeFailures", "ReactDebugCurrentFrame$1", "setCurrentlyValidatingElement", "checkPropTypes", "typeSpecs", "values", "location", "has", "bind", "typeSpecName", "error$1", "err", "ex", "message", "setCurrentlyValidatingElement$1", "propTypesMisspellWarningShown", "getDeclarationErrorAddendum", "getSourceInfoErrorAddendum", "fileName", "lineNumber", "getSourceInfoErrorAddendumForProps", "elementProps", "ownerHasKeyUseWarning", "getCurrentComponentErrorInfo", "parentType", "parentName", "validateExplicitKey", "validated", "currentComponentErrorInfo", "childOwner", "validate<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "node", "validatePropTypes", "PropTypes", "_name", "getDefaultProps", "isReactClassApproved", "validateFragmentProps", "fragment", "createElementWithValidation", "validType", "sourceInfo", "typeString", "didWarnAboutDeprecatedCreateFactory", "createFactoryWithValidation", "validatedFactory", "cloneElementWithValidation", "startTransition", "scope", "options", "prevTransition", "currentTransition", "_updatedFibers", "Set", "updatedFibersCount", "size", "clear", "didWarnAboutMessageChannel", "enqueueTaskImpl", "enqueueTask", "task", "requireString", "Math", "random", "slice", "nodeRequire", "module", "setImmediate", "_err", "MessageChannel", "channel", "port1", "onmessage", "port2", "postMessage", "actScopeDepth", "didWarnNoAwaitAct", "act", "prevActScope<PERSON>epth", "prevIsBatchingLegacy", "queue", "flushActQueue", "popActScope", "thenableResult", "wasAwaited", "resolve", "reject", "returnValue", "recursivelyFlushAsyncActWork", "Promise", "_queue", "_thenable", "_thenable2", "isFlushing", "createElement$1", "cloneElement$1", "createFactory", "Children", "for<PERSON>ach", "only", "exports", "Fragment", "Profiler", "StrictMode", "Suspense", "__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED", "unstable_act", "version", "registerInternalModuleStop"], "sources": ["/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/node_modules/react/cjs/react.development.js"], "sourcesContent": ["/**\n * @license React\n * react.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n\n          'use strict';\n\n/* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error());\n}\n          var ReactVersion = '18.3.1';\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types.\nvar REACT_ELEMENT_TYPE = Symbol.for('react.element');\nvar REACT_PORTAL_TYPE = Symbol.for('react.portal');\nvar REACT_FRAGMENT_TYPE = Symbol.for('react.fragment');\nvar REACT_STRICT_MODE_TYPE = Symbol.for('react.strict_mode');\nvar REACT_PROFILER_TYPE = Symbol.for('react.profiler');\nvar REACT_PROVIDER_TYPE = Symbol.for('react.provider');\nvar REACT_CONTEXT_TYPE = Symbol.for('react.context');\nvar REACT_FORWARD_REF_TYPE = Symbol.for('react.forward_ref');\nvar REACT_SUSPENSE_TYPE = Symbol.for('react.suspense');\nvar REACT_SUSPENSE_LIST_TYPE = Symbol.for('react.suspense_list');\nvar REACT_MEMO_TYPE = Symbol.for('react.memo');\nvar REACT_LAZY_TYPE = Symbol.for('react.lazy');\nvar REACT_OFFSCREEN_TYPE = Symbol.for('react.offscreen');\nvar MAYBE_ITERATOR_SYMBOL = Symbol.iterator;\nvar FAUX_ITERATOR_SYMBOL = '@@iterator';\nfunction getIteratorFn(maybeIterable) {\n  if (maybeIterable === null || typeof maybeIterable !== 'object') {\n    return null;\n  }\n\n  var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n\n  if (typeof maybeIterator === 'function') {\n    return maybeIterator;\n  }\n\n  return null;\n}\n\n/**\n * Keeps track of the current dispatcher.\n */\nvar ReactCurrentDispatcher = {\n  /**\n   * @internal\n   * @type {ReactComponent}\n   */\n  current: null\n};\n\n/**\n * Keeps track of the current batch's configuration such as how long an update\n * should suspend for if it needs to.\n */\nvar ReactCurrentBatchConfig = {\n  transition: null\n};\n\nvar ReactCurrentActQueue = {\n  current: null,\n  // Used to reproduce behavior of `batchedUpdates` in legacy mode.\n  isBatchingLegacy: false,\n  didScheduleLegacyUpdate: false\n};\n\n/**\n * Keeps track of the current owner.\n *\n * The current owner is the component who should own any components that are\n * currently being constructed.\n */\nvar ReactCurrentOwner = {\n  /**\n   * @internal\n   * @type {ReactComponent}\n   */\n  current: null\n};\n\nvar ReactDebugCurrentFrame = {};\nvar currentExtraStackFrame = null;\nfunction setExtraStackFrame(stack) {\n  {\n    currentExtraStackFrame = stack;\n  }\n}\n\n{\n  ReactDebugCurrentFrame.setExtraStackFrame = function (stack) {\n    {\n      currentExtraStackFrame = stack;\n    }\n  }; // Stack implementation injected by the current renderer.\n\n\n  ReactDebugCurrentFrame.getCurrentStack = null;\n\n  ReactDebugCurrentFrame.getStackAddendum = function () {\n    var stack = ''; // Add an extra top frame while an element is being validated\n\n    if (currentExtraStackFrame) {\n      stack += currentExtraStackFrame;\n    } // Delegate to the injected renderer-specific implementation\n\n\n    var impl = ReactDebugCurrentFrame.getCurrentStack;\n\n    if (impl) {\n      stack += impl() || '';\n    }\n\n    return stack;\n  };\n}\n\n// -----------------------------------------------------------------------------\n\nvar enableScopeAPI = false; // Experimental Create Event Handle API.\nvar enableCacheElement = false;\nvar enableTransitionTracing = false; // No known bugs, but needs performance testing\n\nvar enableLegacyHidden = false; // Enables unstable_avoidThisFallback feature in Fiber\n// stuff. Intended to enable React core members to more easily debug scheduling\n// issues in DEV builds.\n\nvar enableDebugTracing = false; // Track which Fiber(s) schedule render work.\n\nvar ReactSharedInternals = {\n  ReactCurrentDispatcher: ReactCurrentDispatcher,\n  ReactCurrentBatchConfig: ReactCurrentBatchConfig,\n  ReactCurrentOwner: ReactCurrentOwner\n};\n\n{\n  ReactSharedInternals.ReactDebugCurrentFrame = ReactDebugCurrentFrame;\n  ReactSharedInternals.ReactCurrentActQueue = ReactCurrentActQueue;\n}\n\n// by calls to these methods by a Babel plugin.\n//\n// In PROD (or in packages without access to React internals),\n// they are left as they are instead.\n\nfunction warn(format) {\n  {\n    {\n      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n\n      printWarning('warn', format, args);\n    }\n  }\n}\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\nvar didWarnStateUpdateForUnmountedComponent = {};\n\nfunction warnNoop(publicInstance, callerName) {\n  {\n    var _constructor = publicInstance.constructor;\n    var componentName = _constructor && (_constructor.displayName || _constructor.name) || 'ReactClass';\n    var warningKey = componentName + \".\" + callerName;\n\n    if (didWarnStateUpdateForUnmountedComponent[warningKey]) {\n      return;\n    }\n\n    error(\"Can't call %s on a component that is not yet mounted. \" + 'This is a no-op, but it might indicate a bug in your application. ' + 'Instead, assign to `this.state` directly or define a `state = {};` ' + 'class property with the desired state in the %s component.', callerName, componentName);\n\n    didWarnStateUpdateForUnmountedComponent[warningKey] = true;\n  }\n}\n/**\n * This is the abstract API for an update queue.\n */\n\n\nvar ReactNoopUpdateQueue = {\n  /**\n   * Checks whether or not this composite component is mounted.\n   * @param {ReactClass} publicInstance The instance we want to test.\n   * @return {boolean} True if mounted, false otherwise.\n   * @protected\n   * @final\n   */\n  isMounted: function (publicInstance) {\n    return false;\n  },\n\n  /**\n   * Forces an update. This should only be invoked when it is known with\n   * certainty that we are **not** in a DOM transaction.\n   *\n   * You may want to call this when you know that some deeper aspect of the\n   * component's state has changed but `setState` was not called.\n   *\n   * This will not invoke `shouldComponentUpdate`, but it will invoke\n   * `componentWillUpdate` and `componentDidUpdate`.\n   *\n   * @param {ReactClass} publicInstance The instance that should rerender.\n   * @param {?function} callback Called after component is updated.\n   * @param {?string} callerName name of the calling function in the public API.\n   * @internal\n   */\n  enqueueForceUpdate: function (publicInstance, callback, callerName) {\n    warnNoop(publicInstance, 'forceUpdate');\n  },\n\n  /**\n   * Replaces all of the state. Always use this or `setState` to mutate state.\n   * You should treat `this.state` as immutable.\n   *\n   * There is no guarantee that `this.state` will be immediately updated, so\n   * accessing `this.state` after calling this method may return the old value.\n   *\n   * @param {ReactClass} publicInstance The instance that should rerender.\n   * @param {object} completeState Next state.\n   * @param {?function} callback Called after component is updated.\n   * @param {?string} callerName name of the calling function in the public API.\n   * @internal\n   */\n  enqueueReplaceState: function (publicInstance, completeState, callback, callerName) {\n    warnNoop(publicInstance, 'replaceState');\n  },\n\n  /**\n   * Sets a subset of the state. This only exists because _pendingState is\n   * internal. This provides a merging strategy that is not available to deep\n   * properties which is confusing. TODO: Expose pendingState or don't use it\n   * during the merge.\n   *\n   * @param {ReactClass} publicInstance The instance that should rerender.\n   * @param {object} partialState Next partial state to be merged with state.\n   * @param {?function} callback Called after component is updated.\n   * @param {?string} Name of the calling function in the public API.\n   * @internal\n   */\n  enqueueSetState: function (publicInstance, partialState, callback, callerName) {\n    warnNoop(publicInstance, 'setState');\n  }\n};\n\nvar assign = Object.assign;\n\nvar emptyObject = {};\n\n{\n  Object.freeze(emptyObject);\n}\n/**\n * Base class helpers for the updating state of a component.\n */\n\n\nfunction Component(props, context, updater) {\n  this.props = props;\n  this.context = context; // If a component has string refs, we will assign a different object later.\n\n  this.refs = emptyObject; // We initialize the default updater but the real one gets injected by the\n  // renderer.\n\n  this.updater = updater || ReactNoopUpdateQueue;\n}\n\nComponent.prototype.isReactComponent = {};\n/**\n * Sets a subset of the state. Always use this to mutate\n * state. You should treat `this.state` as immutable.\n *\n * There is no guarantee that `this.state` will be immediately updated, so\n * accessing `this.state` after calling this method may return the old value.\n *\n * There is no guarantee that calls to `setState` will run synchronously,\n * as they may eventually be batched together.  You can provide an optional\n * callback that will be executed when the call to setState is actually\n * completed.\n *\n * When a function is provided to setState, it will be called at some point in\n * the future (not synchronously). It will be called with the up to date\n * component arguments (state, props, context). These values can be different\n * from this.* because your function may be called after receiveProps but before\n * shouldComponentUpdate, and this new state, props, and context will not yet be\n * assigned to this.\n *\n * @param {object|function} partialState Next partial state or function to\n *        produce next partial state to be merged with current state.\n * @param {?function} callback Called after state is updated.\n * @final\n * @protected\n */\n\nComponent.prototype.setState = function (partialState, callback) {\n  if (typeof partialState !== 'object' && typeof partialState !== 'function' && partialState != null) {\n    throw new Error('setState(...): takes an object of state variables to update or a ' + 'function which returns an object of state variables.');\n  }\n\n  this.updater.enqueueSetState(this, partialState, callback, 'setState');\n};\n/**\n * Forces an update. This should only be invoked when it is known with\n * certainty that we are **not** in a DOM transaction.\n *\n * You may want to call this when you know that some deeper aspect of the\n * component's state has changed but `setState` was not called.\n *\n * This will not invoke `shouldComponentUpdate`, but it will invoke\n * `componentWillUpdate` and `componentDidUpdate`.\n *\n * @param {?function} callback Called after update is complete.\n * @final\n * @protected\n */\n\n\nComponent.prototype.forceUpdate = function (callback) {\n  this.updater.enqueueForceUpdate(this, callback, 'forceUpdate');\n};\n/**\n * Deprecated APIs. These APIs used to exist on classic React classes but since\n * we would like to deprecate them, we're not going to move them over to this\n * modern base class. Instead, we define a getter that warns if it's accessed.\n */\n\n\n{\n  var deprecatedAPIs = {\n    isMounted: ['isMounted', 'Instead, make sure to clean up subscriptions and pending requests in ' + 'componentWillUnmount to prevent memory leaks.'],\n    replaceState: ['replaceState', 'Refactor your code to use setState instead (see ' + 'https://github.com/facebook/react/issues/3236).']\n  };\n\n  var defineDeprecationWarning = function (methodName, info) {\n    Object.defineProperty(Component.prototype, methodName, {\n      get: function () {\n        warn('%s(...) is deprecated in plain JavaScript React classes. %s', info[0], info[1]);\n\n        return undefined;\n      }\n    });\n  };\n\n  for (var fnName in deprecatedAPIs) {\n    if (deprecatedAPIs.hasOwnProperty(fnName)) {\n      defineDeprecationWarning(fnName, deprecatedAPIs[fnName]);\n    }\n  }\n}\n\nfunction ComponentDummy() {}\n\nComponentDummy.prototype = Component.prototype;\n/**\n * Convenience component with default shallow equality check for sCU.\n */\n\nfunction PureComponent(props, context, updater) {\n  this.props = props;\n  this.context = context; // If a component has string refs, we will assign a different object later.\n\n  this.refs = emptyObject;\n  this.updater = updater || ReactNoopUpdateQueue;\n}\n\nvar pureComponentPrototype = PureComponent.prototype = new ComponentDummy();\npureComponentPrototype.constructor = PureComponent; // Avoid an extra prototype jump for these methods.\n\nassign(pureComponentPrototype, Component.prototype);\npureComponentPrototype.isPureReactComponent = true;\n\n// an immutable object with a single mutable value\nfunction createRef() {\n  var refObject = {\n    current: null\n  };\n\n  {\n    Object.seal(refObject);\n  }\n\n  return refObject;\n}\n\nvar isArrayImpl = Array.isArray; // eslint-disable-next-line no-redeclare\n\nfunction isArray(a) {\n  return isArrayImpl(a);\n}\n\n/*\n * The `'' + value` pattern (used in in perf-sensitive code) throws for Symbol\n * and Temporal.* types. See https://github.com/facebook/react/pull/22064.\n *\n * The functions in this module will throw an easier-to-understand,\n * easier-to-debug exception with a clear errors message message explaining the\n * problem. (Instead of a confusing exception thrown inside the implementation\n * of the `value` object).\n */\n// $FlowFixMe only called in DEV, so void return is not possible.\nfunction typeName(value) {\n  {\n    // toStringTag is needed for namespaced types like Temporal.Instant\n    var hasToStringTag = typeof Symbol === 'function' && Symbol.toStringTag;\n    var type = hasToStringTag && value[Symbol.toStringTag] || value.constructor.name || 'Object';\n    return type;\n  }\n} // $FlowFixMe only called in DEV, so void return is not possible.\n\n\nfunction willCoercionThrow(value) {\n  {\n    try {\n      testStringCoercion(value);\n      return false;\n    } catch (e) {\n      return true;\n    }\n  }\n}\n\nfunction testStringCoercion(value) {\n  // If you ended up here by following an exception call stack, here's what's\n  // happened: you supplied an object or symbol value to React (as a prop, key,\n  // DOM attribute, CSS property, string ref, etc.) and when React tried to\n  // coerce it to a string using `'' + value`, an exception was thrown.\n  //\n  // The most common types that will cause this exception are `Symbol` instances\n  // and Temporal objects like `Temporal.Instant`. But any object that has a\n  // `valueOf` or `[Symbol.toPrimitive]` method that throws will also cause this\n  // exception. (Library authors do this to prevent users from using built-in\n  // numeric operators like `+` or comparison operators like `>=` because custom\n  // methods are needed to perform accurate arithmetic or comparison.)\n  //\n  // To fix the problem, coerce this object or symbol value to a string before\n  // passing it to React. The most reliable way is usually `String(value)`.\n  //\n  // To find which value is throwing, check the browser or debugger console.\n  // Before this exception was thrown, there should be `console.error` output\n  // that shows the type (Symbol, Temporal.PlainDate, etc.) that caused the\n  // problem and how that type was used: key, atrribute, input value prop, etc.\n  // In most cases, this console output also shows the component and its\n  // ancestor components where the exception happened.\n  //\n  // eslint-disable-next-line react-internal/safe-string-coercion\n  return '' + value;\n}\nfunction checkKeyStringCoercion(value) {\n  {\n    if (willCoercionThrow(value)) {\n      error('The provided key is an unsupported type %s.' + ' This value must be coerced to a string before before using it here.', typeName(value));\n\n      return testStringCoercion(value); // throw (to help callers find troubleshooting comments)\n    }\n  }\n}\n\nfunction getWrappedName(outerType, innerType, wrapperName) {\n  var displayName = outerType.displayName;\n\n  if (displayName) {\n    return displayName;\n  }\n\n  var functionName = innerType.displayName || innerType.name || '';\n  return functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName;\n} // Keep in sync with react-reconciler/getComponentNameFromFiber\n\n\nfunction getContextName(type) {\n  return type.displayName || 'Context';\n} // Note that the reconciler package should generally prefer to use getComponentNameFromFiber() instead.\n\n\nfunction getComponentNameFromType(type) {\n  if (type == null) {\n    // Host root, text node or just invalid type.\n    return null;\n  }\n\n  {\n    if (typeof type.tag === 'number') {\n      error('Received an unexpected object in getComponentNameFromType(). ' + 'This is likely a bug in React. Please file an issue.');\n    }\n  }\n\n  if (typeof type === 'function') {\n    return type.displayName || type.name || null;\n  }\n\n  if (typeof type === 'string') {\n    return type;\n  }\n\n  switch (type) {\n    case REACT_FRAGMENT_TYPE:\n      return 'Fragment';\n\n    case REACT_PORTAL_TYPE:\n      return 'Portal';\n\n    case REACT_PROFILER_TYPE:\n      return 'Profiler';\n\n    case REACT_STRICT_MODE_TYPE:\n      return 'StrictMode';\n\n    case REACT_SUSPENSE_TYPE:\n      return 'Suspense';\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return 'SuspenseList';\n\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_CONTEXT_TYPE:\n        var context = type;\n        return getContextName(context) + '.Consumer';\n\n      case REACT_PROVIDER_TYPE:\n        var provider = type;\n        return getContextName(provider._context) + '.Provider';\n\n      case REACT_FORWARD_REF_TYPE:\n        return getWrappedName(type, type.render, 'ForwardRef');\n\n      case REACT_MEMO_TYPE:\n        var outerName = type.displayName || null;\n\n        if (outerName !== null) {\n          return outerName;\n        }\n\n        return getComponentNameFromType(type.type) || 'Memo';\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            return getComponentNameFromType(init(payload));\n          } catch (x) {\n            return null;\n          }\n        }\n\n      // eslint-disable-next-line no-fallthrough\n    }\n  }\n\n  return null;\n}\n\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\nvar RESERVED_PROPS = {\n  key: true,\n  ref: true,\n  __self: true,\n  __source: true\n};\nvar specialPropKeyWarningShown, specialPropRefWarningShown, didWarnAboutStringRefs;\n\n{\n  didWarnAboutStringRefs = {};\n}\n\nfunction hasValidRef(config) {\n  {\n    if (hasOwnProperty.call(config, 'ref')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.ref !== undefined;\n}\n\nfunction hasValidKey(config) {\n  {\n    if (hasOwnProperty.call(config, 'key')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.key !== undefined;\n}\n\nfunction defineKeyPropWarningGetter(props, displayName) {\n  var warnAboutAccessingKey = function () {\n    {\n      if (!specialPropKeyWarningShown) {\n        specialPropKeyWarningShown = true;\n\n        error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    }\n  };\n\n  warnAboutAccessingKey.isReactWarning = true;\n  Object.defineProperty(props, 'key', {\n    get: warnAboutAccessingKey,\n    configurable: true\n  });\n}\n\nfunction defineRefPropWarningGetter(props, displayName) {\n  var warnAboutAccessingRef = function () {\n    {\n      if (!specialPropRefWarningShown) {\n        specialPropRefWarningShown = true;\n\n        error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    }\n  };\n\n  warnAboutAccessingRef.isReactWarning = true;\n  Object.defineProperty(props, 'ref', {\n    get: warnAboutAccessingRef,\n    configurable: true\n  });\n}\n\nfunction warnIfStringRefCannotBeAutoConverted(config) {\n  {\n    if (typeof config.ref === 'string' && ReactCurrentOwner.current && config.__self && ReactCurrentOwner.current.stateNode !== config.__self) {\n      var componentName = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (!didWarnAboutStringRefs[componentName]) {\n        error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', componentName, config.ref);\n\n        didWarnAboutStringRefs[componentName] = true;\n      }\n    }\n  }\n}\n/**\n * Factory method to create a new React element. This no longer adheres to\n * the class pattern, so do not use new to call it. Also, instanceof check\n * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n * if something is a React Element.\n *\n * @param {*} type\n * @param {*} props\n * @param {*} key\n * @param {string|object} ref\n * @param {*} owner\n * @param {*} self A *temporary* helper to detect places where `this` is\n * different from the `owner` when React.createElement is called, so that we\n * can warn. We want to get rid of owner and replace string `ref`s with arrow\n * functions, and as long as `this` and owner are the same, there will be no\n * change in behavior.\n * @param {*} source An annotation object (added by a transpiler or otherwise)\n * indicating filename, line number, and/or other information.\n * @internal\n */\n\n\nvar ReactElement = function (type, key, ref, self, source, owner, props) {\n  var element = {\n    // This tag allows us to uniquely identify this as a React Element\n    $$typeof: REACT_ELEMENT_TYPE,\n    // Built-in properties that belong on the element\n    type: type,\n    key: key,\n    ref: ref,\n    props: props,\n    // Record the component responsible for creating this element.\n    _owner: owner\n  };\n\n  {\n    // The validation flag is currently mutative. We put it on\n    // an external backing store so that we can freeze the whole object.\n    // This can be replaced with a WeakMap once they are implemented in\n    // commonly used development environments.\n    element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n    // the validation flag non-enumerable (where possible, which should\n    // include every environment we run tests in), so the test framework\n    // ignores it.\n\n    Object.defineProperty(element._store, 'validated', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: false\n    }); // self and source are DEV only properties.\n\n    Object.defineProperty(element, '_self', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: self\n    }); // Two elements created in two different places should be considered\n    // equal for testing purposes and therefore we hide it from enumeration.\n\n    Object.defineProperty(element, '_source', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: source\n    });\n\n    if (Object.freeze) {\n      Object.freeze(element.props);\n      Object.freeze(element);\n    }\n  }\n\n  return element;\n};\n/**\n * Create and return a new ReactElement of the given type.\n * See https://reactjs.org/docs/react-api.html#createelement\n */\n\nfunction createElement(type, config, children) {\n  var propName; // Reserved names are extracted\n\n  var props = {};\n  var key = null;\n  var ref = null;\n  var self = null;\n  var source = null;\n\n  if (config != null) {\n    if (hasValidRef(config)) {\n      ref = config.ref;\n\n      {\n        warnIfStringRefCannotBeAutoConverted(config);\n      }\n    }\n\n    if (hasValidKey(config)) {\n      {\n        checkKeyStringCoercion(config.key);\n      }\n\n      key = '' + config.key;\n    }\n\n    self = config.__self === undefined ? null : config.__self;\n    source = config.__source === undefined ? null : config.__source; // Remaining properties are added to a new props object\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && !RESERVED_PROPS.hasOwnProperty(propName)) {\n        props[propName] = config[propName];\n      }\n    }\n  } // Children can be more than one argument, and those are transferred onto\n  // the newly allocated props object.\n\n\n  var childrenLength = arguments.length - 2;\n\n  if (childrenLength === 1) {\n    props.children = children;\n  } else if (childrenLength > 1) {\n    var childArray = Array(childrenLength);\n\n    for (var i = 0; i < childrenLength; i++) {\n      childArray[i] = arguments[i + 2];\n    }\n\n    {\n      if (Object.freeze) {\n        Object.freeze(childArray);\n      }\n    }\n\n    props.children = childArray;\n  } // Resolve default props\n\n\n  if (type && type.defaultProps) {\n    var defaultProps = type.defaultProps;\n\n    for (propName in defaultProps) {\n      if (props[propName] === undefined) {\n        props[propName] = defaultProps[propName];\n      }\n    }\n  }\n\n  {\n    if (key || ref) {\n      var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\n\n      if (key) {\n        defineKeyPropWarningGetter(props, displayName);\n      }\n\n      if (ref) {\n        defineRefPropWarningGetter(props, displayName);\n      }\n    }\n  }\n\n  return ReactElement(type, key, ref, self, source, ReactCurrentOwner.current, props);\n}\nfunction cloneAndReplaceKey(oldElement, newKey) {\n  var newElement = ReactElement(oldElement.type, newKey, oldElement.ref, oldElement._self, oldElement._source, oldElement._owner, oldElement.props);\n  return newElement;\n}\n/**\n * Clone and return a new ReactElement using element as the starting point.\n * See https://reactjs.org/docs/react-api.html#cloneelement\n */\n\nfunction cloneElement(element, config, children) {\n  if (element === null || element === undefined) {\n    throw new Error(\"React.cloneElement(...): The argument must be a React element, but you passed \" + element + \".\");\n  }\n\n  var propName; // Original props are copied\n\n  var props = assign({}, element.props); // Reserved names are extracted\n\n  var key = element.key;\n  var ref = element.ref; // Self is preserved since the owner is preserved.\n\n  var self = element._self; // Source is preserved since cloneElement is unlikely to be targeted by a\n  // transpiler, and the original source is probably a better indicator of the\n  // true owner.\n\n  var source = element._source; // Owner will be preserved, unless ref is overridden\n\n  var owner = element._owner;\n\n  if (config != null) {\n    if (hasValidRef(config)) {\n      // Silently steal the ref from the parent.\n      ref = config.ref;\n      owner = ReactCurrentOwner.current;\n    }\n\n    if (hasValidKey(config)) {\n      {\n        checkKeyStringCoercion(config.key);\n      }\n\n      key = '' + config.key;\n    } // Remaining properties override existing props\n\n\n    var defaultProps;\n\n    if (element.type && element.type.defaultProps) {\n      defaultProps = element.type.defaultProps;\n    }\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && !RESERVED_PROPS.hasOwnProperty(propName)) {\n        if (config[propName] === undefined && defaultProps !== undefined) {\n          // Resolve default props\n          props[propName] = defaultProps[propName];\n        } else {\n          props[propName] = config[propName];\n        }\n      }\n    }\n  } // Children can be more than one argument, and those are transferred onto\n  // the newly allocated props object.\n\n\n  var childrenLength = arguments.length - 2;\n\n  if (childrenLength === 1) {\n    props.children = children;\n  } else if (childrenLength > 1) {\n    var childArray = Array(childrenLength);\n\n    for (var i = 0; i < childrenLength; i++) {\n      childArray[i] = arguments[i + 2];\n    }\n\n    props.children = childArray;\n  }\n\n  return ReactElement(element.type, key, ref, self, source, owner, props);\n}\n/**\n * Verifies the object is a ReactElement.\n * See https://reactjs.org/docs/react-api.html#isvalidelement\n * @param {?object} object\n * @return {boolean} True if `object` is a ReactElement.\n * @final\n */\n\nfunction isValidElement(object) {\n  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n}\n\nvar SEPARATOR = '.';\nvar SUBSEPARATOR = ':';\n/**\n * Escape and wrap key so it is safe to use as a reactid\n *\n * @param {string} key to be escaped.\n * @return {string} the escaped key.\n */\n\nfunction escape(key) {\n  var escapeRegex = /[=:]/g;\n  var escaperLookup = {\n    '=': '=0',\n    ':': '=2'\n  };\n  var escapedString = key.replace(escapeRegex, function (match) {\n    return escaperLookup[match];\n  });\n  return '$' + escapedString;\n}\n/**\n * TODO: Test that a single child and an array with one item have the same key\n * pattern.\n */\n\n\nvar didWarnAboutMaps = false;\nvar userProvidedKeyEscapeRegex = /\\/+/g;\n\nfunction escapeUserProvidedKey(text) {\n  return text.replace(userProvidedKeyEscapeRegex, '$&/');\n}\n/**\n * Generate a key string that identifies a element within a set.\n *\n * @param {*} element A element that could contain a manual key.\n * @param {number} index Index that is used if a manual key is not provided.\n * @return {string}\n */\n\n\nfunction getElementKey(element, index) {\n  // Do some typechecking here since we call this blindly. We want to ensure\n  // that we don't block potential future ES APIs.\n  if (typeof element === 'object' && element !== null && element.key != null) {\n    // Explicit key\n    {\n      checkKeyStringCoercion(element.key);\n    }\n\n    return escape('' + element.key);\n  } // Implicit key determined by the index in the set\n\n\n  return index.toString(36);\n}\n\nfunction mapIntoArray(children, array, escapedPrefix, nameSoFar, callback) {\n  var type = typeof children;\n\n  if (type === 'undefined' || type === 'boolean') {\n    // All of the above are perceived as null.\n    children = null;\n  }\n\n  var invokeCallback = false;\n\n  if (children === null) {\n    invokeCallback = true;\n  } else {\n    switch (type) {\n      case 'string':\n      case 'number':\n        invokeCallback = true;\n        break;\n\n      case 'object':\n        switch (children.$$typeof) {\n          case REACT_ELEMENT_TYPE:\n          case REACT_PORTAL_TYPE:\n            invokeCallback = true;\n        }\n\n    }\n  }\n\n  if (invokeCallback) {\n    var _child = children;\n    var mappedChild = callback(_child); // If it's the only child, treat the name as if it was wrapped in an array\n    // so that it's consistent if the number of children grows:\n\n    var childKey = nameSoFar === '' ? SEPARATOR + getElementKey(_child, 0) : nameSoFar;\n\n    if (isArray(mappedChild)) {\n      var escapedChildKey = '';\n\n      if (childKey != null) {\n        escapedChildKey = escapeUserProvidedKey(childKey) + '/';\n      }\n\n      mapIntoArray(mappedChild, array, escapedChildKey, '', function (c) {\n        return c;\n      });\n    } else if (mappedChild != null) {\n      if (isValidElement(mappedChild)) {\n        {\n          // The `if` statement here prevents auto-disabling of the safe\n          // coercion ESLint rule, so we must manually disable it below.\n          // $FlowFixMe Flow incorrectly thinks React.Portal doesn't have a key\n          if (mappedChild.key && (!_child || _child.key !== mappedChild.key)) {\n            checkKeyStringCoercion(mappedChild.key);\n          }\n        }\n\n        mappedChild = cloneAndReplaceKey(mappedChild, // Keep both the (mapped) and old keys if they differ, just as\n        // traverseAllChildren used to do for objects as children\n        escapedPrefix + ( // $FlowFixMe Flow incorrectly thinks React.Portal doesn't have a key\n        mappedChild.key && (!_child || _child.key !== mappedChild.key) ? // $FlowFixMe Flow incorrectly thinks existing element's key can be a number\n        // eslint-disable-next-line react-internal/safe-string-coercion\n        escapeUserProvidedKey('' + mappedChild.key) + '/' : '') + childKey);\n      }\n\n      array.push(mappedChild);\n    }\n\n    return 1;\n  }\n\n  var child;\n  var nextName;\n  var subtreeCount = 0; // Count of children found in the current subtree.\n\n  var nextNamePrefix = nameSoFar === '' ? SEPARATOR : nameSoFar + SUBSEPARATOR;\n\n  if (isArray(children)) {\n    for (var i = 0; i < children.length; i++) {\n      child = children[i];\n      nextName = nextNamePrefix + getElementKey(child, i);\n      subtreeCount += mapIntoArray(child, array, escapedPrefix, nextName, callback);\n    }\n  } else {\n    var iteratorFn = getIteratorFn(children);\n\n    if (typeof iteratorFn === 'function') {\n      var iterableChildren = children;\n\n      {\n        // Warn about using Maps as children\n        if (iteratorFn === iterableChildren.entries) {\n          if (!didWarnAboutMaps) {\n            warn('Using Maps as children is not supported. ' + 'Use an array of keyed ReactElements instead.');\n          }\n\n          didWarnAboutMaps = true;\n        }\n      }\n\n      var iterator = iteratorFn.call(iterableChildren);\n      var step;\n      var ii = 0;\n\n      while (!(step = iterator.next()).done) {\n        child = step.value;\n        nextName = nextNamePrefix + getElementKey(child, ii++);\n        subtreeCount += mapIntoArray(child, array, escapedPrefix, nextName, callback);\n      }\n    } else if (type === 'object') {\n      // eslint-disable-next-line react-internal/safe-string-coercion\n      var childrenString = String(children);\n      throw new Error(\"Objects are not valid as a React child (found: \" + (childrenString === '[object Object]' ? 'object with keys {' + Object.keys(children).join(', ') + '}' : childrenString) + \"). \" + 'If you meant to render a collection of children, use an array ' + 'instead.');\n    }\n  }\n\n  return subtreeCount;\n}\n\n/**\n * Maps children that are typically specified as `props.children`.\n *\n * See https://reactjs.org/docs/react-api.html#reactchildrenmap\n *\n * The provided mapFunction(child, index) will be called for each\n * leaf child.\n *\n * @param {?*} children Children tree container.\n * @param {function(*, int)} func The map function.\n * @param {*} context Context for mapFunction.\n * @return {object} Object containing the ordered map of results.\n */\nfunction mapChildren(children, func, context) {\n  if (children == null) {\n    return children;\n  }\n\n  var result = [];\n  var count = 0;\n  mapIntoArray(children, result, '', '', function (child) {\n    return func.call(context, child, count++);\n  });\n  return result;\n}\n/**\n * Count the number of children that are typically specified as\n * `props.children`.\n *\n * See https://reactjs.org/docs/react-api.html#reactchildrencount\n *\n * @param {?*} children Children tree container.\n * @return {number} The number of children.\n */\n\n\nfunction countChildren(children) {\n  var n = 0;\n  mapChildren(children, function () {\n    n++; // Don't return anything\n  });\n  return n;\n}\n\n/**\n * Iterates through children that are typically specified as `props.children`.\n *\n * See https://reactjs.org/docs/react-api.html#reactchildrenforeach\n *\n * The provided forEachFunc(child, index) will be called for each\n * leaf child.\n *\n * @param {?*} children Children tree container.\n * @param {function(*, int)} forEachFunc\n * @param {*} forEachContext Context for forEachContext.\n */\nfunction forEachChildren(children, forEachFunc, forEachContext) {\n  mapChildren(children, function () {\n    forEachFunc.apply(this, arguments); // Don't return anything.\n  }, forEachContext);\n}\n/**\n * Flatten a children object (typically specified as `props.children`) and\n * return an array with appropriately re-keyed children.\n *\n * See https://reactjs.org/docs/react-api.html#reactchildrentoarray\n */\n\n\nfunction toArray(children) {\n  return mapChildren(children, function (child) {\n    return child;\n  }) || [];\n}\n/**\n * Returns the first child in a collection of children and verifies that there\n * is only one child in the collection.\n *\n * See https://reactjs.org/docs/react-api.html#reactchildrenonly\n *\n * The current implementation of this function assumes that a single child gets\n * passed without a wrapper, but the purpose of this helper function is to\n * abstract away the particular structure of children.\n *\n * @param {?object} children Child collection structure.\n * @return {ReactElement} The first and only `ReactElement` contained in the\n * structure.\n */\n\n\nfunction onlyChild(children) {\n  if (!isValidElement(children)) {\n    throw new Error('React.Children.only expected to receive a single React element child.');\n  }\n\n  return children;\n}\n\nfunction createContext(defaultValue) {\n  // TODO: Second argument used to be an optional `calculateChangedBits`\n  // function. Warn to reserve for future use?\n  var context = {\n    $$typeof: REACT_CONTEXT_TYPE,\n    // As a workaround to support multiple concurrent renderers, we categorize\n    // some renderers as primary and others as secondary. We only expect\n    // there to be two concurrent renderers at most: React Native (primary) and\n    // Fabric (secondary); React DOM (primary) and React ART (secondary).\n    // Secondary renderers store their context values on separate fields.\n    _currentValue: defaultValue,\n    _currentValue2: defaultValue,\n    // Used to track how many concurrent renderers this context currently\n    // supports within in a single renderer. Such as parallel server rendering.\n    _threadCount: 0,\n    // These are circular\n    Provider: null,\n    Consumer: null,\n    // Add these to use same hidden class in VM as ServerContext\n    _defaultValue: null,\n    _globalName: null\n  };\n  context.Provider = {\n    $$typeof: REACT_PROVIDER_TYPE,\n    _context: context\n  };\n  var hasWarnedAboutUsingNestedContextConsumers = false;\n  var hasWarnedAboutUsingConsumerProvider = false;\n  var hasWarnedAboutDisplayNameOnConsumer = false;\n\n  {\n    // A separate object, but proxies back to the original context object for\n    // backwards compatibility. It has a different $$typeof, so we can properly\n    // warn for the incorrect usage of Context as a Consumer.\n    var Consumer = {\n      $$typeof: REACT_CONTEXT_TYPE,\n      _context: context\n    }; // $FlowFixMe: Flow complains about not setting a value, which is intentional here\n\n    Object.defineProperties(Consumer, {\n      Provider: {\n        get: function () {\n          if (!hasWarnedAboutUsingConsumerProvider) {\n            hasWarnedAboutUsingConsumerProvider = true;\n\n            error('Rendering <Context.Consumer.Provider> is not supported and will be removed in ' + 'a future major release. Did you mean to render <Context.Provider> instead?');\n          }\n\n          return context.Provider;\n        },\n        set: function (_Provider) {\n          context.Provider = _Provider;\n        }\n      },\n      _currentValue: {\n        get: function () {\n          return context._currentValue;\n        },\n        set: function (_currentValue) {\n          context._currentValue = _currentValue;\n        }\n      },\n      _currentValue2: {\n        get: function () {\n          return context._currentValue2;\n        },\n        set: function (_currentValue2) {\n          context._currentValue2 = _currentValue2;\n        }\n      },\n      _threadCount: {\n        get: function () {\n          return context._threadCount;\n        },\n        set: function (_threadCount) {\n          context._threadCount = _threadCount;\n        }\n      },\n      Consumer: {\n        get: function () {\n          if (!hasWarnedAboutUsingNestedContextConsumers) {\n            hasWarnedAboutUsingNestedContextConsumers = true;\n\n            error('Rendering <Context.Consumer.Consumer> is not supported and will be removed in ' + 'a future major release. Did you mean to render <Context.Consumer> instead?');\n          }\n\n          return context.Consumer;\n        }\n      },\n      displayName: {\n        get: function () {\n          return context.displayName;\n        },\n        set: function (displayName) {\n          if (!hasWarnedAboutDisplayNameOnConsumer) {\n            warn('Setting `displayName` on Context.Consumer has no effect. ' + \"You should set it directly on the context with Context.displayName = '%s'.\", displayName);\n\n            hasWarnedAboutDisplayNameOnConsumer = true;\n          }\n        }\n      }\n    }); // $FlowFixMe: Flow complains about missing properties because it doesn't understand defineProperty\n\n    context.Consumer = Consumer;\n  }\n\n  {\n    context._currentRenderer = null;\n    context._currentRenderer2 = null;\n  }\n\n  return context;\n}\n\nvar Uninitialized = -1;\nvar Pending = 0;\nvar Resolved = 1;\nvar Rejected = 2;\n\nfunction lazyInitializer(payload) {\n  if (payload._status === Uninitialized) {\n    var ctor = payload._result;\n    var thenable = ctor(); // Transition to the next state.\n    // This might throw either because it's missing or throws. If so, we treat it\n    // as still uninitialized and try again next time. Which is the same as what\n    // happens if the ctor or any wrappers processing the ctor throws. This might\n    // end up fixing it if the resolution was a concurrency bug.\n\n    thenable.then(function (moduleObject) {\n      if (payload._status === Pending || payload._status === Uninitialized) {\n        // Transition to the next state.\n        var resolved = payload;\n        resolved._status = Resolved;\n        resolved._result = moduleObject;\n      }\n    }, function (error) {\n      if (payload._status === Pending || payload._status === Uninitialized) {\n        // Transition to the next state.\n        var rejected = payload;\n        rejected._status = Rejected;\n        rejected._result = error;\n      }\n    });\n\n    if (payload._status === Uninitialized) {\n      // In case, we're still uninitialized, then we're waiting for the thenable\n      // to resolve. Set it as pending in the meantime.\n      var pending = payload;\n      pending._status = Pending;\n      pending._result = thenable;\n    }\n  }\n\n  if (payload._status === Resolved) {\n    var moduleObject = payload._result;\n\n    {\n      if (moduleObject === undefined) {\n        error('lazy: Expected the result of a dynamic imp' + 'ort() call. ' + 'Instead received: %s\\n\\nYour code should look like: \\n  ' + // Break up imports to avoid accidentally parsing them as dependencies.\n        'const MyComponent = lazy(() => imp' + \"ort('./MyComponent'))\\n\\n\" + 'Did you accidentally put curly braces around the import?', moduleObject);\n      }\n    }\n\n    {\n      if (!('default' in moduleObject)) {\n        error('lazy: Expected the result of a dynamic imp' + 'ort() call. ' + 'Instead received: %s\\n\\nYour code should look like: \\n  ' + // Break up imports to avoid accidentally parsing them as dependencies.\n        'const MyComponent = lazy(() => imp' + \"ort('./MyComponent'))\", moduleObject);\n      }\n    }\n\n    return moduleObject.default;\n  } else {\n    throw payload._result;\n  }\n}\n\nfunction lazy(ctor) {\n  var payload = {\n    // We use these fields to store the result.\n    _status: Uninitialized,\n    _result: ctor\n  };\n  var lazyType = {\n    $$typeof: REACT_LAZY_TYPE,\n    _payload: payload,\n    _init: lazyInitializer\n  };\n\n  {\n    // In production, this would just set it on the object.\n    var defaultProps;\n    var propTypes; // $FlowFixMe\n\n    Object.defineProperties(lazyType, {\n      defaultProps: {\n        configurable: true,\n        get: function () {\n          return defaultProps;\n        },\n        set: function (newDefaultProps) {\n          error('React.lazy(...): It is not supported to assign `defaultProps` to ' + 'a lazy component import. Either specify them where the component ' + 'is defined, or create a wrapping component around it.');\n\n          defaultProps = newDefaultProps; // Match production behavior more closely:\n          // $FlowFixMe\n\n          Object.defineProperty(lazyType, 'defaultProps', {\n            enumerable: true\n          });\n        }\n      },\n      propTypes: {\n        configurable: true,\n        get: function () {\n          return propTypes;\n        },\n        set: function (newPropTypes) {\n          error('React.lazy(...): It is not supported to assign `propTypes` to ' + 'a lazy component import. Either specify them where the component ' + 'is defined, or create a wrapping component around it.');\n\n          propTypes = newPropTypes; // Match production behavior more closely:\n          // $FlowFixMe\n\n          Object.defineProperty(lazyType, 'propTypes', {\n            enumerable: true\n          });\n        }\n      }\n    });\n  }\n\n  return lazyType;\n}\n\nfunction forwardRef(render) {\n  {\n    if (render != null && render.$$typeof === REACT_MEMO_TYPE) {\n      error('forwardRef requires a render function but received a `memo` ' + 'component. Instead of forwardRef(memo(...)), use ' + 'memo(forwardRef(...)).');\n    } else if (typeof render !== 'function') {\n      error('forwardRef requires a render function but was given %s.', render === null ? 'null' : typeof render);\n    } else {\n      if (render.length !== 0 && render.length !== 2) {\n        error('forwardRef render functions accept exactly two parameters: props and ref. %s', render.length === 1 ? 'Did you forget to use the ref parameter?' : 'Any additional parameter will be undefined.');\n      }\n    }\n\n    if (render != null) {\n      if (render.defaultProps != null || render.propTypes != null) {\n        error('forwardRef render functions do not support propTypes or defaultProps. ' + 'Did you accidentally pass a React component?');\n      }\n    }\n  }\n\n  var elementType = {\n    $$typeof: REACT_FORWARD_REF_TYPE,\n    render: render\n  };\n\n  {\n    var ownName;\n    Object.defineProperty(elementType, 'displayName', {\n      enumerable: false,\n      configurable: true,\n      get: function () {\n        return ownName;\n      },\n      set: function (name) {\n        ownName = name; // The inner component shouldn't inherit this display name in most cases,\n        // because the component may be used elsewhere.\n        // But it's nice for anonymous functions to inherit the name,\n        // so that our component-stack generation logic will display their frames.\n        // An anonymous function generally suggests a pattern like:\n        //   React.forwardRef((props, ref) => {...});\n        // This kind of inner function is not used elsewhere so the side effect is okay.\n\n        if (!render.name && !render.displayName) {\n          render.displayName = name;\n        }\n      }\n    });\n  }\n\n  return elementType;\n}\n\nvar REACT_MODULE_REFERENCE;\n\n{\n  REACT_MODULE_REFERENCE = Symbol.for('react.module.reference');\n}\n\nfunction isValidElementType(type) {\n  if (typeof type === 'string' || typeof type === 'function') {\n    return true;\n  } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n\n  if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || enableDebugTracing  || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || enableLegacyHidden  || type === REACT_OFFSCREEN_TYPE || enableScopeAPI  || enableCacheElement  || enableTransitionTracing ) {\n    return true;\n  }\n\n  if (typeof type === 'object' && type !== null) {\n    if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || // This needs to include all possible module reference object\n    // types supported by any Flight configuration anywhere since\n    // we don't know which Flight build this will end up being used\n    // with.\n    type.$$typeof === REACT_MODULE_REFERENCE || type.getModuleId !== undefined) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction memo(type, compare) {\n  {\n    if (!isValidElementType(type)) {\n      error('memo: The first argument must be a component. Instead ' + 'received: %s', type === null ? 'null' : typeof type);\n    }\n  }\n\n  var elementType = {\n    $$typeof: REACT_MEMO_TYPE,\n    type: type,\n    compare: compare === undefined ? null : compare\n  };\n\n  {\n    var ownName;\n    Object.defineProperty(elementType, 'displayName', {\n      enumerable: false,\n      configurable: true,\n      get: function () {\n        return ownName;\n      },\n      set: function (name) {\n        ownName = name; // The inner component shouldn't inherit this display name in most cases,\n        // because the component may be used elsewhere.\n        // But it's nice for anonymous functions to inherit the name,\n        // so that our component-stack generation logic will display their frames.\n        // An anonymous function generally suggests a pattern like:\n        //   React.memo((props) => {...});\n        // This kind of inner function is not used elsewhere so the side effect is okay.\n\n        if (!type.name && !type.displayName) {\n          type.displayName = name;\n        }\n      }\n    });\n  }\n\n  return elementType;\n}\n\nfunction resolveDispatcher() {\n  var dispatcher = ReactCurrentDispatcher.current;\n\n  {\n    if (dispatcher === null) {\n      error('Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for' + ' one of the following reasons:\\n' + '1. You might have mismatching versions of React and the renderer (such as React DOM)\\n' + '2. You might be breaking the Rules of Hooks\\n' + '3. You might have more than one copy of React in the same app\\n' + 'See https://reactjs.org/link/invalid-hook-call for tips about how to debug and fix this problem.');\n    }\n  } // Will result in a null access error if accessed outside render phase. We\n  // intentionally don't throw our own error because this is in a hot path.\n  // Also helps ensure this is inlined.\n\n\n  return dispatcher;\n}\nfunction useContext(Context) {\n  var dispatcher = resolveDispatcher();\n\n  {\n    // TODO: add a more generic warning for invalid values.\n    if (Context._context !== undefined) {\n      var realContext = Context._context; // Don't deduplicate because this legitimately causes bugs\n      // and nobody should be using this in existing code.\n\n      if (realContext.Consumer === Context) {\n        error('Calling useContext(Context.Consumer) is not supported, may cause bugs, and will be ' + 'removed in a future major release. Did you mean to call useContext(Context) instead?');\n      } else if (realContext.Provider === Context) {\n        error('Calling useContext(Context.Provider) is not supported. ' + 'Did you mean to call useContext(Context) instead?');\n      }\n    }\n  }\n\n  return dispatcher.useContext(Context);\n}\nfunction useState(initialState) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useState(initialState);\n}\nfunction useReducer(reducer, initialArg, init) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useReducer(reducer, initialArg, init);\n}\nfunction useRef(initialValue) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useRef(initialValue);\n}\nfunction useEffect(create, deps) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useEffect(create, deps);\n}\nfunction useInsertionEffect(create, deps) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useInsertionEffect(create, deps);\n}\nfunction useLayoutEffect(create, deps) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useLayoutEffect(create, deps);\n}\nfunction useCallback(callback, deps) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useCallback(callback, deps);\n}\nfunction useMemo(create, deps) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useMemo(create, deps);\n}\nfunction useImperativeHandle(ref, create, deps) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useImperativeHandle(ref, create, deps);\n}\nfunction useDebugValue(value, formatterFn) {\n  {\n    var dispatcher = resolveDispatcher();\n    return dispatcher.useDebugValue(value, formatterFn);\n  }\n}\nfunction useTransition() {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useTransition();\n}\nfunction useDeferredValue(value) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useDeferredValue(value);\n}\nfunction useId() {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useId();\n}\nfunction useSyncExternalStore(subscribe, getSnapshot, getServerSnapshot) {\n  var dispatcher = resolveDispatcher();\n  return dispatcher.useSyncExternalStore(subscribe, getSnapshot, getServerSnapshot);\n}\n\n// Helpers to patch console.logs to avoid logging during side-effect free\n// replaying on render function. This currently only patches the object\n// lazily which won't cover if the log function was extracted eagerly.\n// We could also eagerly patch the method.\nvar disabledDepth = 0;\nvar prevLog;\nvar prevInfo;\nvar prevWarn;\nvar prevError;\nvar prevGroup;\nvar prevGroupCollapsed;\nvar prevGroupEnd;\n\nfunction disabledLog() {}\n\ndisabledLog.__reactDisabledLog = true;\nfunction disableLogs() {\n  {\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      prevLog = console.log;\n      prevInfo = console.info;\n      prevWarn = console.warn;\n      prevError = console.error;\n      prevGroup = console.group;\n      prevGroupCollapsed = console.groupCollapsed;\n      prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n\n      var props = {\n        configurable: true,\n        enumerable: true,\n        value: disabledLog,\n        writable: true\n      }; // $FlowFixMe Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        info: props,\n        log: props,\n        warn: props,\n        error: props,\n        group: props,\n        groupCollapsed: props,\n        groupEnd: props\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    disabledDepth++;\n  }\n}\nfunction reenableLogs() {\n  {\n    disabledDepth--;\n\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      var props = {\n        configurable: true,\n        enumerable: true,\n        writable: true\n      }; // $FlowFixMe Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        log: assign({}, props, {\n          value: prevLog\n        }),\n        info: assign({}, props, {\n          value: prevInfo\n        }),\n        warn: assign({}, props, {\n          value: prevWarn\n        }),\n        error: assign({}, props, {\n          value: prevError\n        }),\n        group: assign({}, props, {\n          value: prevGroup\n        }),\n        groupCollapsed: assign({}, props, {\n          value: prevGroupCollapsed\n        }),\n        groupEnd: assign({}, props, {\n          value: prevGroupEnd\n        })\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    if (disabledDepth < 0) {\n      error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\n    }\n  }\n}\n\nvar ReactCurrentDispatcher$1 = ReactSharedInternals.ReactCurrentDispatcher;\nvar prefix;\nfunction describeBuiltInComponentFrame(name, source, ownerFn) {\n  {\n    if (prefix === undefined) {\n      // Extract the VM specific prefix used by each line.\n      try {\n        throw Error();\n      } catch (x) {\n        var match = x.stack.trim().match(/\\n( *(at )?)/);\n        prefix = match && match[1] || '';\n      }\n    } // We use the prefix to ensure our stacks line up with native stack frames.\n\n\n    return '\\n' + prefix + name;\n  }\n}\nvar reentry = false;\nvar componentFrameCache;\n\n{\n  var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\n  componentFrameCache = new PossiblyWeakMap();\n}\n\nfunction describeNativeComponentFrame(fn, construct) {\n  // If something asked for a stack inside a fake render, it should get ignored.\n  if ( !fn || reentry) {\n    return '';\n  }\n\n  {\n    var frame = componentFrameCache.get(fn);\n\n    if (frame !== undefined) {\n      return frame;\n    }\n  }\n\n  var control;\n  reentry = true;\n  var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe It does accept undefined.\n\n  Error.prepareStackTrace = undefined;\n  var previousDispatcher;\n\n  {\n    previousDispatcher = ReactCurrentDispatcher$1.current; // Set the dispatcher in DEV because this might be call in the render function\n    // for warnings.\n\n    ReactCurrentDispatcher$1.current = null;\n    disableLogs();\n  }\n\n  try {\n    // This should throw.\n    if (construct) {\n      // Something should be setting the props in the constructor.\n      var Fake = function () {\n        throw Error();\n      }; // $FlowFixMe\n\n\n      Object.defineProperty(Fake.prototype, 'props', {\n        set: function () {\n          // We use a throwing setter instead of frozen or non-writable props\n          // because that won't throw in a non-strict mode function.\n          throw Error();\n        }\n      });\n\n      if (typeof Reflect === 'object' && Reflect.construct) {\n        // We construct a different control for this case to include any extra\n        // frames added by the construct call.\n        try {\n          Reflect.construct(Fake, []);\n        } catch (x) {\n          control = x;\n        }\n\n        Reflect.construct(fn, [], Fake);\n      } else {\n        try {\n          Fake.call();\n        } catch (x) {\n          control = x;\n        }\n\n        fn.call(Fake.prototype);\n      }\n    } else {\n      try {\n        throw Error();\n      } catch (x) {\n        control = x;\n      }\n\n      fn();\n    }\n  } catch (sample) {\n    // This is inlined manually because closure doesn't do it for us.\n    if (sample && control && typeof sample.stack === 'string') {\n      // This extracts the first frame from the sample that isn't also in the control.\n      // Skipping one frame that we assume is the frame that calls the two.\n      var sampleLines = sample.stack.split('\\n');\n      var controlLines = control.stack.split('\\n');\n      var s = sampleLines.length - 1;\n      var c = controlLines.length - 1;\n\n      while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\n        // We expect at least one stack frame to be shared.\n        // Typically this will be the root most one. However, stack frames may be\n        // cut off due to maximum stack limits. In this case, one maybe cut off\n        // earlier than the other. We assume that the sample is longer or the same\n        // and there for cut off earlier. So we should find the root most frame in\n        // the sample somewhere in the control.\n        c--;\n      }\n\n      for (; s >= 1 && c >= 0; s--, c--) {\n        // Next we find the first one that isn't the same which should be the\n        // frame that called our sample function and the control.\n        if (sampleLines[s] !== controlLines[c]) {\n          // In V8, the first line is describing the message but other VMs don't.\n          // If we're about to return the first line, and the control is also on the same\n          // line, that's a pretty good indicator that our sample threw at same line as\n          // the control. I.e. before we entered the sample frame. So we ignore this result.\n          // This can happen if you passed a class to function component, or non-function.\n          if (s !== 1 || c !== 1) {\n            do {\n              s--;\n              c--; // We may still have similar intermediate frames from the construct call.\n              // The next one that isn't the same should be our match though.\n\n              if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at '); // If our component frame is labeled \"<anonymous>\"\n                // but we have a user-provided \"displayName\"\n                // splice it in to make the stack more readable.\n\n\n                if (fn.displayName && _frame.includes('<anonymous>')) {\n                  _frame = _frame.replace('<anonymous>', fn.displayName);\n                }\n\n                {\n                  if (typeof fn === 'function') {\n                    componentFrameCache.set(fn, _frame);\n                  }\n                } // Return the line we found.\n\n\n                return _frame;\n              }\n            } while (s >= 1 && c >= 0);\n          }\n\n          break;\n        }\n      }\n    }\n  } finally {\n    reentry = false;\n\n    {\n      ReactCurrentDispatcher$1.current = previousDispatcher;\n      reenableLogs();\n    }\n\n    Error.prepareStackTrace = previousPrepareStackTrace;\n  } // Fallback to just using the name if we couldn't make it throw.\n\n\n  var name = fn ? fn.displayName || fn.name : '';\n  var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\n\n  {\n    if (typeof fn === 'function') {\n      componentFrameCache.set(fn, syntheticFrame);\n    }\n  }\n\n  return syntheticFrame;\n}\nfunction describeFunctionComponentFrame(fn, source, ownerFn) {\n  {\n    return describeNativeComponentFrame(fn, false);\n  }\n}\n\nfunction shouldConstruct(Component) {\n  var prototype = Component.prototype;\n  return !!(prototype && prototype.isReactComponent);\n}\n\nfunction describeUnknownElementTypeFrameInDEV(type, source, ownerFn) {\n\n  if (type == null) {\n    return '';\n  }\n\n  if (typeof type === 'function') {\n    {\n      return describeNativeComponentFrame(type, shouldConstruct(type));\n    }\n  }\n\n  if (typeof type === 'string') {\n    return describeBuiltInComponentFrame(type);\n  }\n\n  switch (type) {\n    case REACT_SUSPENSE_TYPE:\n      return describeBuiltInComponentFrame('Suspense');\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return describeBuiltInComponentFrame('SuspenseList');\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_FORWARD_REF_TYPE:\n        return describeFunctionComponentFrame(type.render);\n\n      case REACT_MEMO_TYPE:\n        // Memo may contain any component type so we recursively resolve it.\n        return describeUnknownElementTypeFrameInDEV(type.type, source, ownerFn);\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            // Lazy may contain any component type so we recursively resolve it.\n            return describeUnknownElementTypeFrameInDEV(init(payload), source, ownerFn);\n          } catch (x) {}\n        }\n    }\n  }\n\n  return '';\n}\n\nvar loggedTypeFailures = {};\nvar ReactDebugCurrentFrame$1 = ReactSharedInternals.ReactDebugCurrentFrame;\n\nfunction setCurrentlyValidatingElement(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      ReactDebugCurrentFrame$1.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame$1.setExtraStackFrame(null);\n    }\n  }\n}\n\nfunction checkPropTypes(typeSpecs, values, location, componentName, element) {\n  {\n    // $FlowFixMe This is okay but Flow doesn't know it.\n    var has = Function.call.bind(hasOwnProperty);\n\n    for (var typeSpecName in typeSpecs) {\n      if (has(typeSpecs, typeSpecName)) {\n        var error$1 = void 0; // Prop type validation may throw. In case they do, we don't want to\n        // fail the render phase where it didn't fail before. So we log it.\n        // After these have been cleaned up, we'll let them throw.\n\n        try {\n          // This is intentionally an invariant that gets caught. It's the same\n          // behavior as without this statement except with a better message.\n          if (typeof typeSpecs[typeSpecName] !== 'function') {\n            // eslint-disable-next-line react-internal/prod-error-codes\n            var err = Error((componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' + 'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' + 'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.');\n            err.name = 'Invariant Violation';\n            throw err;\n          }\n\n          error$1 = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED');\n        } catch (ex) {\n          error$1 = ex;\n        }\n\n        if (error$1 && !(error$1 instanceof Error)) {\n          setCurrentlyValidatingElement(element);\n\n          error('%s: type specification of %s' + ' `%s` is invalid; the type checker ' + 'function must return `null` or an `Error` but returned a %s. ' + 'You may have forgotten to pass an argument to the type checker ' + 'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' + 'shape all require an argument).', componentName || 'React class', location, typeSpecName, typeof error$1);\n\n          setCurrentlyValidatingElement(null);\n        }\n\n        if (error$1 instanceof Error && !(error$1.message in loggedTypeFailures)) {\n          // Only monitor this failure once because there tends to be a lot of the\n          // same error.\n          loggedTypeFailures[error$1.message] = true;\n          setCurrentlyValidatingElement(element);\n\n          error('Failed %s type: %s', location, error$1.message);\n\n          setCurrentlyValidatingElement(null);\n        }\n      }\n    }\n  }\n}\n\nfunction setCurrentlyValidatingElement$1(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      setExtraStackFrame(stack);\n    } else {\n      setExtraStackFrame(null);\n    }\n  }\n}\n\nvar propTypesMisspellWarningShown;\n\n{\n  propTypesMisspellWarningShown = false;\n}\n\nfunction getDeclarationErrorAddendum() {\n  if (ReactCurrentOwner.current) {\n    var name = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n    if (name) {\n      return '\\n\\nCheck the render method of `' + name + '`.';\n    }\n  }\n\n  return '';\n}\n\nfunction getSourceInfoErrorAddendum(source) {\n  if (source !== undefined) {\n    var fileName = source.fileName.replace(/^.*[\\\\\\/]/, '');\n    var lineNumber = source.lineNumber;\n    return '\\n\\nCheck your code at ' + fileName + ':' + lineNumber + '.';\n  }\n\n  return '';\n}\n\nfunction getSourceInfoErrorAddendumForProps(elementProps) {\n  if (elementProps !== null && elementProps !== undefined) {\n    return getSourceInfoErrorAddendum(elementProps.__source);\n  }\n\n  return '';\n}\n/**\n * Warn if there's no key explicitly set on dynamic arrays of children or\n * object keys are not valid. This allows us to keep track of children between\n * updates.\n */\n\n\nvar ownerHasKeyUseWarning = {};\n\nfunction getCurrentComponentErrorInfo(parentType) {\n  var info = getDeclarationErrorAddendum();\n\n  if (!info) {\n    var parentName = typeof parentType === 'string' ? parentType : parentType.displayName || parentType.name;\n\n    if (parentName) {\n      info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n    }\n  }\n\n  return info;\n}\n/**\n * Warn if the element doesn't have an explicit key assigned to it.\n * This element is in an array. The array could grow and shrink or be\n * reordered. All children that haven't already been validated are required to\n * have a \"key\" property assigned to it. Error statuses are cached so a warning\n * will only be shown once.\n *\n * @internal\n * @param {ReactElement} element Element that requires a key.\n * @param {*} parentType element's parent's type.\n */\n\n\nfunction validateExplicitKey(element, parentType) {\n  if (!element._store || element._store.validated || element.key != null) {\n    return;\n  }\n\n  element._store.validated = true;\n  var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n\n  if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n    return;\n  }\n\n  ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n  // property, it may be the creator of the child that's responsible for\n  // assigning it a key.\n\n  var childOwner = '';\n\n  if (element && element._owner && element._owner !== ReactCurrentOwner.current) {\n    // Give the component that originally created this child.\n    childOwner = \" It was passed a child from \" + getComponentNameFromType(element._owner.type) + \".\";\n  }\n\n  {\n    setCurrentlyValidatingElement$1(element);\n\n    error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\n\n    setCurrentlyValidatingElement$1(null);\n  }\n}\n/**\n * Ensure that every element either is passed in a static location, in an\n * array with an explicit keys property defined, or in an object literal\n * with valid key property.\n *\n * @internal\n * @param {ReactNode} node Statically passed child of any type.\n * @param {*} parentType node's parent's type.\n */\n\n\nfunction validateChildKeys(node, parentType) {\n  if (typeof node !== 'object') {\n    return;\n  }\n\n  if (isArray(node)) {\n    for (var i = 0; i < node.length; i++) {\n      var child = node[i];\n\n      if (isValidElement(child)) {\n        validateExplicitKey(child, parentType);\n      }\n    }\n  } else if (isValidElement(node)) {\n    // This element was passed in a valid location.\n    if (node._store) {\n      node._store.validated = true;\n    }\n  } else if (node) {\n    var iteratorFn = getIteratorFn(node);\n\n    if (typeof iteratorFn === 'function') {\n      // Entry iterators used to provide implicit keys,\n      // but now we print a separate warning for them later.\n      if (iteratorFn !== node.entries) {\n        var iterator = iteratorFn.call(node);\n        var step;\n\n        while (!(step = iterator.next()).done) {\n          if (isValidElement(step.value)) {\n            validateExplicitKey(step.value, parentType);\n          }\n        }\n      }\n    }\n  }\n}\n/**\n * Given an element, validate that its props follow the propTypes definition,\n * provided by the type.\n *\n * @param {ReactElement} element\n */\n\n\nfunction validatePropTypes(element) {\n  {\n    var type = element.type;\n\n    if (type === null || type === undefined || typeof type === 'string') {\n      return;\n    }\n\n    var propTypes;\n\n    if (typeof type === 'function') {\n      propTypes = type.propTypes;\n    } else if (typeof type === 'object' && (type.$$typeof === REACT_FORWARD_REF_TYPE || // Note: Memo only checks outer props here.\n    // Inner props are checked in the reconciler.\n    type.$$typeof === REACT_MEMO_TYPE)) {\n      propTypes = type.propTypes;\n    } else {\n      return;\n    }\n\n    if (propTypes) {\n      // Intentionally inside to avoid triggering lazy initializers:\n      var name = getComponentNameFromType(type);\n      checkPropTypes(propTypes, element.props, 'prop', name, element);\n    } else if (type.PropTypes !== undefined && !propTypesMisspellWarningShown) {\n      propTypesMisspellWarningShown = true; // Intentionally inside to avoid triggering lazy initializers:\n\n      var _name = getComponentNameFromType(type);\n\n      error('Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?', _name || 'Unknown');\n    }\n\n    if (typeof type.getDefaultProps === 'function' && !type.getDefaultProps.isReactClassApproved) {\n      error('getDefaultProps is only used on classic React.createClass ' + 'definitions. Use a static property named `defaultProps` instead.');\n    }\n  }\n}\n/**\n * Given a fragment, validate that it can only be provided with fragment props\n * @param {ReactElement} fragment\n */\n\n\nfunction validateFragmentProps(fragment) {\n  {\n    var keys = Object.keys(fragment.props);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n\n      if (key !== 'children' && key !== 'key') {\n        setCurrentlyValidatingElement$1(fragment);\n\n        error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\n\n        setCurrentlyValidatingElement$1(null);\n        break;\n      }\n    }\n\n    if (fragment.ref !== null) {\n      setCurrentlyValidatingElement$1(fragment);\n\n      error('Invalid attribute `ref` supplied to `React.Fragment`.');\n\n      setCurrentlyValidatingElement$1(null);\n    }\n  }\n}\nfunction createElementWithValidation(type, props, children) {\n  var validType = isValidElementType(type); // We warn in this case but don't throw. We expect the element creation to\n  // succeed and there will likely be errors in render.\n\n  if (!validType) {\n    var info = '';\n\n    if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\n      info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\n    }\n\n    var sourceInfo = getSourceInfoErrorAddendumForProps(props);\n\n    if (sourceInfo) {\n      info += sourceInfo;\n    } else {\n      info += getDeclarationErrorAddendum();\n    }\n\n    var typeString;\n\n    if (type === null) {\n      typeString = 'null';\n    } else if (isArray(type)) {\n      typeString = 'array';\n    } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n      typeString = \"<\" + (getComponentNameFromType(type.type) || 'Unknown') + \" />\";\n      info = ' Did you accidentally export a JSX literal instead of a component?';\n    } else {\n      typeString = typeof type;\n    }\n\n    {\n      error('React.createElement: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\n    }\n  }\n\n  var element = createElement.apply(this, arguments); // The result can be nullish if a mock or a custom function is used.\n  // TODO: Drop this when these are no longer allowed as the type argument.\n\n  if (element == null) {\n    return element;\n  } // Skip key warning if the type isn't valid since our key validation logic\n  // doesn't expect a non-string/function type and can throw confusing errors.\n  // We don't want exception behavior to differ between dev and prod.\n  // (Rendering will throw with a helpful message and as soon as the type is\n  // fixed, the key warnings will appear.)\n\n\n  if (validType) {\n    for (var i = 2; i < arguments.length; i++) {\n      validateChildKeys(arguments[i], type);\n    }\n  }\n\n  if (type === REACT_FRAGMENT_TYPE) {\n    validateFragmentProps(element);\n  } else {\n    validatePropTypes(element);\n  }\n\n  return element;\n}\nvar didWarnAboutDeprecatedCreateFactory = false;\nfunction createFactoryWithValidation(type) {\n  var validatedFactory = createElementWithValidation.bind(null, type);\n  validatedFactory.type = type;\n\n  {\n    if (!didWarnAboutDeprecatedCreateFactory) {\n      didWarnAboutDeprecatedCreateFactory = true;\n\n      warn('React.createFactory() is deprecated and will be removed in ' + 'a future major release. Consider using JSX ' + 'or use React.createElement() directly instead.');\n    } // Legacy hook: remove it\n\n\n    Object.defineProperty(validatedFactory, 'type', {\n      enumerable: false,\n      get: function () {\n        warn('Factory.type is deprecated. Access the class directly ' + 'before passing it to createFactory.');\n\n        Object.defineProperty(this, 'type', {\n          value: type\n        });\n        return type;\n      }\n    });\n  }\n\n  return validatedFactory;\n}\nfunction cloneElementWithValidation(element, props, children) {\n  var newElement = cloneElement.apply(this, arguments);\n\n  for (var i = 2; i < arguments.length; i++) {\n    validateChildKeys(arguments[i], newElement.type);\n  }\n\n  validatePropTypes(newElement);\n  return newElement;\n}\n\nfunction startTransition(scope, options) {\n  var prevTransition = ReactCurrentBatchConfig.transition;\n  ReactCurrentBatchConfig.transition = {};\n  var currentTransition = ReactCurrentBatchConfig.transition;\n\n  {\n    ReactCurrentBatchConfig.transition._updatedFibers = new Set();\n  }\n\n  try {\n    scope();\n  } finally {\n    ReactCurrentBatchConfig.transition = prevTransition;\n\n    {\n      if (prevTransition === null && currentTransition._updatedFibers) {\n        var updatedFibersCount = currentTransition._updatedFibers.size;\n\n        if (updatedFibersCount > 10) {\n          warn('Detected a large number of updates inside startTransition. ' + 'If this is due to a subscription please re-write it to use React provided hooks. ' + 'Otherwise concurrent mode guarantees are off the table.');\n        }\n\n        currentTransition._updatedFibers.clear();\n      }\n    }\n  }\n}\n\nvar didWarnAboutMessageChannel = false;\nvar enqueueTaskImpl = null;\nfunction enqueueTask(task) {\n  if (enqueueTaskImpl === null) {\n    try {\n      // read require off the module object to get around the bundlers.\n      // we don't want them to detect a require and bundle a Node polyfill.\n      var requireString = ('require' + Math.random()).slice(0, 7);\n      var nodeRequire = module && module[requireString]; // assuming we're in node, let's try to get node's\n      // version of setImmediate, bypassing fake timers if any.\n\n      enqueueTaskImpl = nodeRequire.call(module, 'timers').setImmediate;\n    } catch (_err) {\n      // we're in a browser\n      // we can't use regular timers because they may still be faked\n      // so we try MessageChannel+postMessage instead\n      enqueueTaskImpl = function (callback) {\n        {\n          if (didWarnAboutMessageChannel === false) {\n            didWarnAboutMessageChannel = true;\n\n            if (typeof MessageChannel === 'undefined') {\n              error('This browser does not have a MessageChannel implementation, ' + 'so enqueuing tasks via await act(async () => ...) will fail. ' + 'Please file an issue at https://github.com/facebook/react/issues ' + 'if you encounter this warning.');\n            }\n          }\n        }\n\n        var channel = new MessageChannel();\n        channel.port1.onmessage = callback;\n        channel.port2.postMessage(undefined);\n      };\n    }\n  }\n\n  return enqueueTaskImpl(task);\n}\n\nvar actScopeDepth = 0;\nvar didWarnNoAwaitAct = false;\nfunction act(callback) {\n  {\n    // `act` calls can be nested, so we track the depth. This represents the\n    // number of `act` scopes on the stack.\n    var prevActScopeDepth = actScopeDepth;\n    actScopeDepth++;\n\n    if (ReactCurrentActQueue.current === null) {\n      // This is the outermost `act` scope. Initialize the queue. The reconciler\n      // will detect the queue and use it instead of Scheduler.\n      ReactCurrentActQueue.current = [];\n    }\n\n    var prevIsBatchingLegacy = ReactCurrentActQueue.isBatchingLegacy;\n    var result;\n\n    try {\n      // Used to reproduce behavior of `batchedUpdates` in legacy mode. Only\n      // set to `true` while the given callback is executed, not for updates\n      // triggered during an async event, because this is how the legacy\n      // implementation of `act` behaved.\n      ReactCurrentActQueue.isBatchingLegacy = true;\n      result = callback(); // Replicate behavior of original `act` implementation in legacy mode,\n      // which flushed updates immediately after the scope function exits, even\n      // if it's an async function.\n\n      if (!prevIsBatchingLegacy && ReactCurrentActQueue.didScheduleLegacyUpdate) {\n        var queue = ReactCurrentActQueue.current;\n\n        if (queue !== null) {\n          ReactCurrentActQueue.didScheduleLegacyUpdate = false;\n          flushActQueue(queue);\n        }\n      }\n    } catch (error) {\n      popActScope(prevActScopeDepth);\n      throw error;\n    } finally {\n      ReactCurrentActQueue.isBatchingLegacy = prevIsBatchingLegacy;\n    }\n\n    if (result !== null && typeof result === 'object' && typeof result.then === 'function') {\n      var thenableResult = result; // The callback is an async function (i.e. returned a promise). Wait\n      // for it to resolve before exiting the current scope.\n\n      var wasAwaited = false;\n      var thenable = {\n        then: function (resolve, reject) {\n          wasAwaited = true;\n          thenableResult.then(function (returnValue) {\n            popActScope(prevActScopeDepth);\n\n            if (actScopeDepth === 0) {\n              // We've exited the outermost act scope. Recursively flush the\n              // queue until there's no remaining work.\n              recursivelyFlushAsyncActWork(returnValue, resolve, reject);\n            } else {\n              resolve(returnValue);\n            }\n          }, function (error) {\n            // The callback threw an error.\n            popActScope(prevActScopeDepth);\n            reject(error);\n          });\n        }\n      };\n\n      {\n        if (!didWarnNoAwaitAct && typeof Promise !== 'undefined') {\n          // eslint-disable-next-line no-undef\n          Promise.resolve().then(function () {}).then(function () {\n            if (!wasAwaited) {\n              didWarnNoAwaitAct = true;\n\n              error('You called act(async () => ...) without await. ' + 'This could lead to unexpected testing behaviour, ' + 'interleaving multiple act calls and mixing their ' + 'scopes. ' + 'You should - await act(async () => ...);');\n            }\n          });\n        }\n      }\n\n      return thenable;\n    } else {\n      var returnValue = result; // The callback is not an async function. Exit the current scope\n      // immediately, without awaiting.\n\n      popActScope(prevActScopeDepth);\n\n      if (actScopeDepth === 0) {\n        // Exiting the outermost act scope. Flush the queue.\n        var _queue = ReactCurrentActQueue.current;\n\n        if (_queue !== null) {\n          flushActQueue(_queue);\n          ReactCurrentActQueue.current = null;\n        } // Return a thenable. If the user awaits it, we'll flush again in\n        // case additional work was scheduled by a microtask.\n\n\n        var _thenable = {\n          then: function (resolve, reject) {\n            // Confirm we haven't re-entered another `act` scope, in case\n            // the user does something weird like await the thenable\n            // multiple times.\n            if (ReactCurrentActQueue.current === null) {\n              // Recursively flush the queue until there's no remaining work.\n              ReactCurrentActQueue.current = [];\n              recursivelyFlushAsyncActWork(returnValue, resolve, reject);\n            } else {\n              resolve(returnValue);\n            }\n          }\n        };\n        return _thenable;\n      } else {\n        // Since we're inside a nested `act` scope, the returned thenable\n        // immediately resolves. The outer scope will flush the queue.\n        var _thenable2 = {\n          then: function (resolve, reject) {\n            resolve(returnValue);\n          }\n        };\n        return _thenable2;\n      }\n    }\n  }\n}\n\nfunction popActScope(prevActScopeDepth) {\n  {\n    if (prevActScopeDepth !== actScopeDepth - 1) {\n      error('You seem to have overlapping act() calls, this is not supported. ' + 'Be sure to await previous act() calls before making a new one. ');\n    }\n\n    actScopeDepth = prevActScopeDepth;\n  }\n}\n\nfunction recursivelyFlushAsyncActWork(returnValue, resolve, reject) {\n  {\n    var queue = ReactCurrentActQueue.current;\n\n    if (queue !== null) {\n      try {\n        flushActQueue(queue);\n        enqueueTask(function () {\n          if (queue.length === 0) {\n            // No additional work was scheduled. Finish.\n            ReactCurrentActQueue.current = null;\n            resolve(returnValue);\n          } else {\n            // Keep flushing work until there's none left.\n            recursivelyFlushAsyncActWork(returnValue, resolve, reject);\n          }\n        });\n      } catch (error) {\n        reject(error);\n      }\n    } else {\n      resolve(returnValue);\n    }\n  }\n}\n\nvar isFlushing = false;\n\nfunction flushActQueue(queue) {\n  {\n    if (!isFlushing) {\n      // Prevent re-entrance.\n      isFlushing = true;\n      var i = 0;\n\n      try {\n        for (; i < queue.length; i++) {\n          var callback = queue[i];\n\n          do {\n            callback = callback(true);\n          } while (callback !== null);\n        }\n\n        queue.length = 0;\n      } catch (error) {\n        // If something throws, leave the remaining callbacks on the queue.\n        queue = queue.slice(i + 1);\n        throw error;\n      } finally {\n        isFlushing = false;\n      }\n    }\n  }\n}\n\nvar createElement$1 =  createElementWithValidation ;\nvar cloneElement$1 =  cloneElementWithValidation ;\nvar createFactory =  createFactoryWithValidation ;\nvar Children = {\n  map: mapChildren,\n  forEach: forEachChildren,\n  count: countChildren,\n  toArray: toArray,\n  only: onlyChild\n};\n\nexports.Children = Children;\nexports.Component = Component;\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.Profiler = REACT_PROFILER_TYPE;\nexports.PureComponent = PureComponent;\nexports.StrictMode = REACT_STRICT_MODE_TYPE;\nexports.Suspense = REACT_SUSPENSE_TYPE;\nexports.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = ReactSharedInternals;\nexports.act = act;\nexports.cloneElement = cloneElement$1;\nexports.createContext = createContext;\nexports.createElement = createElement$1;\nexports.createFactory = createFactory;\nexports.createRef = createRef;\nexports.forwardRef = forwardRef;\nexports.isValidElement = isValidElement;\nexports.lazy = lazy;\nexports.memo = memo;\nexports.startTransition = startTransition;\nexports.unstable_act = act;\nexports.useCallback = useCallback;\nexports.useContext = useContext;\nexports.useDebugValue = useDebugValue;\nexports.useDeferredValue = useDeferredValue;\nexports.useEffect = useEffect;\nexports.useId = useId;\nexports.useImperativeHandle = useImperativeHandle;\nexports.useInsertionEffect = useInsertionEffect;\nexports.useLayoutEffect = useLayoutEffect;\nexports.useMemo = useMemo;\nexports.useReducer = useReducer;\nexports.useRef = useRef;\nexports.useState = useState;\nexports.useSyncExternalStore = useSyncExternalStore;\nexports.useTransition = useTransition;\nexports.version = ReactVersion;\n          /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error());\n}\n        \n  })();\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,IAAIA,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC,CAAC,YAAW;IAEJ,YAAY;;IAEtB;IACA,IACE,OAAOC,8BAA8B,KAAK,WAAW,IACrD,OAAOA,8BAA8B,CAACC,2BAA2B,KAC/D,UAAU,EACZ;MACAD,8BAA8B,CAACC,2BAA2B,CAAC,IAAIC,KAAK,CAAC,CAAC,CAAC;IACzE;IACU,IAAIC,YAAY,GAAG,QAAQ;;IAErC;IACA;IACA;IACA;IACA,IAAIC,kBAAkB,GAAGC,MAAM,CAACC,GAAG,CAAC,eAAe,CAAC;IACpD,IAAIC,iBAAiB,GAAGF,MAAM,CAACC,GAAG,CAAC,cAAc,CAAC;IAClD,IAAIE,mBAAmB,GAAGH,MAAM,CAACC,GAAG,CAAC,gBAAgB,CAAC;IACtD,IAAIG,sBAAsB,GAAGJ,MAAM,CAACC,GAAG,CAAC,mBAAmB,CAAC;IAC5D,IAAII,mBAAmB,GAAGL,MAAM,CAACC,GAAG,CAAC,gBAAgB,CAAC;IACtD,IAAIK,mBAAmB,GAAGN,MAAM,CAACC,GAAG,CAAC,gBAAgB,CAAC;IACtD,IAAIM,kBAAkB,GAAGP,MAAM,CAACC,GAAG,CAAC,eAAe,CAAC;IACpD,IAAIO,sBAAsB,GAAGR,MAAM,CAACC,GAAG,CAAC,mBAAmB,CAAC;IAC5D,IAAIQ,mBAAmB,GAAGT,MAAM,CAACC,GAAG,CAAC,gBAAgB,CAAC;IACtD,IAAIS,wBAAwB,GAAGV,MAAM,CAACC,GAAG,CAAC,qBAAqB,CAAC;IAChE,IAAIU,eAAe,GAAGX,MAAM,CAACC,GAAG,CAAC,YAAY,CAAC;IAC9C,IAAIW,eAAe,GAAGZ,MAAM,CAACC,GAAG,CAAC,YAAY,CAAC;IAC9C,IAAIY,oBAAoB,GAAGb,MAAM,CAACC,GAAG,CAAC,iBAAiB,CAAC;IACxD,IAAIa,qBAAqB,GAAGd,MAAM,CAACe,QAAQ;IAC3C,IAAIC,oBAAoB,GAAG,YAAY;IACvC,SAASC,aAAaA,CAACC,aAAa,EAAE;MACpC,IAAIA,aAAa,KAAK,IAAI,IAAI,OAAOA,aAAa,KAAK,QAAQ,EAAE;QAC/D,OAAO,IAAI;MACb;MAEA,IAAIC,aAAa,GAAGL,qBAAqB,IAAII,aAAa,CAACJ,qBAAqB,CAAC,IAAII,aAAa,CAACF,oBAAoB,CAAC;MAExH,IAAI,OAAOG,aAAa,KAAK,UAAU,EAAE;QACvC,OAAOA,aAAa;MACtB;MAEA,OAAO,IAAI;IACb;;IAEA;AACA;AACA;IACA,IAAIC,sBAAsB,GAAG;MAC3B;AACF;AACA;AACA;MACEC,OAAO,EAAE;IACX,CAAC;;IAED;AACA;AACA;AACA;IACA,IAAIC,uBAAuB,GAAG;MAC5BC,UAAU,EAAE;IACd,CAAC;IAED,IAAIC,oBAAoB,GAAG;MACzBH,OAAO,EAAE,IAAI;MACb;MACAI,gBAAgB,EAAE,KAAK;MACvBC,uBAAuB,EAAE;IAC3B,CAAC;;IAED;AACA;AACA;AACA;AACA;AACA;IACA,IAAIC,iBAAiB,GAAG;MACtB;AACF;AACA;AACA;MACEN,OAAO,EAAE;IACX,CAAC;IAED,IAAIO,sBAAsB,GAAG,CAAC,CAAC;IAC/B,IAAIC,sBAAsB,GAAG,IAAI;IACjC,SAASC,kBAAkBA,CAACC,KAAK,EAAE;MACjC;QACEF,sBAAsB,GAAGE,KAAK;MAChC;IACF;IAEA;MACEH,sBAAsB,CAACE,kBAAkB,GAAG,UAAUC,KAAK,EAAE;QAC3D;UACEF,sBAAsB,GAAGE,KAAK;QAChC;MACF,CAAC,CAAC,CAAC;;MAGHH,sBAAsB,CAACI,eAAe,GAAG,IAAI;MAE7CJ,sBAAsB,CAACK,gBAAgB,GAAG,YAAY;QACpD,IAAIF,KAAK,GAAG,EAAE,CAAC,CAAC;;QAEhB,IAAIF,sBAAsB,EAAE;UAC1BE,KAAK,IAAIF,sBAAsB;QACjC,CAAC,CAAC;;QAGF,IAAIK,IAAI,GAAGN,sBAAsB,CAACI,eAAe;QAEjD,IAAIE,IAAI,EAAE;UACRH,KAAK,IAAIG,IAAI,CAAC,CAAC,IAAI,EAAE;QACvB;QAEA,OAAOH,KAAK;MACd,CAAC;IACH;;IAEA;;IAEA,IAAII,cAAc,GAAG,KAAK,CAAC,CAAC;IAC5B,IAAIC,kBAAkB,GAAG,KAAK;IAC9B,IAAIC,uBAAuB,GAAG,KAAK,CAAC,CAAC;;IAErC,IAAIC,kBAAkB,GAAG,KAAK,CAAC,CAAC;IAChC;IACA;;IAEA,IAAIC,kBAAkB,GAAG,KAAK,CAAC,CAAC;;IAEhC,IAAIC,oBAAoB,GAAG;MACzBpB,sBAAsB,EAAEA,sBAAsB;MAC9CE,uBAAuB,EAAEA,uBAAuB;MAChDK,iBAAiB,EAAEA;IACrB,CAAC;IAED;MACEa,oBAAoB,CAACZ,sBAAsB,GAAGA,sBAAsB;MACpEY,oBAAoB,CAAChB,oBAAoB,GAAGA,oBAAoB;IAClE;;IAEA;IACA;IACA;IACA;;IAEA,SAASiB,IAAIA,CAACC,MAAM,EAAE;MACpB;QACE;UACE,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;YAC1GF,IAAI,CAACE,IAAI,GAAG,CAAC,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;UAClC;UAEAC,YAAY,CAAC,MAAM,EAAEP,MAAM,EAAEI,IAAI,CAAC;QACpC;MACF;IACF;IACA,SAASI,KAAKA,CAACR,MAAM,EAAE;MACrB;QACE;UACE,KAAK,IAAIS,KAAK,GAAGP,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACI,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;YACjHN,IAAI,CAACM,KAAK,GAAG,CAAC,CAAC,GAAGR,SAAS,CAACQ,KAAK,CAAC;UACpC;UAEAH,YAAY,CAAC,OAAO,EAAEP,MAAM,EAAEI,IAAI,CAAC;QACrC;MACF;IACF;IAEA,SAASG,YAAYA,CAACI,KAAK,EAAEX,MAAM,EAAEI,IAAI,EAAE;MACzC;MACA;MACA;QACE,IAAIlB,sBAAsB,GAAGY,oBAAoB,CAACZ,sBAAsB;QACxE,IAAIG,KAAK,GAAGH,sBAAsB,CAACK,gBAAgB,CAAC,CAAC;QAErD,IAAIF,KAAK,KAAK,EAAE,EAAE;UAChBW,MAAM,IAAI,IAAI;UACdI,IAAI,GAAGA,IAAI,CAACQ,MAAM,CAAC,CAACvB,KAAK,CAAC,CAAC;QAC7B,CAAC,CAAC;;QAGF,IAAIwB,cAAc,GAAGT,IAAI,CAACU,GAAG,CAAC,UAAUC,IAAI,EAAE;UAC5C,OAAOC,MAAM,CAACD,IAAI,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC;;QAEJF,cAAc,CAACI,OAAO,CAAC,WAAW,GAAGjB,MAAM,CAAC,CAAC,CAAC;QAC9C;QACA;;QAEAkB,QAAQ,CAACC,SAAS,CAACC,KAAK,CAACC,IAAI,CAACC,OAAO,CAACX,KAAK,CAAC,EAAEW,OAAO,EAAET,cAAc,CAAC;MACxE;IACF;IAEA,IAAIU,uCAAuC,GAAG,CAAC,CAAC;IAEhD,SAASC,QAAQA,CAACC,cAAc,EAAEC,UAAU,EAAE;MAC5C;QACE,IAAIC,YAAY,GAAGF,cAAc,CAACG,WAAW;QAC7C,IAAIC,aAAa,GAAGF,YAAY,KAAKA,YAAY,CAACG,WAAW,IAAIH,YAAY,CAACI,IAAI,CAAC,IAAI,YAAY;QACnG,IAAIC,UAAU,GAAGH,aAAa,GAAG,GAAG,GAAGH,UAAU;QAEjD,IAAIH,uCAAuC,CAACS,UAAU,CAAC,EAAE;UACvD;QACF;QAEAxB,KAAK,CAAC,wDAAwD,GAAG,oEAAoE,GAAG,qEAAqE,GAAG,4DAA4D,EAAEkB,UAAU,EAAEG,aAAa,CAAC;QAExSN,uCAAuC,CAACS,UAAU,CAAC,GAAG,IAAI;MAC5D;IACF;IACA;AACA;AACA;;IAGA,IAAIC,oBAAoB,GAAG;MACzB;AACF;AACA;AACA;AACA;AACA;AACA;MACEC,SAAS,EAAE,SAAAA,CAAUT,cAAc,EAAE;QACnC,OAAO,KAAK;MACd,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEU,kBAAkB,EAAE,SAAAA,CAAUV,cAAc,EAAEW,QAAQ,EAAEV,UAAU,EAAE;QAClEF,QAAQ,CAACC,cAAc,EAAE,aAAa,CAAC;MACzC,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEY,mBAAmB,EAAE,SAAAA,CAAUZ,cAAc,EAAEa,aAAa,EAAEF,QAAQ,EAAEV,UAAU,EAAE;QAClFF,QAAQ,CAACC,cAAc,EAAE,cAAc,CAAC;MAC1C,CAAC;MAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACEc,eAAe,EAAE,SAAAA,CAAUd,cAAc,EAAEe,YAAY,EAAEJ,QAAQ,EAAEV,UAAU,EAAE;QAC7EF,QAAQ,CAACC,cAAc,EAAE,UAAU,CAAC;MACtC;IACF,CAAC;IAED,IAAIgB,MAAM,GAAGC,MAAM,CAACD,MAAM;IAE1B,IAAIE,WAAW,GAAG,CAAC,CAAC;IAEpB;MACED,MAAM,CAACE,MAAM,CAACD,WAAW,CAAC;IAC5B;IACA;AACA;AACA;;IAGA,SAASE,SAASA,CAACC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAE;MAC1C,IAAI,CAACF,KAAK,GAAGA,KAAK;MAClB,IAAI,CAACC,OAAO,GAAGA,OAAO,CAAC,CAAC;;MAExB,IAAI,CAACE,IAAI,GAAGN,WAAW,CAAC,CAAC;MACzB;;MAEA,IAAI,CAACK,OAAO,GAAGA,OAAO,IAAIf,oBAAoB;IAChD;IAEAY,SAAS,CAAC1B,SAAS,CAAC+B,gBAAgB,GAAG,CAAC,CAAC;IACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAEAL,SAAS,CAAC1B,SAAS,CAACgC,QAAQ,GAAG,UAAUX,YAAY,EAAEJ,QAAQ,EAAE;MAC/D,IAAI,OAAOI,YAAY,KAAK,QAAQ,IAAI,OAAOA,YAAY,KAAK,UAAU,IAAIA,YAAY,IAAI,IAAI,EAAE;QAClG,MAAM,IAAIrF,KAAK,CAAC,mEAAmE,GAAG,sDAAsD,CAAC;MAC/I;MAEA,IAAI,CAAC6F,OAAO,CAACT,eAAe,CAAC,IAAI,EAAEC,YAAY,EAAEJ,QAAQ,EAAE,UAAU,CAAC;IACxE,CAAC;IACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAGAS,SAAS,CAAC1B,SAAS,CAACiC,WAAW,GAAG,UAAUhB,QAAQ,EAAE;MACpD,IAAI,CAACY,OAAO,CAACb,kBAAkB,CAAC,IAAI,EAAEC,QAAQ,EAAE,aAAa,CAAC;IAChE,CAAC;IACD;AACA;AACA;AACA;AACA;;IAGA;MACE,IAAIiB,cAAc,GAAG;QACnBnB,SAAS,EAAE,CAAC,WAAW,EAAE,uEAAuE,GAAG,+CAA+C,CAAC;QACnJoB,YAAY,EAAE,CAAC,cAAc,EAAE,kDAAkD,GAAG,iDAAiD;MACvI,CAAC;MAED,IAAIC,wBAAwB,GAAG,SAAAA,CAAUC,UAAU,EAAEC,IAAI,EAAE;QACzDf,MAAM,CAACgB,cAAc,CAACb,SAAS,CAAC1B,SAAS,EAAEqC,UAAU,EAAE;UACrDG,GAAG,EAAE,SAAAA,CAAA,EAAY;YACf5D,IAAI,CAAC,6DAA6D,EAAE0D,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;YAErF,OAAOG,SAAS;UAClB;QACF,CAAC,CAAC;MACJ,CAAC;MAED,KAAK,IAAIC,MAAM,IAAIR,cAAc,EAAE;QACjC,IAAIA,cAAc,CAACS,cAAc,CAACD,MAAM,CAAC,EAAE;UACzCN,wBAAwB,CAACM,MAAM,EAAER,cAAc,CAACQ,MAAM,CAAC,CAAC;QAC1D;MACF;IACF;IAEA,SAASE,cAAcA,CAAA,EAAG,CAAC;IAE3BA,cAAc,CAAC5C,SAAS,GAAG0B,SAAS,CAAC1B,SAAS;IAC9C;AACA;AACA;;IAEA,SAAS6C,aAAaA,CAAClB,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAE;MAC9C,IAAI,CAACF,KAAK,GAAGA,KAAK;MAClB,IAAI,CAACC,OAAO,GAAGA,OAAO,CAAC,CAAC;;MAExB,IAAI,CAACE,IAAI,GAAGN,WAAW;MACvB,IAAI,CAACK,OAAO,GAAGA,OAAO,IAAIf,oBAAoB;IAChD;IAEA,IAAIgC,sBAAsB,GAAGD,aAAa,CAAC7C,SAAS,GAAG,IAAI4C,cAAc,CAAC,CAAC;IAC3EE,sBAAsB,CAACrC,WAAW,GAAGoC,aAAa,CAAC,CAAC;;IAEpDvB,MAAM,CAACwB,sBAAsB,EAAEpB,SAAS,CAAC1B,SAAS,CAAC;IACnD8C,sBAAsB,CAACC,oBAAoB,GAAG,IAAI;;IAElD;IACA,SAASC,SAASA,CAAA,EAAG;MACnB,IAAIC,SAAS,GAAG;QACdzF,OAAO,EAAE;MACX,CAAC;MAED;QACE+D,MAAM,CAAC2B,IAAI,CAACD,SAAS,CAAC;MACxB;MAEA,OAAOA,SAAS;IAClB;IAEA,IAAIE,WAAW,GAAGjE,KAAK,CAACkE,OAAO,CAAC,CAAC;;IAEjC,SAASA,OAAOA,CAACC,CAAC,EAAE;MAClB,OAAOF,WAAW,CAACE,CAAC,CAAC;IACvB;;IAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACA;IACA,SAASC,QAAQA,CAACC,KAAK,EAAE;MACvB;QACE;QACA,IAAIC,cAAc,GAAG,OAAOrH,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACsH,WAAW;QACvE,IAAIC,IAAI,GAAGF,cAAc,IAAID,KAAK,CAACpH,MAAM,CAACsH,WAAW,CAAC,IAAIF,KAAK,CAAC9C,WAAW,CAACG,IAAI,IAAI,QAAQ;QAC5F,OAAO8C,IAAI;MACb;IACF,CAAC,CAAC;;IAGF,SAASC,iBAAiBA,CAACJ,KAAK,EAAE;MAChC;QACE,IAAI;UACFK,kBAAkB,CAACL,KAAK,CAAC;UACzB,OAAO,KAAK;QACd,CAAC,CAAC,OAAOM,CAAC,EAAE;UACV,OAAO,IAAI;QACb;MACF;IACF;IAEA,SAASD,kBAAkBA,CAACL,KAAK,EAAE;MACjC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,OAAO,EAAE,GAAGA,KAAK;IACnB;IACA,SAASO,sBAAsBA,CAACP,KAAK,EAAE;MACrC;QACE,IAAII,iBAAiB,CAACJ,KAAK,CAAC,EAAE;UAC5BlE,KAAK,CAAC,6CAA6C,GAAG,sEAAsE,EAAEiE,QAAQ,CAACC,KAAK,CAAC,CAAC;UAE9I,OAAOK,kBAAkB,CAACL,KAAK,CAAC,CAAC,CAAC;QACpC;MACF;IACF;IAEA,SAASQ,cAAcA,CAACC,SAAS,EAAEC,SAAS,EAAEC,WAAW,EAAE;MACzD,IAAIvD,WAAW,GAAGqD,SAAS,CAACrD,WAAW;MAEvC,IAAIA,WAAW,EAAE;QACf,OAAOA,WAAW;MACpB;MAEA,IAAIwD,YAAY,GAAGF,SAAS,CAACtD,WAAW,IAAIsD,SAAS,CAACrD,IAAI,IAAI,EAAE;MAChE,OAAOuD,YAAY,KAAK,EAAE,GAAGD,WAAW,GAAG,GAAG,GAAGC,YAAY,GAAG,GAAG,GAAGD,WAAW;IACnF,CAAC,CAAC;;IAGF,SAASE,cAAcA,CAACV,IAAI,EAAE;MAC5B,OAAOA,IAAI,CAAC/C,WAAW,IAAI,SAAS;IACtC,CAAC,CAAC;;IAGF,SAAS0D,wBAAwBA,CAACX,IAAI,EAAE;MACtC,IAAIA,IAAI,IAAI,IAAI,EAAE;QAChB;QACA,OAAO,IAAI;MACb;MAEA;QACE,IAAI,OAAOA,IAAI,CAACY,GAAG,KAAK,QAAQ,EAAE;UAChCjF,KAAK,CAAC,+DAA+D,GAAG,sDAAsD,CAAC;QACjI;MACF;MAEA,IAAI,OAAOqE,IAAI,KAAK,UAAU,EAAE;QAC9B,OAAOA,IAAI,CAAC/C,WAAW,IAAI+C,IAAI,CAAC9C,IAAI,IAAI,IAAI;MAC9C;MAEA,IAAI,OAAO8C,IAAI,KAAK,QAAQ,EAAE;QAC5B,OAAOA,IAAI;MACb;MAEA,QAAQA,IAAI;QACV,KAAKpH,mBAAmB;UACtB,OAAO,UAAU;QAEnB,KAAKD,iBAAiB;UACpB,OAAO,QAAQ;QAEjB,KAAKG,mBAAmB;UACtB,OAAO,UAAU;QAEnB,KAAKD,sBAAsB;UACzB,OAAO,YAAY;QAErB,KAAKK,mBAAmB;UACtB,OAAO,UAAU;QAEnB,KAAKC,wBAAwB;UAC3B,OAAO,cAAc;MAEzB;MAEA,IAAI,OAAO6G,IAAI,KAAK,QAAQ,EAAE;QAC5B,QAAQA,IAAI,CAACa,QAAQ;UACnB,KAAK7H,kBAAkB;YACrB,IAAIkF,OAAO,GAAG8B,IAAI;YAClB,OAAOU,cAAc,CAACxC,OAAO,CAAC,GAAG,WAAW;UAE9C,KAAKnF,mBAAmB;YACtB,IAAI+H,QAAQ,GAAGd,IAAI;YACnB,OAAOU,cAAc,CAACI,QAAQ,CAACC,QAAQ,CAAC,GAAG,WAAW;UAExD,KAAK9H,sBAAsB;YACzB,OAAOoH,cAAc,CAACL,IAAI,EAAEA,IAAI,CAACgB,MAAM,EAAE,YAAY,CAAC;UAExD,KAAK5H,eAAe;YAClB,IAAI6H,SAAS,GAAGjB,IAAI,CAAC/C,WAAW,IAAI,IAAI;YAExC,IAAIgE,SAAS,KAAK,IAAI,EAAE;cACtB,OAAOA,SAAS;YAClB;YAEA,OAAON,wBAAwB,CAACX,IAAI,CAACA,IAAI,CAAC,IAAI,MAAM;UAEtD,KAAK3G,eAAe;YAClB;cACE,IAAI6H,aAAa,GAAGlB,IAAI;cACxB,IAAImB,OAAO,GAAGD,aAAa,CAACE,QAAQ;cACpC,IAAIC,IAAI,GAAGH,aAAa,CAACI,KAAK;cAE9B,IAAI;gBACF,OAAOX,wBAAwB,CAACU,IAAI,CAACF,OAAO,CAAC,CAAC;cAChD,CAAC,CAAC,OAAOI,CAAC,EAAE;gBACV,OAAO,IAAI;cACb;YACF;;UAEF;QACF;MACF;MAEA,OAAO,IAAI;IACb;IAEA,IAAItC,cAAc,GAAGpB,MAAM,CAACvB,SAAS,CAAC2C,cAAc;IAEpD,IAAIuC,cAAc,GAAG;MACnBC,GAAG,EAAE,IAAI;MACTC,GAAG,EAAE,IAAI;MACTC,MAAM,EAAE,IAAI;MACZC,QAAQ,EAAE;IACZ,CAAC;IACD,IAAIC,0BAA0B,EAAEC,0BAA0B,EAAEC,sBAAsB;IAElF;MACEA,sBAAsB,GAAG,CAAC,CAAC;IAC7B;IAEA,SAASC,WAAWA,CAACC,MAAM,EAAE;MAC3B;QACE,IAAIhD,cAAc,CAACzC,IAAI,CAACyF,MAAM,EAAE,KAAK,CAAC,EAAE;UACtC,IAAIC,MAAM,GAAGrE,MAAM,CAACsE,wBAAwB,CAACF,MAAM,EAAE,KAAK,CAAC,CAACnD,GAAG;UAE/D,IAAIoD,MAAM,IAAIA,MAAM,CAACE,cAAc,EAAE;YACnC,OAAO,KAAK;UACd;QACF;MACF;MAEA,OAAOH,MAAM,CAACP,GAAG,KAAK3C,SAAS;IACjC;IAEA,SAASsD,WAAWA,CAACJ,MAAM,EAAE;MAC3B;QACE,IAAIhD,cAAc,CAACzC,IAAI,CAACyF,MAAM,EAAE,KAAK,CAAC,EAAE;UACtC,IAAIC,MAAM,GAAGrE,MAAM,CAACsE,wBAAwB,CAACF,MAAM,EAAE,KAAK,CAAC,CAACnD,GAAG;UAE/D,IAAIoD,MAAM,IAAIA,MAAM,CAACE,cAAc,EAAE;YACnC,OAAO,KAAK;UACd;QACF;MACF;MAEA,OAAOH,MAAM,CAACR,GAAG,KAAK1C,SAAS;IACjC;IAEA,SAASuD,0BAA0BA,CAACrE,KAAK,EAAEhB,WAAW,EAAE;MACtD,IAAIsF,qBAAqB,GAAG,SAAAA,CAAA,EAAY;QACtC;UACE,IAAI,CAACV,0BAA0B,EAAE;YAC/BA,0BAA0B,GAAG,IAAI;YAEjClG,KAAK,CAAC,2DAA2D,GAAG,gEAAgE,GAAG,sEAAsE,GAAG,gDAAgD,EAAEsB,WAAW,CAAC;UAChR;QACF;MACF,CAAC;MAEDsF,qBAAqB,CAACH,cAAc,GAAG,IAAI;MAC3CvE,MAAM,CAACgB,cAAc,CAACZ,KAAK,EAAE,KAAK,EAAE;QAClCa,GAAG,EAAEyD,qBAAqB;QAC1BC,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ;IAEA,SAASC,0BAA0BA,CAACxE,KAAK,EAAEhB,WAAW,EAAE;MACtD,IAAIyF,qBAAqB,GAAG,SAAAA,CAAA,EAAY;QACtC;UACE,IAAI,CAACZ,0BAA0B,EAAE;YAC/BA,0BAA0B,GAAG,IAAI;YAEjCnG,KAAK,CAAC,2DAA2D,GAAG,gEAAgE,GAAG,sEAAsE,GAAG,gDAAgD,EAAEsB,WAAW,CAAC;UAChR;QACF;MACF,CAAC;MAEDyF,qBAAqB,CAACN,cAAc,GAAG,IAAI;MAC3CvE,MAAM,CAACgB,cAAc,CAACZ,KAAK,EAAE,KAAK,EAAE;QAClCa,GAAG,EAAE4D,qBAAqB;QAC1BF,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ;IAEA,SAASG,oCAAoCA,CAACV,MAAM,EAAE;MACpD;QACE,IAAI,OAAOA,MAAM,CAACP,GAAG,KAAK,QAAQ,IAAItH,iBAAiB,CAACN,OAAO,IAAImI,MAAM,CAACN,MAAM,IAAIvH,iBAAiB,CAACN,OAAO,CAAC8I,SAAS,KAAKX,MAAM,CAACN,MAAM,EAAE;UACzI,IAAI3E,aAAa,GAAG2D,wBAAwB,CAACvG,iBAAiB,CAACN,OAAO,CAACkG,IAAI,CAAC;UAE5E,IAAI,CAAC+B,sBAAsB,CAAC/E,aAAa,CAAC,EAAE;YAC1CrB,KAAK,CAAC,+CAA+C,GAAG,qEAAqE,GAAG,oEAAoE,GAAG,iFAAiF,GAAG,2CAA2C,GAAG,iDAAiD,EAAEqB,aAAa,EAAEiF,MAAM,CAACP,GAAG,CAAC;YAEtZK,sBAAsB,CAAC/E,aAAa,CAAC,GAAG,IAAI;UAC9C;QACF;MACF;IACF;IACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAGA,IAAI6F,YAAY,GAAG,SAAAA,CAAU7C,IAAI,EAAEyB,GAAG,EAAEC,GAAG,EAAEoB,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAE/E,KAAK,EAAE;MACvE,IAAIgF,OAAO,GAAG;QACZ;QACApC,QAAQ,EAAErI,kBAAkB;QAC5B;QACAwH,IAAI,EAAEA,IAAI;QACVyB,GAAG,EAAEA,GAAG;QACRC,GAAG,EAAEA,GAAG;QACRzD,KAAK,EAAEA,KAAK;QACZ;QACAiF,MAAM,EAAEF;MACV,CAAC;MAED;QACE;QACA;QACA;QACA;QACAC,OAAO,CAACE,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;QACrB;QACA;QACA;;QAEAtF,MAAM,CAACgB,cAAc,CAACoE,OAAO,CAACE,MAAM,EAAE,WAAW,EAAE;UACjDX,YAAY,EAAE,KAAK;UACnBY,UAAU,EAAE,KAAK;UACjBC,QAAQ,EAAE,IAAI;UACdxD,KAAK,EAAE;QACT,CAAC,CAAC,CAAC,CAAC;;QAEJhC,MAAM,CAACgB,cAAc,CAACoE,OAAO,EAAE,OAAO,EAAE;UACtCT,YAAY,EAAE,KAAK;UACnBY,UAAU,EAAE,KAAK;UACjBC,QAAQ,EAAE,KAAK;UACfxD,KAAK,EAAEiD;QACT,CAAC,CAAC,CAAC,CAAC;QACJ;;QAEAjF,MAAM,CAACgB,cAAc,CAACoE,OAAO,EAAE,SAAS,EAAE;UACxCT,YAAY,EAAE,KAAK;UACnBY,UAAU,EAAE,KAAK;UACjBC,QAAQ,EAAE,KAAK;UACfxD,KAAK,EAAEkD;QACT,CAAC,CAAC;QAEF,IAAIlF,MAAM,CAACE,MAAM,EAAE;UACjBF,MAAM,CAACE,MAAM,CAACkF,OAAO,CAAChF,KAAK,CAAC;UAC5BJ,MAAM,CAACE,MAAM,CAACkF,OAAO,CAAC;QACxB;MACF;MAEA,OAAOA,OAAO;IAChB,CAAC;IACD;AACA;AACA;AACA;;IAEA,SAASK,aAAaA,CAACtD,IAAI,EAAEiC,MAAM,EAAEsB,QAAQ,EAAE;MAC7C,IAAIC,QAAQ,CAAC,CAAC;;MAEd,IAAIvF,KAAK,GAAG,CAAC,CAAC;MACd,IAAIwD,GAAG,GAAG,IAAI;MACd,IAAIC,GAAG,GAAG,IAAI;MACd,IAAIoB,IAAI,GAAG,IAAI;MACf,IAAIC,MAAM,GAAG,IAAI;MAEjB,IAAId,MAAM,IAAI,IAAI,EAAE;QAClB,IAAID,WAAW,CAACC,MAAM,CAAC,EAAE;UACvBP,GAAG,GAAGO,MAAM,CAACP,GAAG;UAEhB;YACEiB,oCAAoC,CAACV,MAAM,CAAC;UAC9C;QACF;QAEA,IAAII,WAAW,CAACJ,MAAM,CAAC,EAAE;UACvB;YACE7B,sBAAsB,CAAC6B,MAAM,CAACR,GAAG,CAAC;UACpC;UAEAA,GAAG,GAAG,EAAE,GAAGQ,MAAM,CAACR,GAAG;QACvB;QAEAqB,IAAI,GAAGb,MAAM,CAACN,MAAM,KAAK5C,SAAS,GAAG,IAAI,GAAGkD,MAAM,CAACN,MAAM;QACzDoB,MAAM,GAAGd,MAAM,CAACL,QAAQ,KAAK7C,SAAS,GAAG,IAAI,GAAGkD,MAAM,CAACL,QAAQ,CAAC,CAAC;;QAEjE,KAAK4B,QAAQ,IAAIvB,MAAM,EAAE;UACvB,IAAIhD,cAAc,CAACzC,IAAI,CAACyF,MAAM,EAAEuB,QAAQ,CAAC,IAAI,CAAChC,cAAc,CAACvC,cAAc,CAACuE,QAAQ,CAAC,EAAE;YACrFvF,KAAK,CAACuF,QAAQ,CAAC,GAAGvB,MAAM,CAACuB,QAAQ,CAAC;UACpC;QACF;MACF,CAAC,CAAC;MACF;;MAGA,IAAIC,cAAc,GAAGpI,SAAS,CAACC,MAAM,GAAG,CAAC;MAEzC,IAAImI,cAAc,KAAK,CAAC,EAAE;QACxBxF,KAAK,CAACsF,QAAQ,GAAGA,QAAQ;MAC3B,CAAC,MAAM,IAAIE,cAAc,GAAG,CAAC,EAAE;QAC7B,IAAIC,UAAU,GAAGlI,KAAK,CAACiI,cAAc,CAAC;QAEtC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,cAAc,EAAEE,CAAC,EAAE,EAAE;UACvCD,UAAU,CAACC,CAAC,CAAC,GAAGtI,SAAS,CAACsI,CAAC,GAAG,CAAC,CAAC;QAClC;QAEA;UACE,IAAI9F,MAAM,CAACE,MAAM,EAAE;YACjBF,MAAM,CAACE,MAAM,CAAC2F,UAAU,CAAC;UAC3B;QACF;QAEAzF,KAAK,CAACsF,QAAQ,GAAGG,UAAU;MAC7B,CAAC,CAAC;;MAGF,IAAI1D,IAAI,IAAIA,IAAI,CAAC4D,YAAY,EAAE;QAC7B,IAAIA,YAAY,GAAG5D,IAAI,CAAC4D,YAAY;QAEpC,KAAKJ,QAAQ,IAAII,YAAY,EAAE;UAC7B,IAAI3F,KAAK,CAACuF,QAAQ,CAAC,KAAKzE,SAAS,EAAE;YACjCd,KAAK,CAACuF,QAAQ,CAAC,GAAGI,YAAY,CAACJ,QAAQ,CAAC;UAC1C;QACF;MACF;MAEA;QACE,IAAI/B,GAAG,IAAIC,GAAG,EAAE;UACd,IAAIzE,WAAW,GAAG,OAAO+C,IAAI,KAAK,UAAU,GAAGA,IAAI,CAAC/C,WAAW,IAAI+C,IAAI,CAAC9C,IAAI,IAAI,SAAS,GAAG8C,IAAI;UAEhG,IAAIyB,GAAG,EAAE;YACPa,0BAA0B,CAACrE,KAAK,EAAEhB,WAAW,CAAC;UAChD;UAEA,IAAIyE,GAAG,EAAE;YACPe,0BAA0B,CAACxE,KAAK,EAAEhB,WAAW,CAAC;UAChD;QACF;MACF;MAEA,OAAO4F,YAAY,CAAC7C,IAAI,EAAEyB,GAAG,EAAEC,GAAG,EAAEoB,IAAI,EAAEC,MAAM,EAAE3I,iBAAiB,CAACN,OAAO,EAAEmE,KAAK,CAAC;IACrF;IACA,SAAS4F,kBAAkBA,CAACC,UAAU,EAAEC,MAAM,EAAE;MAC9C,IAAIC,UAAU,GAAGnB,YAAY,CAACiB,UAAU,CAAC9D,IAAI,EAAE+D,MAAM,EAAED,UAAU,CAACpC,GAAG,EAAEoC,UAAU,CAACG,KAAK,EAAEH,UAAU,CAACI,OAAO,EAAEJ,UAAU,CAACZ,MAAM,EAAEY,UAAU,CAAC7F,KAAK,CAAC;MACjJ,OAAO+F,UAAU;IACnB;IACA;AACA;AACA;AACA;;IAEA,SAASG,YAAYA,CAAClB,OAAO,EAAEhB,MAAM,EAAEsB,QAAQ,EAAE;MAC/C,IAAIN,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAKlE,SAAS,EAAE;QAC7C,MAAM,IAAIzG,KAAK,CAAC,gFAAgF,GAAG2K,OAAO,GAAG,GAAG,CAAC;MACnH;MAEA,IAAIO,QAAQ,CAAC,CAAC;;MAEd,IAAIvF,KAAK,GAAGL,MAAM,CAAC,CAAC,CAAC,EAAEqF,OAAO,CAAChF,KAAK,CAAC,CAAC,CAAC;;MAEvC,IAAIwD,GAAG,GAAGwB,OAAO,CAACxB,GAAG;MACrB,IAAIC,GAAG,GAAGuB,OAAO,CAACvB,GAAG,CAAC,CAAC;;MAEvB,IAAIoB,IAAI,GAAGG,OAAO,CAACgB,KAAK,CAAC,CAAC;MAC1B;MACA;;MAEA,IAAIlB,MAAM,GAAGE,OAAO,CAACiB,OAAO,CAAC,CAAC;;MAE9B,IAAIlB,KAAK,GAAGC,OAAO,CAACC,MAAM;MAE1B,IAAIjB,MAAM,IAAI,IAAI,EAAE;QAClB,IAAID,WAAW,CAACC,MAAM,CAAC,EAAE;UACvB;UACAP,GAAG,GAAGO,MAAM,CAACP,GAAG;UAChBsB,KAAK,GAAG5I,iBAAiB,CAACN,OAAO;QACnC;QAEA,IAAIuI,WAAW,CAACJ,MAAM,CAAC,EAAE;UACvB;YACE7B,sBAAsB,CAAC6B,MAAM,CAACR,GAAG,CAAC;UACpC;UAEAA,GAAG,GAAG,EAAE,GAAGQ,MAAM,CAACR,GAAG;QACvB,CAAC,CAAC;;QAGF,IAAImC,YAAY;QAEhB,IAAIX,OAAO,CAACjD,IAAI,IAAIiD,OAAO,CAACjD,IAAI,CAAC4D,YAAY,EAAE;UAC7CA,YAAY,GAAGX,OAAO,CAACjD,IAAI,CAAC4D,YAAY;QAC1C;QAEA,KAAKJ,QAAQ,IAAIvB,MAAM,EAAE;UACvB,IAAIhD,cAAc,CAACzC,IAAI,CAACyF,MAAM,EAAEuB,QAAQ,CAAC,IAAI,CAAChC,cAAc,CAACvC,cAAc,CAACuE,QAAQ,CAAC,EAAE;YACrF,IAAIvB,MAAM,CAACuB,QAAQ,CAAC,KAAKzE,SAAS,IAAI6E,YAAY,KAAK7E,SAAS,EAAE;cAChE;cACAd,KAAK,CAACuF,QAAQ,CAAC,GAAGI,YAAY,CAACJ,QAAQ,CAAC;YAC1C,CAAC,MAAM;cACLvF,KAAK,CAACuF,QAAQ,CAAC,GAAGvB,MAAM,CAACuB,QAAQ,CAAC;YACpC;UACF;QACF;MACF,CAAC,CAAC;MACF;;MAGA,IAAIC,cAAc,GAAGpI,SAAS,CAACC,MAAM,GAAG,CAAC;MAEzC,IAAImI,cAAc,KAAK,CAAC,EAAE;QACxBxF,KAAK,CAACsF,QAAQ,GAAGA,QAAQ;MAC3B,CAAC,MAAM,IAAIE,cAAc,GAAG,CAAC,EAAE;QAC7B,IAAIC,UAAU,GAAGlI,KAAK,CAACiI,cAAc,CAAC;QAEtC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,cAAc,EAAEE,CAAC,EAAE,EAAE;UACvCD,UAAU,CAACC,CAAC,CAAC,GAAGtI,SAAS,CAACsI,CAAC,GAAG,CAAC,CAAC;QAClC;QAEA1F,KAAK,CAACsF,QAAQ,GAAGG,UAAU;MAC7B;MAEA,OAAOb,YAAY,CAACI,OAAO,CAACjD,IAAI,EAAEyB,GAAG,EAAEC,GAAG,EAAEoB,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAE/E,KAAK,CAAC;IACzE;IACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAEA,SAASmG,cAAcA,CAACC,MAAM,EAAE;MAC9B,OAAO,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACxD,QAAQ,KAAKrI,kBAAkB;IAChG;IAEA,IAAI8L,SAAS,GAAG,GAAG;IACnB,IAAIC,YAAY,GAAG,GAAG;IACtB;AACA;AACA;AACA;AACA;AACA;;IAEA,SAASC,MAAMA,CAAC/C,GAAG,EAAE;MACnB,IAAIgD,WAAW,GAAG,OAAO;MACzB,IAAIC,aAAa,GAAG;QAClB,GAAG,EAAE,IAAI;QACT,GAAG,EAAE;MACP,CAAC;MACD,IAAIC,aAAa,GAAGlD,GAAG,CAACmD,OAAO,CAACH,WAAW,EAAE,UAAUI,KAAK,EAAE;QAC5D,OAAOH,aAAa,CAACG,KAAK,CAAC;MAC7B,CAAC,CAAC;MACF,OAAO,GAAG,GAAGF,aAAa;IAC5B;IACA;AACA;AACA;AACA;;IAGA,IAAIG,gBAAgB,GAAG,KAAK;IAC5B,IAAIC,0BAA0B,GAAG,MAAM;IAEvC,SAASC,qBAAqBA,CAACC,IAAI,EAAE;MACnC,OAAOA,IAAI,CAACL,OAAO,CAACG,0BAA0B,EAAE,KAAK,CAAC;IACxD;IACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAGA,SAASG,aAAaA,CAACjC,OAAO,EAAEkC,KAAK,EAAE;MACrC;MACA;MACA,IAAI,OAAOlC,OAAO,KAAK,QAAQ,IAAIA,OAAO,KAAK,IAAI,IAAIA,OAAO,CAACxB,GAAG,IAAI,IAAI,EAAE;QAC1E;QACA;UACErB,sBAAsB,CAAC6C,OAAO,CAACxB,GAAG,CAAC;QACrC;QAEA,OAAO+C,MAAM,CAAC,EAAE,GAAGvB,OAAO,CAACxB,GAAG,CAAC;MACjC,CAAC,CAAC;;MAGF,OAAO0D,KAAK,CAACC,QAAQ,CAAC,EAAE,CAAC;IAC3B;IAEA,SAASC,YAAYA,CAAC9B,QAAQ,EAAE+B,KAAK,EAAEC,aAAa,EAAEC,SAAS,EAAEjI,QAAQ,EAAE;MACzE,IAAIyC,IAAI,GAAG,OAAOuD,QAAQ;MAE1B,IAAIvD,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,SAAS,EAAE;QAC9C;QACAuD,QAAQ,GAAG,IAAI;MACjB;MAEA,IAAIkC,cAAc,GAAG,KAAK;MAE1B,IAAIlC,QAAQ,KAAK,IAAI,EAAE;QACrBkC,cAAc,GAAG,IAAI;MACvB,CAAC,MAAM;QACL,QAAQzF,IAAI;UACV,KAAK,QAAQ;UACb,KAAK,QAAQ;YACXyF,cAAc,GAAG,IAAI;YACrB;UAEF,KAAK,QAAQ;YACX,QAAQlC,QAAQ,CAAC1C,QAAQ;cACvB,KAAKrI,kBAAkB;cACvB,KAAKG,iBAAiB;gBACpB8M,cAAc,GAAG,IAAI;YACzB;QAEJ;MACF;MAEA,IAAIA,cAAc,EAAE;QAClB,IAAIC,MAAM,GAAGnC,QAAQ;QACrB,IAAIoC,WAAW,GAAGpI,QAAQ,CAACmI,MAAM,CAAC,CAAC,CAAC;QACpC;;QAEA,IAAIE,QAAQ,GAAGJ,SAAS,KAAK,EAAE,GAAGlB,SAAS,GAAGY,aAAa,CAACQ,MAAM,EAAE,CAAC,CAAC,GAAGF,SAAS;QAElF,IAAI9F,OAAO,CAACiG,WAAW,CAAC,EAAE;UACxB,IAAIE,eAAe,GAAG,EAAE;UAExB,IAAID,QAAQ,IAAI,IAAI,EAAE;YACpBC,eAAe,GAAGb,qBAAqB,CAACY,QAAQ,CAAC,GAAG,GAAG;UACzD;UAEAP,YAAY,CAACM,WAAW,EAAEL,KAAK,EAAEO,eAAe,EAAE,EAAE,EAAE,UAAUC,CAAC,EAAE;YACjE,OAAOA,CAAC;UACV,CAAC,CAAC;QACJ,CAAC,MAAM,IAAIH,WAAW,IAAI,IAAI,EAAE;UAC9B,IAAIvB,cAAc,CAACuB,WAAW,CAAC,EAAE;YAC/B;cACE;cACA;cACA;cACA,IAAIA,WAAW,CAAClE,GAAG,KAAK,CAACiE,MAAM,IAAIA,MAAM,CAACjE,GAAG,KAAKkE,WAAW,CAAClE,GAAG,CAAC,EAAE;gBAClErB,sBAAsB,CAACuF,WAAW,CAAClE,GAAG,CAAC;cACzC;YACF;YAEAkE,WAAW,GAAG9B,kBAAkB,CAAC8B,WAAW;YAAE;YAC9C;YACAJ,aAAa;YAAK;YAClBI,WAAW,CAAClE,GAAG,KAAK,CAACiE,MAAM,IAAIA,MAAM,CAACjE,GAAG,KAAKkE,WAAW,CAAClE,GAAG,CAAC;YAAG;YACjE;YACAuD,qBAAqB,CAAC,EAAE,GAAGW,WAAW,CAAClE,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,GAAGmE,QAAQ,CAAC;UACrE;UAEAN,KAAK,CAACS,IAAI,CAACJ,WAAW,CAAC;QACzB;QAEA,OAAO,CAAC;MACV;MAEA,IAAIK,KAAK;MACT,IAAIC,QAAQ;MACZ,IAAIC,YAAY,GAAG,CAAC,CAAC,CAAC;;MAEtB,IAAIC,cAAc,GAAGX,SAAS,KAAK,EAAE,GAAGlB,SAAS,GAAGkB,SAAS,GAAGjB,YAAY;MAE5E,IAAI7E,OAAO,CAAC6D,QAAQ,CAAC,EAAE;QACrB,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,QAAQ,CAACjI,MAAM,EAAEqI,CAAC,EAAE,EAAE;UACxCqC,KAAK,GAAGzC,QAAQ,CAACI,CAAC,CAAC;UACnBsC,QAAQ,GAAGE,cAAc,GAAGjB,aAAa,CAACc,KAAK,EAAErC,CAAC,CAAC;UACnDuC,YAAY,IAAIb,YAAY,CAACW,KAAK,EAAEV,KAAK,EAAEC,aAAa,EAAEU,QAAQ,EAAE1I,QAAQ,CAAC;QAC/E;MACF,CAAC,MAAM;QACL,IAAI6I,UAAU,GAAG1M,aAAa,CAAC6J,QAAQ,CAAC;QAExC,IAAI,OAAO6C,UAAU,KAAK,UAAU,EAAE;UACpC,IAAIC,gBAAgB,GAAG9C,QAAQ;UAE/B;YACE;YACA,IAAI6C,UAAU,KAAKC,gBAAgB,CAACC,OAAO,EAAE;cAC3C,IAAI,CAACxB,gBAAgB,EAAE;gBACrB5J,IAAI,CAAC,2CAA2C,GAAG,8CAA8C,CAAC;cACpG;cAEA4J,gBAAgB,GAAG,IAAI;YACzB;UACF;UAEA,IAAItL,QAAQ,GAAG4M,UAAU,CAAC5J,IAAI,CAAC6J,gBAAgB,CAAC;UAChD,IAAIE,IAAI;UACR,IAAIC,EAAE,GAAG,CAAC;UAEV,OAAO,CAAC,CAACD,IAAI,GAAG/M,QAAQ,CAACiN,IAAI,CAAC,CAAC,EAAEC,IAAI,EAAE;YACrCV,KAAK,GAAGO,IAAI,CAAC1G,KAAK;YAClBoG,QAAQ,GAAGE,cAAc,GAAGjB,aAAa,CAACc,KAAK,EAAEQ,EAAE,EAAE,CAAC;YACtDN,YAAY,IAAIb,YAAY,CAACW,KAAK,EAAEV,KAAK,EAAEC,aAAa,EAAEU,QAAQ,EAAE1I,QAAQ,CAAC;UAC/E;QACF,CAAC,MAAM,IAAIyC,IAAI,KAAK,QAAQ,EAAE;UAC5B;UACA,IAAI2G,cAAc,GAAGxK,MAAM,CAACoH,QAAQ,CAAC;UACrC,MAAM,IAAIjL,KAAK,CAAC,iDAAiD,IAAIqO,cAAc,KAAK,iBAAiB,GAAG,oBAAoB,GAAG9I,MAAM,CAAC+I,IAAI,CAACrD,QAAQ,CAAC,CAACsD,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,GAAGF,cAAc,CAAC,GAAG,KAAK,GAAG,gEAAgE,GAAG,UAAU,CAAC;QACtR;MACF;MAEA,OAAOT,YAAY;IACrB;;IAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACA,SAASY,WAAWA,CAACvD,QAAQ,EAAEwD,IAAI,EAAE7I,OAAO,EAAE;MAC5C,IAAIqF,QAAQ,IAAI,IAAI,EAAE;QACpB,OAAOA,QAAQ;MACjB;MAEA,IAAIyD,MAAM,GAAG,EAAE;MACf,IAAIC,KAAK,GAAG,CAAC;MACb5B,YAAY,CAAC9B,QAAQ,EAAEyD,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,UAAUhB,KAAK,EAAE;QACtD,OAAOe,IAAI,CAACvK,IAAI,CAAC0B,OAAO,EAAE8H,KAAK,EAAEiB,KAAK,EAAE,CAAC;MAC3C,CAAC,CAAC;MACF,OAAOD,MAAM;IACf;IACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAGA,SAASE,aAAaA,CAAC3D,QAAQ,EAAE;MAC/B,IAAI4D,CAAC,GAAG,CAAC;MACTL,WAAW,CAACvD,QAAQ,EAAE,YAAY;QAChC4D,CAAC,EAAE,CAAC,CAAC;MACP,CAAC,CAAC;MACF,OAAOA,CAAC;IACV;;IAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACA,SAASC,eAAeA,CAAC7D,QAAQ,EAAE8D,WAAW,EAAEC,cAAc,EAAE;MAC9DR,WAAW,CAACvD,QAAQ,EAAE,YAAY;QAChC8D,WAAW,CAAC9K,KAAK,CAAC,IAAI,EAAElB,SAAS,CAAC,CAAC,CAAC;MACtC,CAAC,EAAEiM,cAAc,CAAC;IACpB;IACA;AACA;AACA;AACA;AACA;AACA;;IAGA,SAASC,OAAOA,CAAChE,QAAQ,EAAE;MACzB,OAAOuD,WAAW,CAACvD,QAAQ,EAAE,UAAUyC,KAAK,EAAE;QAC5C,OAAOA,KAAK;MACd,CAAC,CAAC,IAAI,EAAE;IACV;IACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAGA,SAASwB,SAASA,CAACjE,QAAQ,EAAE;MAC3B,IAAI,CAACa,cAAc,CAACb,QAAQ,CAAC,EAAE;QAC7B,MAAM,IAAIjL,KAAK,CAAC,uEAAuE,CAAC;MAC1F;MAEA,OAAOiL,QAAQ;IACjB;IAEA,SAASkE,aAAaA,CAACC,YAAY,EAAE;MACnC;MACA;MACA,IAAIxJ,OAAO,GAAG;QACZ2C,QAAQ,EAAE7H,kBAAkB;QAC5B;QACA;QACA;QACA;QACA;QACA2O,aAAa,EAAED,YAAY;QAC3BE,cAAc,EAAEF,YAAY;QAC5B;QACA;QACAG,YAAY,EAAE,CAAC;QACf;QACAC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACd;QACAC,aAAa,EAAE,IAAI;QACnBC,WAAW,EAAE;MACf,CAAC;MACD/J,OAAO,CAAC4J,QAAQ,GAAG;QACjBjH,QAAQ,EAAE9H,mBAAmB;QAC7BgI,QAAQ,EAAE7C;MACZ,CAAC;MACD,IAAIgK,yCAAyC,GAAG,KAAK;MACrD,IAAIC,mCAAmC,GAAG,KAAK;MAC/C,IAAIC,mCAAmC,GAAG,KAAK;MAE/C;QACE;QACA;QACA;QACA,IAAIL,QAAQ,GAAG;UACblH,QAAQ,EAAE7H,kBAAkB;UAC5B+H,QAAQ,EAAE7C;QACZ,CAAC,CAAC,CAAC;;QAEHL,MAAM,CAACwK,gBAAgB,CAACN,QAAQ,EAAE;UAChCD,QAAQ,EAAE;YACRhJ,GAAG,EAAE,SAAAA,CAAA,EAAY;cACf,IAAI,CAACqJ,mCAAmC,EAAE;gBACxCA,mCAAmC,GAAG,IAAI;gBAE1CxM,KAAK,CAAC,gFAAgF,GAAG,4EAA4E,CAAC;cACxK;cAEA,OAAOuC,OAAO,CAAC4J,QAAQ;YACzB,CAAC;YACDQ,GAAG,EAAE,SAAAA,CAAUC,SAAS,EAAE;cACxBrK,OAAO,CAAC4J,QAAQ,GAAGS,SAAS;YAC9B;UACF,CAAC;UACDZ,aAAa,EAAE;YACb7I,GAAG,EAAE,SAAAA,CAAA,EAAY;cACf,OAAOZ,OAAO,CAACyJ,aAAa;YAC9B,CAAC;YACDW,GAAG,EAAE,SAAAA,CAAUX,aAAa,EAAE;cAC5BzJ,OAAO,CAACyJ,aAAa,GAAGA,aAAa;YACvC;UACF,CAAC;UACDC,cAAc,EAAE;YACd9I,GAAG,EAAE,SAAAA,CAAA,EAAY;cACf,OAAOZ,OAAO,CAAC0J,cAAc;YAC/B,CAAC;YACDU,GAAG,EAAE,SAAAA,CAAUV,cAAc,EAAE;cAC7B1J,OAAO,CAAC0J,cAAc,GAAGA,cAAc;YACzC;UACF,CAAC;UACDC,YAAY,EAAE;YACZ/I,GAAG,EAAE,SAAAA,CAAA,EAAY;cACf,OAAOZ,OAAO,CAAC2J,YAAY;YAC7B,CAAC;YACDS,GAAG,EAAE,SAAAA,CAAUT,YAAY,EAAE;cAC3B3J,OAAO,CAAC2J,YAAY,GAAGA,YAAY;YACrC;UACF,CAAC;UACDE,QAAQ,EAAE;YACRjJ,GAAG,EAAE,SAAAA,CAAA,EAAY;cACf,IAAI,CAACoJ,yCAAyC,EAAE;gBAC9CA,yCAAyC,GAAG,IAAI;gBAEhDvM,KAAK,CAAC,gFAAgF,GAAG,4EAA4E,CAAC;cACxK;cAEA,OAAOuC,OAAO,CAAC6J,QAAQ;YACzB;UACF,CAAC;UACD9K,WAAW,EAAE;YACX6B,GAAG,EAAE,SAAAA,CAAA,EAAY;cACf,OAAOZ,OAAO,CAACjB,WAAW;YAC5B,CAAC;YACDqL,GAAG,EAAE,SAAAA,CAAUrL,WAAW,EAAE;cAC1B,IAAI,CAACmL,mCAAmC,EAAE;gBACxClN,IAAI,CAAC,2DAA2D,GAAG,4EAA4E,EAAE+B,WAAW,CAAC;gBAE7JmL,mCAAmC,GAAG,IAAI;cAC5C;YACF;UACF;QACF,CAAC,CAAC,CAAC,CAAC;;QAEJlK,OAAO,CAAC6J,QAAQ,GAAGA,QAAQ;MAC7B;MAEA;QACE7J,OAAO,CAACsK,gBAAgB,GAAG,IAAI;QAC/BtK,OAAO,CAACuK,iBAAiB,GAAG,IAAI;MAClC;MAEA,OAAOvK,OAAO;IAChB;IAEA,IAAIwK,aAAa,GAAG,CAAC,CAAC;IACtB,IAAIC,OAAO,GAAG,CAAC;IACf,IAAIC,QAAQ,GAAG,CAAC;IAChB,IAAIC,QAAQ,GAAG,CAAC;IAEhB,SAASC,eAAeA,CAAC3H,OAAO,EAAE;MAChC,IAAIA,OAAO,CAAC4H,OAAO,KAAKL,aAAa,EAAE;QACrC,IAAIM,IAAI,GAAG7H,OAAO,CAAC8H,OAAO;QAC1B,IAAIC,QAAQ,GAAGF,IAAI,CAAC,CAAC,CAAC,CAAC;QACvB;QACA;QACA;QACA;;QAEAE,QAAQ,CAACC,IAAI,CAAC,UAAUC,YAAY,EAAE;UACpC,IAAIjI,OAAO,CAAC4H,OAAO,KAAKJ,OAAO,IAAIxH,OAAO,CAAC4H,OAAO,KAAKL,aAAa,EAAE;YACpE;YACA,IAAIW,QAAQ,GAAGlI,OAAO;YACtBkI,QAAQ,CAACN,OAAO,GAAGH,QAAQ;YAC3BS,QAAQ,CAACJ,OAAO,GAAGG,YAAY;UACjC;QACF,CAAC,EAAE,UAAUzN,KAAK,EAAE;UAClB,IAAIwF,OAAO,CAAC4H,OAAO,KAAKJ,OAAO,IAAIxH,OAAO,CAAC4H,OAAO,KAAKL,aAAa,EAAE;YACpE;YACA,IAAIY,QAAQ,GAAGnI,OAAO;YACtBmI,QAAQ,CAACP,OAAO,GAAGF,QAAQ;YAC3BS,QAAQ,CAACL,OAAO,GAAGtN,KAAK;UAC1B;QACF,CAAC,CAAC;QAEF,IAAIwF,OAAO,CAAC4H,OAAO,KAAKL,aAAa,EAAE;UACrC;UACA;UACA,IAAIa,OAAO,GAAGpI,OAAO;UACrBoI,OAAO,CAACR,OAAO,GAAGJ,OAAO;UACzBY,OAAO,CAACN,OAAO,GAAGC,QAAQ;QAC5B;MACF;MAEA,IAAI/H,OAAO,CAAC4H,OAAO,KAAKH,QAAQ,EAAE;QAChC,IAAIQ,YAAY,GAAGjI,OAAO,CAAC8H,OAAO;QAElC;UACE,IAAIG,YAAY,KAAKrK,SAAS,EAAE;YAC9BpD,KAAK,CAAC,4CAA4C,GAAG,cAAc,GAAG,0DAA0D;YAAG;YACnI,oCAAoC,GAAG,2BAA2B,GAAG,0DAA0D,EAAEyN,YAAY,CAAC;UAChJ;QACF;QAEA;UACE,IAAI,EAAE,SAAS,IAAIA,YAAY,CAAC,EAAE;YAChCzN,KAAK,CAAC,4CAA4C,GAAG,cAAc,GAAG,0DAA0D;YAAG;YACnI,oCAAoC,GAAG,uBAAuB,EAAEyN,YAAY,CAAC;UAC/E;QACF;QAEA,OAAOA,YAAY,CAACI,OAAO;MAC7B,CAAC,MAAM;QACL,MAAMrI,OAAO,CAAC8H,OAAO;MACvB;IACF;IAEA,SAASQ,IAAIA,CAACT,IAAI,EAAE;MAClB,IAAI7H,OAAO,GAAG;QACZ;QACA4H,OAAO,EAAEL,aAAa;QACtBO,OAAO,EAAED;MACX,CAAC;MACD,IAAIU,QAAQ,GAAG;QACb7I,QAAQ,EAAExH,eAAe;QACzB+H,QAAQ,EAAED,OAAO;QACjBG,KAAK,EAAEwH;MACT,CAAC;MAED;QACE;QACA,IAAIlF,YAAY;QAChB,IAAI+F,SAAS,CAAC,CAAC;;QAEf9L,MAAM,CAACwK,gBAAgB,CAACqB,QAAQ,EAAE;UAChC9F,YAAY,EAAE;YACZpB,YAAY,EAAE,IAAI;YAClB1D,GAAG,EAAE,SAAAA,CAAA,EAAY;cACf,OAAO8E,YAAY;YACrB,CAAC;YACD0E,GAAG,EAAE,SAAAA,CAAUsB,eAAe,EAAE;cAC9BjO,KAAK,CAAC,mEAAmE,GAAG,mEAAmE,GAAG,uDAAuD,CAAC;cAE1MiI,YAAY,GAAGgG,eAAe,CAAC,CAAC;cAChC;;cAEA/L,MAAM,CAACgB,cAAc,CAAC6K,QAAQ,EAAE,cAAc,EAAE;gBAC9CtG,UAAU,EAAE;cACd,CAAC,CAAC;YACJ;UACF,CAAC;UACDuG,SAAS,EAAE;YACTnH,YAAY,EAAE,IAAI;YAClB1D,GAAG,EAAE,SAAAA,CAAA,EAAY;cACf,OAAO6K,SAAS;YAClB,CAAC;YACDrB,GAAG,EAAE,SAAAA,CAAUuB,YAAY,EAAE;cAC3BlO,KAAK,CAAC,gEAAgE,GAAG,mEAAmE,GAAG,uDAAuD,CAAC;cAEvMgO,SAAS,GAAGE,YAAY,CAAC,CAAC;cAC1B;;cAEAhM,MAAM,CAACgB,cAAc,CAAC6K,QAAQ,EAAE,WAAW,EAAE;gBAC3CtG,UAAU,EAAE;cACd,CAAC,CAAC;YACJ;UACF;QACF,CAAC,CAAC;MACJ;MAEA,OAAOsG,QAAQ;IACjB;IAEA,SAASI,UAAUA,CAAC9I,MAAM,EAAE;MAC1B;QACE,IAAIA,MAAM,IAAI,IAAI,IAAIA,MAAM,CAACH,QAAQ,KAAKzH,eAAe,EAAE;UACzDuC,KAAK,CAAC,8DAA8D,GAAG,mDAAmD,GAAG,wBAAwB,CAAC;QACxJ,CAAC,MAAM,IAAI,OAAOqF,MAAM,KAAK,UAAU,EAAE;UACvCrF,KAAK,CAAC,yDAAyD,EAAEqF,MAAM,KAAK,IAAI,GAAG,MAAM,GAAG,OAAOA,MAAM,CAAC;QAC5G,CAAC,MAAM;UACL,IAAIA,MAAM,CAAC1F,MAAM,KAAK,CAAC,IAAI0F,MAAM,CAAC1F,MAAM,KAAK,CAAC,EAAE;YAC9CK,KAAK,CAAC,8EAA8E,EAAEqF,MAAM,CAAC1F,MAAM,KAAK,CAAC,GAAG,0CAA0C,GAAG,6CAA6C,CAAC;UACzM;QACF;QAEA,IAAI0F,MAAM,IAAI,IAAI,EAAE;UAClB,IAAIA,MAAM,CAAC4C,YAAY,IAAI,IAAI,IAAI5C,MAAM,CAAC2I,SAAS,IAAI,IAAI,EAAE;YAC3DhO,KAAK,CAAC,wEAAwE,GAAG,8CAA8C,CAAC;UAClI;QACF;MACF;MAEA,IAAIoO,WAAW,GAAG;QAChBlJ,QAAQ,EAAE5H,sBAAsB;QAChC+H,MAAM,EAAEA;MACV,CAAC;MAED;QACE,IAAIgJ,OAAO;QACXnM,MAAM,CAACgB,cAAc,CAACkL,WAAW,EAAE,aAAa,EAAE;UAChD3G,UAAU,EAAE,KAAK;UACjBZ,YAAY,EAAE,IAAI;UAClB1D,GAAG,EAAE,SAAAA,CAAA,EAAY;YACf,OAAOkL,OAAO;UAChB,CAAC;UACD1B,GAAG,EAAE,SAAAA,CAAUpL,IAAI,EAAE;YACnB8M,OAAO,GAAG9M,IAAI,CAAC,CAAC;YAChB;YACA;YACA;YACA;YACA;YACA;;YAEA,IAAI,CAAC8D,MAAM,CAAC9D,IAAI,IAAI,CAAC8D,MAAM,CAAC/D,WAAW,EAAE;cACvC+D,MAAM,CAAC/D,WAAW,GAAGC,IAAI;YAC3B;UACF;QACF,CAAC,CAAC;MACJ;MAEA,OAAO6M,WAAW;IACpB;IAEA,IAAIE,sBAAsB;IAE1B;MACEA,sBAAsB,GAAGxR,MAAM,CAACC,GAAG,CAAC,wBAAwB,CAAC;IAC/D;IAEA,SAASwR,kBAAkBA,CAAClK,IAAI,EAAE;MAChC,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE;QAC1D,OAAO,IAAI;MACb,CAAC,CAAC;;MAGF,IAAIA,IAAI,KAAKpH,mBAAmB,IAAIoH,IAAI,KAAKlH,mBAAmB,IAAIkC,kBAAkB,IAAKgF,IAAI,KAAKnH,sBAAsB,IAAImH,IAAI,KAAK9G,mBAAmB,IAAI8G,IAAI,KAAK7G,wBAAwB,IAAI4B,kBAAkB,IAAKiF,IAAI,KAAK1G,oBAAoB,IAAIsB,cAAc,IAAKC,kBAAkB,IAAKC,uBAAuB,EAAG;QAC7T,OAAO,IAAI;MACb;MAEA,IAAI,OAAOkF,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,EAAE;QAC7C,IAAIA,IAAI,CAACa,QAAQ,KAAKxH,eAAe,IAAI2G,IAAI,CAACa,QAAQ,KAAKzH,eAAe,IAAI4G,IAAI,CAACa,QAAQ,KAAK9H,mBAAmB,IAAIiH,IAAI,CAACa,QAAQ,KAAK7H,kBAAkB,IAAIgH,IAAI,CAACa,QAAQ,KAAK5H,sBAAsB;QAAI;QAC3M;QACA;QACA;QACA+G,IAAI,CAACa,QAAQ,KAAKoJ,sBAAsB,IAAIjK,IAAI,CAACmK,WAAW,KAAKpL,SAAS,EAAE;UAC1E,OAAO,IAAI;QACb;MACF;MAEA,OAAO,KAAK;IACd;IAEA,SAASqL,IAAIA,CAACpK,IAAI,EAAEqK,OAAO,EAAE;MAC3B;QACE,IAAI,CAACH,kBAAkB,CAAClK,IAAI,CAAC,EAAE;UAC7BrE,KAAK,CAAC,wDAAwD,GAAG,cAAc,EAAEqE,IAAI,KAAK,IAAI,GAAG,MAAM,GAAG,OAAOA,IAAI,CAAC;QACxH;MACF;MAEA,IAAI+J,WAAW,GAAG;QAChBlJ,QAAQ,EAAEzH,eAAe;QACzB4G,IAAI,EAAEA,IAAI;QACVqK,OAAO,EAAEA,OAAO,KAAKtL,SAAS,GAAG,IAAI,GAAGsL;MAC1C,CAAC;MAED;QACE,IAAIL,OAAO;QACXnM,MAAM,CAACgB,cAAc,CAACkL,WAAW,EAAE,aAAa,EAAE;UAChD3G,UAAU,EAAE,KAAK;UACjBZ,YAAY,EAAE,IAAI;UAClB1D,GAAG,EAAE,SAAAA,CAAA,EAAY;YACf,OAAOkL,OAAO;UAChB,CAAC;UACD1B,GAAG,EAAE,SAAAA,CAAUpL,IAAI,EAAE;YACnB8M,OAAO,GAAG9M,IAAI,CAAC,CAAC;YAChB;YACA;YACA;YACA;YACA;YACA;;YAEA,IAAI,CAAC8C,IAAI,CAAC9C,IAAI,IAAI,CAAC8C,IAAI,CAAC/C,WAAW,EAAE;cACnC+C,IAAI,CAAC/C,WAAW,GAAGC,IAAI;YACzB;UACF;QACF,CAAC,CAAC;MACJ;MAEA,OAAO6M,WAAW;IACpB;IAEA,SAASO,iBAAiBA,CAAA,EAAG;MAC3B,IAAIC,UAAU,GAAG1Q,sBAAsB,CAACC,OAAO;MAE/C;QACE,IAAIyQ,UAAU,KAAK,IAAI,EAAE;UACvB5O,KAAK,CAAC,+GAA+G,GAAG,kCAAkC,GAAG,wFAAwF,GAAG,+CAA+C,GAAG,iEAAiE,GAAG,kGAAkG,CAAC;QACnd;MACF,CAAC,CAAC;MACF;MACA;;MAGA,OAAO4O,UAAU;IACnB;IACA,SAASC,UAAUA,CAACC,OAAO,EAAE;MAC3B,IAAIF,UAAU,GAAGD,iBAAiB,CAAC,CAAC;MAEpC;QACE;QACA,IAAIG,OAAO,CAAC1J,QAAQ,KAAKhC,SAAS,EAAE;UAClC,IAAI2L,WAAW,GAAGD,OAAO,CAAC1J,QAAQ,CAAC,CAAC;UACpC;;UAEA,IAAI2J,WAAW,CAAC3C,QAAQ,KAAK0C,OAAO,EAAE;YACpC9O,KAAK,CAAC,qFAAqF,GAAG,sFAAsF,CAAC;UACvL,CAAC,MAAM,IAAI+O,WAAW,CAAC5C,QAAQ,KAAK2C,OAAO,EAAE;YAC3C9O,KAAK,CAAC,yDAAyD,GAAG,mDAAmD,CAAC;UACxH;QACF;MACF;MAEA,OAAO4O,UAAU,CAACC,UAAU,CAACC,OAAO,CAAC;IACvC;IACA,SAASE,QAAQA,CAACC,YAAY,EAAE;MAC9B,IAAIL,UAAU,GAAGD,iBAAiB,CAAC,CAAC;MACpC,OAAOC,UAAU,CAACI,QAAQ,CAACC,YAAY,CAAC;IAC1C;IACA,SAASC,UAAUA,CAACC,OAAO,EAAEC,UAAU,EAAE1J,IAAI,EAAE;MAC7C,IAAIkJ,UAAU,GAAGD,iBAAiB,CAAC,CAAC;MACpC,OAAOC,UAAU,CAACM,UAAU,CAACC,OAAO,EAAEC,UAAU,EAAE1J,IAAI,CAAC;IACzD;IACA,SAAS2J,MAAMA,CAACC,YAAY,EAAE;MAC5B,IAAIV,UAAU,GAAGD,iBAAiB,CAAC,CAAC;MACpC,OAAOC,UAAU,CAACS,MAAM,CAACC,YAAY,CAAC;IACxC;IACA,SAASC,SAASA,CAACC,MAAM,EAAEC,IAAI,EAAE;MAC/B,IAAIb,UAAU,GAAGD,iBAAiB,CAAC,CAAC;MACpC,OAAOC,UAAU,CAACW,SAAS,CAACC,MAAM,EAAEC,IAAI,CAAC;IAC3C;IACA,SAASC,kBAAkBA,CAACF,MAAM,EAAEC,IAAI,EAAE;MACxC,IAAIb,UAAU,GAAGD,iBAAiB,CAAC,CAAC;MACpC,OAAOC,UAAU,CAACc,kBAAkB,CAACF,MAAM,EAAEC,IAAI,CAAC;IACpD;IACA,SAASE,eAAeA,CAACH,MAAM,EAAEC,IAAI,EAAE;MACrC,IAAIb,UAAU,GAAGD,iBAAiB,CAAC,CAAC;MACpC,OAAOC,UAAU,CAACe,eAAe,CAACH,MAAM,EAAEC,IAAI,CAAC;IACjD;IACA,SAASG,WAAWA,CAAChO,QAAQ,EAAE6N,IAAI,EAAE;MACnC,IAAIb,UAAU,GAAGD,iBAAiB,CAAC,CAAC;MACpC,OAAOC,UAAU,CAACgB,WAAW,CAAChO,QAAQ,EAAE6N,IAAI,CAAC;IAC/C;IACA,SAASI,OAAOA,CAACL,MAAM,EAAEC,IAAI,EAAE;MAC7B,IAAIb,UAAU,GAAGD,iBAAiB,CAAC,CAAC;MACpC,OAAOC,UAAU,CAACiB,OAAO,CAACL,MAAM,EAAEC,IAAI,CAAC;IACzC;IACA,SAASK,mBAAmBA,CAAC/J,GAAG,EAAEyJ,MAAM,EAAEC,IAAI,EAAE;MAC9C,IAAIb,UAAU,GAAGD,iBAAiB,CAAC,CAAC;MACpC,OAAOC,UAAU,CAACkB,mBAAmB,CAAC/J,GAAG,EAAEyJ,MAAM,EAAEC,IAAI,CAAC;IAC1D;IACA,SAASM,aAAaA,CAAC7L,KAAK,EAAE8L,WAAW,EAAE;MACzC;QACE,IAAIpB,UAAU,GAAGD,iBAAiB,CAAC,CAAC;QACpC,OAAOC,UAAU,CAACmB,aAAa,CAAC7L,KAAK,EAAE8L,WAAW,CAAC;MACrD;IACF;IACA,SAASC,aAAaA,CAAA,EAAG;MACvB,IAAIrB,UAAU,GAAGD,iBAAiB,CAAC,CAAC;MACpC,OAAOC,UAAU,CAACqB,aAAa,CAAC,CAAC;IACnC;IACA,SAASC,gBAAgBA,CAAChM,KAAK,EAAE;MAC/B,IAAI0K,UAAU,GAAGD,iBAAiB,CAAC,CAAC;MACpC,OAAOC,UAAU,CAACsB,gBAAgB,CAAChM,KAAK,CAAC;IAC3C;IACA,SAASiM,KAAKA,CAAA,EAAG;MACf,IAAIvB,UAAU,GAAGD,iBAAiB,CAAC,CAAC;MACpC,OAAOC,UAAU,CAACuB,KAAK,CAAC,CAAC;IAC3B;IACA,SAASC,oBAAoBA,CAACC,SAAS,EAAEC,WAAW,EAAEC,iBAAiB,EAAE;MACvE,IAAI3B,UAAU,GAAGD,iBAAiB,CAAC,CAAC;MACpC,OAAOC,UAAU,CAACwB,oBAAoB,CAACC,SAAS,EAAEC,WAAW,EAAEC,iBAAiB,CAAC;IACnF;;IAEA;IACA;IACA;IACA;IACA,IAAIC,aAAa,GAAG,CAAC;IACrB,IAAIC,OAAO;IACX,IAAIC,QAAQ;IACZ,IAAIC,QAAQ;IACZ,IAAIC,SAAS;IACb,IAAIC,SAAS;IACb,IAAIC,kBAAkB;IACtB,IAAIC,YAAY;IAEhB,SAASC,WAAWA,CAAA,EAAG,CAAC;IAExBA,WAAW,CAACC,kBAAkB,GAAG,IAAI;IACrC,SAASC,WAAWA,CAAA,EAAG;MACrB;QACE,IAAIV,aAAa,KAAK,CAAC,EAAE;UACvB;UACAC,OAAO,GAAG3P,OAAO,CAACqQ,GAAG;UACrBT,QAAQ,GAAG5P,OAAO,CAACmC,IAAI;UACvB0N,QAAQ,GAAG7P,OAAO,CAACvB,IAAI;UACvBqR,SAAS,GAAG9P,OAAO,CAACd,KAAK;UACzB6Q,SAAS,GAAG/P,OAAO,CAACsQ,KAAK;UACzBN,kBAAkB,GAAGhQ,OAAO,CAACuQ,cAAc;UAC3CN,YAAY,GAAGjQ,OAAO,CAACwQ,QAAQ,CAAC,CAAC;;UAEjC,IAAIhP,KAAK,GAAG;YACVuE,YAAY,EAAE,IAAI;YAClBY,UAAU,EAAE,IAAI;YAChBvD,KAAK,EAAE8M,WAAW;YAClBtJ,QAAQ,EAAE;UACZ,CAAC,CAAC,CAAC;;UAEHxF,MAAM,CAACwK,gBAAgB,CAAC5L,OAAO,EAAE;YAC/BmC,IAAI,EAAEX,KAAK;YACX6O,GAAG,EAAE7O,KAAK;YACV/C,IAAI,EAAE+C,KAAK;YACXtC,KAAK,EAAEsC,KAAK;YACZ8O,KAAK,EAAE9O,KAAK;YACZ+O,cAAc,EAAE/O,KAAK;YACrBgP,QAAQ,EAAEhP;UACZ,CAAC,CAAC;UACF;QACF;QAEAkO,aAAa,EAAE;MACjB;IACF;IACA,SAASe,YAAYA,CAAA,EAAG;MACtB;QACEf,aAAa,EAAE;QAEf,IAAIA,aAAa,KAAK,CAAC,EAAE;UACvB;UACA,IAAIlO,KAAK,GAAG;YACVuE,YAAY,EAAE,IAAI;YAClBY,UAAU,EAAE,IAAI;YAChBC,QAAQ,EAAE;UACZ,CAAC,CAAC,CAAC;;UAEHxF,MAAM,CAACwK,gBAAgB,CAAC5L,OAAO,EAAE;YAC/BqQ,GAAG,EAAElP,MAAM,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;cACrB4B,KAAK,EAAEuM;YACT,CAAC,CAAC;YACFxN,IAAI,EAAEhB,MAAM,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;cACtB4B,KAAK,EAAEwM;YACT,CAAC,CAAC;YACFnR,IAAI,EAAE0C,MAAM,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;cACtB4B,KAAK,EAAEyM;YACT,CAAC,CAAC;YACF3Q,KAAK,EAAEiC,MAAM,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;cACvB4B,KAAK,EAAE0M;YACT,CAAC,CAAC;YACFQ,KAAK,EAAEnP,MAAM,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;cACvB4B,KAAK,EAAE2M;YACT,CAAC,CAAC;YACFQ,cAAc,EAAEpP,MAAM,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;cAChC4B,KAAK,EAAE4M;YACT,CAAC,CAAC;YACFQ,QAAQ,EAAErP,MAAM,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;cAC1B4B,KAAK,EAAE6M;YACT,CAAC;UACH,CAAC,CAAC;UACF;QACF;QAEA,IAAIP,aAAa,GAAG,CAAC,EAAE;UACrBxQ,KAAK,CAAC,iCAAiC,GAAG,+CAA+C,CAAC;QAC5F;MACF;IACF;IAEA,IAAIwR,wBAAwB,GAAGlS,oBAAoB,CAACpB,sBAAsB;IAC1E,IAAIuT,MAAM;IACV,SAASC,6BAA6BA,CAACnQ,IAAI,EAAE6F,MAAM,EAAEuK,OAAO,EAAE;MAC5D;QACE,IAAIF,MAAM,KAAKrO,SAAS,EAAE;UACxB;UACA,IAAI;YACF,MAAMzG,KAAK,CAAC,CAAC;UACf,CAAC,CAAC,OAAOiJ,CAAC,EAAE;YACV,IAAIsD,KAAK,GAAGtD,CAAC,CAAC/G,KAAK,CAAC+S,IAAI,CAAC,CAAC,CAAC1I,KAAK,CAAC,cAAc,CAAC;YAChDuI,MAAM,GAAGvI,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE;UAClC;QACF,CAAC,CAAC;;QAGF,OAAO,IAAI,GAAGuI,MAAM,GAAGlQ,IAAI;MAC7B;IACF;IACA,IAAIsQ,OAAO,GAAG,KAAK;IACnB,IAAIC,mBAAmB;IAEvB;MACE,IAAIC,eAAe,GAAG,OAAOC,OAAO,KAAK,UAAU,GAAGA,OAAO,GAAGC,GAAG;MACnEH,mBAAmB,GAAG,IAAIC,eAAe,CAAC,CAAC;IAC7C;IAEA,SAASG,4BAA4BA,CAACC,EAAE,EAAEC,SAAS,EAAE;MACnD;MACA,IAAK,CAACD,EAAE,IAAIN,OAAO,EAAE;QACnB,OAAO,EAAE;MACX;MAEA;QACE,IAAIQ,KAAK,GAAGP,mBAAmB,CAAC3O,GAAG,CAACgP,EAAE,CAAC;QAEvC,IAAIE,KAAK,KAAKjP,SAAS,EAAE;UACvB,OAAOiP,KAAK;QACd;MACF;MAEA,IAAIC,OAAO;MACXT,OAAO,GAAG,IAAI;MACd,IAAIU,yBAAyB,GAAG5V,KAAK,CAAC6V,iBAAiB,CAAC,CAAC;;MAEzD7V,KAAK,CAAC6V,iBAAiB,GAAGpP,SAAS;MACnC,IAAIqP,kBAAkB;MAEtB;QACEA,kBAAkB,GAAGjB,wBAAwB,CAACrT,OAAO,CAAC,CAAC;QACvD;;QAEAqT,wBAAwB,CAACrT,OAAO,GAAG,IAAI;QACvC+S,WAAW,CAAC,CAAC;MACf;MAEA,IAAI;QACF;QACA,IAAIkB,SAAS,EAAE;UACb;UACA,IAAIM,IAAI,GAAG,SAAAA,CAAA,EAAY;YACrB,MAAM/V,KAAK,CAAC,CAAC;UACf,CAAC,CAAC,CAAC;;UAGHuF,MAAM,CAACgB,cAAc,CAACwP,IAAI,CAAC/R,SAAS,EAAE,OAAO,EAAE;YAC7CgM,GAAG,EAAE,SAAAA,CAAA,EAAY;cACf;cACA;cACA,MAAMhQ,KAAK,CAAC,CAAC;YACf;UACF,CAAC,CAAC;UAEF,IAAI,OAAOgW,OAAO,KAAK,QAAQ,IAAIA,OAAO,CAACP,SAAS,EAAE;YACpD;YACA;YACA,IAAI;cACFO,OAAO,CAACP,SAAS,CAACM,IAAI,EAAE,EAAE,CAAC;YAC7B,CAAC,CAAC,OAAO9M,CAAC,EAAE;cACV0M,OAAO,GAAG1M,CAAC;YACb;YAEA+M,OAAO,CAACP,SAAS,CAACD,EAAE,EAAE,EAAE,EAAEO,IAAI,CAAC;UACjC,CAAC,MAAM;YACL,IAAI;cACFA,IAAI,CAAC7R,IAAI,CAAC,CAAC;YACb,CAAC,CAAC,OAAO+E,CAAC,EAAE;cACV0M,OAAO,GAAG1M,CAAC;YACb;YAEAuM,EAAE,CAACtR,IAAI,CAAC6R,IAAI,CAAC/R,SAAS,CAAC;UACzB;QACF,CAAC,MAAM;UACL,IAAI;YACF,MAAMhE,KAAK,CAAC,CAAC;UACf,CAAC,CAAC,OAAOiJ,CAAC,EAAE;YACV0M,OAAO,GAAG1M,CAAC;UACb;UAEAuM,EAAE,CAAC,CAAC;QACN;MACF,CAAC,CAAC,OAAOS,MAAM,EAAE;QACf;QACA,IAAIA,MAAM,IAAIN,OAAO,IAAI,OAAOM,MAAM,CAAC/T,KAAK,KAAK,QAAQ,EAAE;UACzD;UACA;UACA,IAAIgU,WAAW,GAAGD,MAAM,CAAC/T,KAAK,CAACiU,KAAK,CAAC,IAAI,CAAC;UAC1C,IAAIC,YAAY,GAAGT,OAAO,CAACzT,KAAK,CAACiU,KAAK,CAAC,IAAI,CAAC;UAC5C,IAAIE,CAAC,GAAGH,WAAW,CAAClT,MAAM,GAAG,CAAC;UAC9B,IAAIwK,CAAC,GAAG4I,YAAY,CAACpT,MAAM,GAAG,CAAC;UAE/B,OAAOqT,CAAC,IAAI,CAAC,IAAI7I,CAAC,IAAI,CAAC,IAAI0I,WAAW,CAACG,CAAC,CAAC,KAAKD,YAAY,CAAC5I,CAAC,CAAC,EAAE;YAC7D;YACA;YACA;YACA;YACA;YACA;YACAA,CAAC,EAAE;UACL;UAEA,OAAO6I,CAAC,IAAI,CAAC,IAAI7I,CAAC,IAAI,CAAC,EAAE6I,CAAC,EAAE,EAAE7I,CAAC,EAAE,EAAE;YACjC;YACA;YACA,IAAI0I,WAAW,CAACG,CAAC,CAAC,KAAKD,YAAY,CAAC5I,CAAC,CAAC,EAAE;cACtC;cACA;cACA;cACA;cACA;cACA,IAAI6I,CAAC,KAAK,CAAC,IAAI7I,CAAC,KAAK,CAAC,EAAE;gBACtB,GAAG;kBACD6I,CAAC,EAAE;kBACH7I,CAAC,EAAE,CAAC,CAAC;kBACL;;kBAEA,IAAIA,CAAC,GAAG,CAAC,IAAI0I,WAAW,CAACG,CAAC,CAAC,KAAKD,YAAY,CAAC5I,CAAC,CAAC,EAAE;oBAC/C;oBACA,IAAI8I,MAAM,GAAG,IAAI,GAAGJ,WAAW,CAACG,CAAC,CAAC,CAAC/J,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC;oBAChE;oBACA;;oBAGA,IAAIkJ,EAAE,CAAC7Q,WAAW,IAAI2R,MAAM,CAACC,QAAQ,CAAC,aAAa,CAAC,EAAE;sBACpDD,MAAM,GAAGA,MAAM,CAAChK,OAAO,CAAC,aAAa,EAAEkJ,EAAE,CAAC7Q,WAAW,CAAC;oBACxD;oBAEA;sBACE,IAAI,OAAO6Q,EAAE,KAAK,UAAU,EAAE;wBAC5BL,mBAAmB,CAACnF,GAAG,CAACwF,EAAE,EAAEc,MAAM,CAAC;sBACrC;oBACF,CAAC,CAAC;;oBAGF,OAAOA,MAAM;kBACf;gBACF,CAAC,QAAQD,CAAC,IAAI,CAAC,IAAI7I,CAAC,IAAI,CAAC;cAC3B;cAEA;YACF;UACF;QACF;MACF,CAAC,SAAS;QACR0H,OAAO,GAAG,KAAK;QAEf;UACEL,wBAAwB,CAACrT,OAAO,GAAGsU,kBAAkB;UACrDlB,YAAY,CAAC,CAAC;QAChB;QAEA5U,KAAK,CAAC6V,iBAAiB,GAAGD,yBAAyB;MACrD,CAAC,CAAC;;MAGF,IAAIhR,IAAI,GAAG4Q,EAAE,GAAGA,EAAE,CAAC7Q,WAAW,IAAI6Q,EAAE,CAAC5Q,IAAI,GAAG,EAAE;MAC9C,IAAI4R,cAAc,GAAG5R,IAAI,GAAGmQ,6BAA6B,CAACnQ,IAAI,CAAC,GAAG,EAAE;MAEpE;QACE,IAAI,OAAO4Q,EAAE,KAAK,UAAU,EAAE;UAC5BL,mBAAmB,CAACnF,GAAG,CAACwF,EAAE,EAAEgB,cAAc,CAAC;QAC7C;MACF;MAEA,OAAOA,cAAc;IACvB;IACA,SAASC,8BAA8BA,CAACjB,EAAE,EAAE/K,MAAM,EAAEuK,OAAO,EAAE;MAC3D;QACE,OAAOO,4BAA4B,CAACC,EAAE,EAAE,KAAK,CAAC;MAChD;IACF;IAEA,SAASkB,eAAeA,CAAChR,SAAS,EAAE;MAClC,IAAI1B,SAAS,GAAG0B,SAAS,CAAC1B,SAAS;MACnC,OAAO,CAAC,EAAEA,SAAS,IAAIA,SAAS,CAAC+B,gBAAgB,CAAC;IACpD;IAEA,SAAS4Q,oCAAoCA,CAACjP,IAAI,EAAE+C,MAAM,EAAEuK,OAAO,EAAE;MAEnE,IAAItN,IAAI,IAAI,IAAI,EAAE;QAChB,OAAO,EAAE;MACX;MAEA,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE;QAC9B;UACE,OAAO6N,4BAA4B,CAAC7N,IAAI,EAAEgP,eAAe,CAAChP,IAAI,CAAC,CAAC;QAClE;MACF;MAEA,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;QAC5B,OAAOqN,6BAA6B,CAACrN,IAAI,CAAC;MAC5C;MAEA,QAAQA,IAAI;QACV,KAAK9G,mBAAmB;UACtB,OAAOmU,6BAA6B,CAAC,UAAU,CAAC;QAElD,KAAKlU,wBAAwB;UAC3B,OAAOkU,6BAA6B,CAAC,cAAc,CAAC;MACxD;MAEA,IAAI,OAAOrN,IAAI,KAAK,QAAQ,EAAE;QAC5B,QAAQA,IAAI,CAACa,QAAQ;UACnB,KAAK5H,sBAAsB;YACzB,OAAO8V,8BAA8B,CAAC/O,IAAI,CAACgB,MAAM,CAAC;UAEpD,KAAK5H,eAAe;YAClB;YACA,OAAO6V,oCAAoC,CAACjP,IAAI,CAACA,IAAI,EAAE+C,MAAM,EAAEuK,OAAO,CAAC;UAEzE,KAAKjU,eAAe;YAClB;cACE,IAAI6H,aAAa,GAAGlB,IAAI;cACxB,IAAImB,OAAO,GAAGD,aAAa,CAACE,QAAQ;cACpC,IAAIC,IAAI,GAAGH,aAAa,CAACI,KAAK;cAE9B,IAAI;gBACF;gBACA,OAAO2N,oCAAoC,CAAC5N,IAAI,CAACF,OAAO,CAAC,EAAE4B,MAAM,EAAEuK,OAAO,CAAC;cAC7E,CAAC,CAAC,OAAO/L,CAAC,EAAE,CAAC;YACf;QACJ;MACF;MAEA,OAAO,EAAE;IACX;IAEA,IAAI2N,kBAAkB,GAAG,CAAC,CAAC;IAC3B,IAAIC,wBAAwB,GAAGlU,oBAAoB,CAACZ,sBAAsB;IAE1E,SAAS+U,6BAA6BA,CAACnM,OAAO,EAAE;MAC9C;QACE,IAAIA,OAAO,EAAE;UACX,IAAID,KAAK,GAAGC,OAAO,CAACC,MAAM;UAC1B,IAAI1I,KAAK,GAAGyU,oCAAoC,CAAChM,OAAO,CAACjD,IAAI,EAAEiD,OAAO,CAACiB,OAAO,EAAElB,KAAK,GAAGA,KAAK,CAAChD,IAAI,GAAG,IAAI,CAAC;UAC1GmP,wBAAwB,CAAC5U,kBAAkB,CAACC,KAAK,CAAC;QACpD,CAAC,MAAM;UACL2U,wBAAwB,CAAC5U,kBAAkB,CAAC,IAAI,CAAC;QACnD;MACF;IACF;IAEA,SAAS8U,cAAcA,CAACC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAExS,aAAa,EAAEiG,OAAO,EAAE;MAC3E;QACE;QACA,IAAIwM,GAAG,GAAGpT,QAAQ,CAACG,IAAI,CAACkT,IAAI,CAACzQ,cAAc,CAAC;QAE5C,KAAK,IAAI0Q,YAAY,IAAIL,SAAS,EAAE;UAClC,IAAIG,GAAG,CAACH,SAAS,EAAEK,YAAY,CAAC,EAAE;YAChC,IAAIC,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC;YACtB;YACA;;YAEA,IAAI;cACF;cACA;cACA,IAAI,OAAON,SAAS,CAACK,YAAY,CAAC,KAAK,UAAU,EAAE;gBACjD;gBACA,IAAIE,GAAG,GAAGvX,KAAK,CAAC,CAAC0E,aAAa,IAAI,aAAa,IAAI,IAAI,GAAGwS,QAAQ,GAAG,SAAS,GAAGG,YAAY,GAAG,gBAAgB,GAAG,8EAA8E,GAAG,OAAOL,SAAS,CAACK,YAAY,CAAC,GAAG,IAAI,GAAG,+FAA+F,CAAC;gBAC5UE,GAAG,CAAC3S,IAAI,GAAG,qBAAqB;gBAChC,MAAM2S,GAAG;cACX;cAEAD,OAAO,GAAGN,SAAS,CAACK,YAAY,CAAC,CAACJ,MAAM,EAAEI,YAAY,EAAE3S,aAAa,EAAEwS,QAAQ,EAAE,IAAI,EAAE,8CAA8C,CAAC;YACxI,CAAC,CAAC,OAAOM,EAAE,EAAE;cACXF,OAAO,GAAGE,EAAE;YACd;YAEA,IAAIF,OAAO,IAAI,EAAEA,OAAO,YAAYtX,KAAK,CAAC,EAAE;cAC1C8W,6BAA6B,CAACnM,OAAO,CAAC;cAEtCtH,KAAK,CAAC,8BAA8B,GAAG,qCAAqC,GAAG,+DAA+D,GAAG,iEAAiE,GAAG,gEAAgE,GAAG,iCAAiC,EAAEqB,aAAa,IAAI,aAAa,EAAEwS,QAAQ,EAAEG,YAAY,EAAE,OAAOC,OAAO,CAAC;cAElYR,6BAA6B,CAAC,IAAI,CAAC;YACrC;YAEA,IAAIQ,OAAO,YAAYtX,KAAK,IAAI,EAAEsX,OAAO,CAACG,OAAO,IAAIb,kBAAkB,CAAC,EAAE;cACxE;cACA;cACAA,kBAAkB,CAACU,OAAO,CAACG,OAAO,CAAC,GAAG,IAAI;cAC1CX,6BAA6B,CAACnM,OAAO,CAAC;cAEtCtH,KAAK,CAAC,oBAAoB,EAAE6T,QAAQ,EAAEI,OAAO,CAACG,OAAO,CAAC;cAEtDX,6BAA6B,CAAC,IAAI,CAAC;YACrC;UACF;QACF;MACF;IACF;IAEA,SAASY,+BAA+BA,CAAC/M,OAAO,EAAE;MAChD;QACE,IAAIA,OAAO,EAAE;UACX,IAAID,KAAK,GAAGC,OAAO,CAACC,MAAM;UAC1B,IAAI1I,KAAK,GAAGyU,oCAAoC,CAAChM,OAAO,CAACjD,IAAI,EAAEiD,OAAO,CAACiB,OAAO,EAAElB,KAAK,GAAGA,KAAK,CAAChD,IAAI,GAAG,IAAI,CAAC;UAC1GzF,kBAAkB,CAACC,KAAK,CAAC;QAC3B,CAAC,MAAM;UACLD,kBAAkB,CAAC,IAAI,CAAC;QAC1B;MACF;IACF;IAEA,IAAI0V,6BAA6B;IAEjC;MACEA,6BAA6B,GAAG,KAAK;IACvC;IAEA,SAASC,2BAA2BA,CAAA,EAAG;MACrC,IAAI9V,iBAAiB,CAACN,OAAO,EAAE;QAC7B,IAAIoD,IAAI,GAAGyD,wBAAwB,CAACvG,iBAAiB,CAACN,OAAO,CAACkG,IAAI,CAAC;QAEnE,IAAI9C,IAAI,EAAE;UACR,OAAO,kCAAkC,GAAGA,IAAI,GAAG,IAAI;QACzD;MACF;MAEA,OAAO,EAAE;IACX;IAEA,SAASiT,0BAA0BA,CAACpN,MAAM,EAAE;MAC1C,IAAIA,MAAM,KAAKhE,SAAS,EAAE;QACxB,IAAIqR,QAAQ,GAAGrN,MAAM,CAACqN,QAAQ,CAACxL,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;QACvD,IAAIyL,UAAU,GAAGtN,MAAM,CAACsN,UAAU;QAClC,OAAO,yBAAyB,GAAGD,QAAQ,GAAG,GAAG,GAAGC,UAAU,GAAG,GAAG;MACtE;MAEA,OAAO,EAAE;IACX;IAEA,SAASC,kCAAkCA,CAACC,YAAY,EAAE;MACxD,IAAIA,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAKxR,SAAS,EAAE;QACvD,OAAOoR,0BAA0B,CAACI,YAAY,CAAC3O,QAAQ,CAAC;MAC1D;MAEA,OAAO,EAAE;IACX;IACA;AACA;AACA;AACA;AACA;;IAGA,IAAI4O,qBAAqB,GAAG,CAAC,CAAC;IAE9B,SAASC,4BAA4BA,CAACC,UAAU,EAAE;MAChD,IAAI9R,IAAI,GAAGsR,2BAA2B,CAAC,CAAC;MAExC,IAAI,CAACtR,IAAI,EAAE;QACT,IAAI+R,UAAU,GAAG,OAAOD,UAAU,KAAK,QAAQ,GAAGA,UAAU,GAAGA,UAAU,CAACzT,WAAW,IAAIyT,UAAU,CAACxT,IAAI;QAExG,IAAIyT,UAAU,EAAE;UACd/R,IAAI,GAAG,6CAA6C,GAAG+R,UAAU,GAAG,IAAI;QAC1E;MACF;MAEA,OAAO/R,IAAI;IACb;IACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAGA,SAASgS,mBAAmBA,CAAC3N,OAAO,EAAEyN,UAAU,EAAE;MAChD,IAAI,CAACzN,OAAO,CAACE,MAAM,IAAIF,OAAO,CAACE,MAAM,CAAC0N,SAAS,IAAI5N,OAAO,CAACxB,GAAG,IAAI,IAAI,EAAE;QACtE;MACF;MAEAwB,OAAO,CAACE,MAAM,CAAC0N,SAAS,GAAG,IAAI;MAC/B,IAAIC,yBAAyB,GAAGL,4BAA4B,CAACC,UAAU,CAAC;MAExE,IAAIF,qBAAqB,CAACM,yBAAyB,CAAC,EAAE;QACpD;MACF;MAEAN,qBAAqB,CAACM,yBAAyB,CAAC,GAAG,IAAI,CAAC,CAAC;MACzD;MACA;;MAEA,IAAIC,UAAU,GAAG,EAAE;MAEnB,IAAI9N,OAAO,IAAIA,OAAO,CAACC,MAAM,IAAID,OAAO,CAACC,MAAM,KAAK9I,iBAAiB,CAACN,OAAO,EAAE;QAC7E;QACAiX,UAAU,GAAG,8BAA8B,GAAGpQ,wBAAwB,CAACsC,OAAO,CAACC,MAAM,CAAClD,IAAI,CAAC,GAAG,GAAG;MACnG;MAEA;QACEgQ,+BAA+B,CAAC/M,OAAO,CAAC;QAExCtH,KAAK,CAAC,uDAAuD,GAAG,sEAAsE,EAAEmV,yBAAyB,EAAEC,UAAU,CAAC;QAE9Kf,+BAA+B,CAAC,IAAI,CAAC;MACvC;IACF;IACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAGA,SAASgB,iBAAiBA,CAACC,IAAI,EAAEP,UAAU,EAAE;MAC3C,IAAI,OAAOO,IAAI,KAAK,QAAQ,EAAE;QAC5B;MACF;MAEA,IAAIvR,OAAO,CAACuR,IAAI,CAAC,EAAE;QACjB,KAAK,IAAItN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsN,IAAI,CAAC3V,MAAM,EAAEqI,CAAC,EAAE,EAAE;UACpC,IAAIqC,KAAK,GAAGiL,IAAI,CAACtN,CAAC,CAAC;UAEnB,IAAIS,cAAc,CAAC4B,KAAK,CAAC,EAAE;YACzB4K,mBAAmB,CAAC5K,KAAK,EAAE0K,UAAU,CAAC;UACxC;QACF;MACF,CAAC,MAAM,IAAItM,cAAc,CAAC6M,IAAI,CAAC,EAAE;QAC/B;QACA,IAAIA,IAAI,CAAC9N,MAAM,EAAE;UACf8N,IAAI,CAAC9N,MAAM,CAAC0N,SAAS,GAAG,IAAI;QAC9B;MACF,CAAC,MAAM,IAAII,IAAI,EAAE;QACf,IAAI7K,UAAU,GAAG1M,aAAa,CAACuX,IAAI,CAAC;QAEpC,IAAI,OAAO7K,UAAU,KAAK,UAAU,EAAE;UACpC;UACA;UACA,IAAIA,UAAU,KAAK6K,IAAI,CAAC3K,OAAO,EAAE;YAC/B,IAAI9M,QAAQ,GAAG4M,UAAU,CAAC5J,IAAI,CAACyU,IAAI,CAAC;YACpC,IAAI1K,IAAI;YAER,OAAO,CAAC,CAACA,IAAI,GAAG/M,QAAQ,CAACiN,IAAI,CAAC,CAAC,EAAEC,IAAI,EAAE;cACrC,IAAItC,cAAc,CAACmC,IAAI,CAAC1G,KAAK,CAAC,EAAE;gBAC9B+Q,mBAAmB,CAACrK,IAAI,CAAC1G,KAAK,EAAE6Q,UAAU,CAAC;cAC7C;YACF;UACF;QACF;MACF;IACF;IACA;AACA;AACA;AACA;AACA;AACA;;IAGA,SAASQ,iBAAiBA,CAACjO,OAAO,EAAE;MAClC;QACE,IAAIjD,IAAI,GAAGiD,OAAO,CAACjD,IAAI;QAEvB,IAAIA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAKjB,SAAS,IAAI,OAAOiB,IAAI,KAAK,QAAQ,EAAE;UACnE;QACF;QAEA,IAAI2J,SAAS;QAEb,IAAI,OAAO3J,IAAI,KAAK,UAAU,EAAE;UAC9B2J,SAAS,GAAG3J,IAAI,CAAC2J,SAAS;QAC5B,CAAC,MAAM,IAAI,OAAO3J,IAAI,KAAK,QAAQ,KAAKA,IAAI,CAACa,QAAQ,KAAK5H,sBAAsB;QAAI;QACpF;QACA+G,IAAI,CAACa,QAAQ,KAAKzH,eAAe,CAAC,EAAE;UAClCuQ,SAAS,GAAG3J,IAAI,CAAC2J,SAAS;QAC5B,CAAC,MAAM;UACL;QACF;QAEA,IAAIA,SAAS,EAAE;UACb;UACA,IAAIzM,IAAI,GAAGyD,wBAAwB,CAACX,IAAI,CAAC;UACzCqP,cAAc,CAAC1F,SAAS,EAAE1G,OAAO,CAAChF,KAAK,EAAE,MAAM,EAAEf,IAAI,EAAE+F,OAAO,CAAC;QACjE,CAAC,MAAM,IAAIjD,IAAI,CAACmR,SAAS,KAAKpS,SAAS,IAAI,CAACkR,6BAA6B,EAAE;UACzEA,6BAA6B,GAAG,IAAI,CAAC,CAAC;;UAEtC,IAAImB,KAAK,GAAGzQ,wBAAwB,CAACX,IAAI,CAAC;UAE1CrE,KAAK,CAAC,qGAAqG,EAAEyV,KAAK,IAAI,SAAS,CAAC;QAClI;QAEA,IAAI,OAAOpR,IAAI,CAACqR,eAAe,KAAK,UAAU,IAAI,CAACrR,IAAI,CAACqR,eAAe,CAACC,oBAAoB,EAAE;UAC5F3V,KAAK,CAAC,4DAA4D,GAAG,kEAAkE,CAAC;QAC1I;MACF;IACF;IACA;AACA;AACA;AACA;;IAGA,SAAS4V,qBAAqBA,CAACC,QAAQ,EAAE;MACvC;QACE,IAAI5K,IAAI,GAAG/I,MAAM,CAAC+I,IAAI,CAAC4K,QAAQ,CAACvT,KAAK,CAAC;QAEtC,KAAK,IAAI0F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiD,IAAI,CAACtL,MAAM,EAAEqI,CAAC,EAAE,EAAE;UACpC,IAAIlC,GAAG,GAAGmF,IAAI,CAACjD,CAAC,CAAC;UAEjB,IAAIlC,GAAG,KAAK,UAAU,IAAIA,GAAG,KAAK,KAAK,EAAE;YACvCuO,+BAA+B,CAACwB,QAAQ,CAAC;YAEzC7V,KAAK,CAAC,kDAAkD,GAAG,0DAA0D,EAAE8F,GAAG,CAAC;YAE3HuO,+BAA+B,CAAC,IAAI,CAAC;YACrC;UACF;QACF;QAEA,IAAIwB,QAAQ,CAAC9P,GAAG,KAAK,IAAI,EAAE;UACzBsO,+BAA+B,CAACwB,QAAQ,CAAC;UAEzC7V,KAAK,CAAC,uDAAuD,CAAC;UAE9DqU,+BAA+B,CAAC,IAAI,CAAC;QACvC;MACF;IACF;IACA,SAASyB,2BAA2BA,CAACzR,IAAI,EAAE/B,KAAK,EAAEsF,QAAQ,EAAE;MAC1D,IAAImO,SAAS,GAAGxH,kBAAkB,CAAClK,IAAI,CAAC,CAAC,CAAC;MAC1C;;MAEA,IAAI,CAAC0R,SAAS,EAAE;QACd,IAAI9S,IAAI,GAAG,EAAE;QAEb,IAAIoB,IAAI,KAAKjB,SAAS,IAAI,OAAOiB,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,IAAInC,MAAM,CAAC+I,IAAI,CAAC5G,IAAI,CAAC,CAAC1E,MAAM,KAAK,CAAC,EAAE;UACrGsD,IAAI,IAAI,4DAA4D,GAAG,wEAAwE;QACjJ;QAEA,IAAI+S,UAAU,GAAGrB,kCAAkC,CAACrS,KAAK,CAAC;QAE1D,IAAI0T,UAAU,EAAE;UACd/S,IAAI,IAAI+S,UAAU;QACpB,CAAC,MAAM;UACL/S,IAAI,IAAIsR,2BAA2B,CAAC,CAAC;QACvC;QAEA,IAAI0B,UAAU;QAEd,IAAI5R,IAAI,KAAK,IAAI,EAAE;UACjB4R,UAAU,GAAG,MAAM;QACrB,CAAC,MAAM,IAAIlS,OAAO,CAACM,IAAI,CAAC,EAAE;UACxB4R,UAAU,GAAG,OAAO;QACtB,CAAC,MAAM,IAAI5R,IAAI,KAAKjB,SAAS,IAAIiB,IAAI,CAACa,QAAQ,KAAKrI,kBAAkB,EAAE;UACrEoZ,UAAU,GAAG,GAAG,IAAIjR,wBAAwB,CAACX,IAAI,CAACA,IAAI,CAAC,IAAI,SAAS,CAAC,GAAG,KAAK;UAC7EpB,IAAI,GAAG,oEAAoE;QAC7E,CAAC,MAAM;UACLgT,UAAU,GAAG,OAAO5R,IAAI;QAC1B;QAEA;UACErE,KAAK,CAAC,iEAAiE,GAAG,0DAA0D,GAAG,4BAA4B,EAAEiW,UAAU,EAAEhT,IAAI,CAAC;QACxL;MACF;MAEA,IAAIqE,OAAO,GAAGK,aAAa,CAAC/G,KAAK,CAAC,IAAI,EAAElB,SAAS,CAAC,CAAC,CAAC;MACpD;;MAEA,IAAI4H,OAAO,IAAI,IAAI,EAAE;QACnB,OAAOA,OAAO;MAChB,CAAC,CAAC;MACF;MACA;MACA;MACA;;MAGA,IAAIyO,SAAS,EAAE;QACb,KAAK,IAAI/N,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtI,SAAS,CAACC,MAAM,EAAEqI,CAAC,EAAE,EAAE;UACzCqN,iBAAiB,CAAC3V,SAAS,CAACsI,CAAC,CAAC,EAAE3D,IAAI,CAAC;QACvC;MACF;MAEA,IAAIA,IAAI,KAAKpH,mBAAmB,EAAE;QAChC2Y,qBAAqB,CAACtO,OAAO,CAAC;MAChC,CAAC,MAAM;QACLiO,iBAAiB,CAACjO,OAAO,CAAC;MAC5B;MAEA,OAAOA,OAAO;IAChB;IACA,IAAI4O,mCAAmC,GAAG,KAAK;IAC/C,SAASC,2BAA2BA,CAAC9R,IAAI,EAAE;MACzC,IAAI+R,gBAAgB,GAAGN,2BAA2B,CAAC/B,IAAI,CAAC,IAAI,EAAE1P,IAAI,CAAC;MACnE+R,gBAAgB,CAAC/R,IAAI,GAAGA,IAAI;MAE5B;QACE,IAAI,CAAC6R,mCAAmC,EAAE;UACxCA,mCAAmC,GAAG,IAAI;UAE1C3W,IAAI,CAAC,6DAA6D,GAAG,6CAA6C,GAAG,gDAAgD,CAAC;QACxK,CAAC,CAAC;;QAGF2C,MAAM,CAACgB,cAAc,CAACkT,gBAAgB,EAAE,MAAM,EAAE;UAC9C3O,UAAU,EAAE,KAAK;UACjBtE,GAAG,EAAE,SAAAA,CAAA,EAAY;YACf5D,IAAI,CAAC,wDAAwD,GAAG,qCAAqC,CAAC;YAEtG2C,MAAM,CAACgB,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE;cAClCgB,KAAK,EAAEG;YACT,CAAC,CAAC;YACF,OAAOA,IAAI;UACb;QACF,CAAC,CAAC;MACJ;MAEA,OAAO+R,gBAAgB;IACzB;IACA,SAASC,0BAA0BA,CAAC/O,OAAO,EAAEhF,KAAK,EAAEsF,QAAQ,EAAE;MAC5D,IAAIS,UAAU,GAAGG,YAAY,CAAC5H,KAAK,CAAC,IAAI,EAAElB,SAAS,CAAC;MAEpD,KAAK,IAAIsI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtI,SAAS,CAACC,MAAM,EAAEqI,CAAC,EAAE,EAAE;QACzCqN,iBAAiB,CAAC3V,SAAS,CAACsI,CAAC,CAAC,EAAEK,UAAU,CAAChE,IAAI,CAAC;MAClD;MAEAkR,iBAAiB,CAAClN,UAAU,CAAC;MAC7B,OAAOA,UAAU;IACnB;IAEA,SAASiO,eAAeA,CAACC,KAAK,EAAEC,OAAO,EAAE;MACvC,IAAIC,cAAc,GAAGrY,uBAAuB,CAACC,UAAU;MACvDD,uBAAuB,CAACC,UAAU,GAAG,CAAC,CAAC;MACvC,IAAIqY,iBAAiB,GAAGtY,uBAAuB,CAACC,UAAU;MAE1D;QACED,uBAAuB,CAACC,UAAU,CAACsY,cAAc,GAAG,IAAIC,GAAG,CAAC,CAAC;MAC/D;MAEA,IAAI;QACFL,KAAK,CAAC,CAAC;MACT,CAAC,SAAS;QACRnY,uBAAuB,CAACC,UAAU,GAAGoY,cAAc;QAEnD;UACE,IAAIA,cAAc,KAAK,IAAI,IAAIC,iBAAiB,CAACC,cAAc,EAAE;YAC/D,IAAIE,kBAAkB,GAAGH,iBAAiB,CAACC,cAAc,CAACG,IAAI;YAE9D,IAAID,kBAAkB,GAAG,EAAE,EAAE;cAC3BtX,IAAI,CAAC,6DAA6D,GAAG,mFAAmF,GAAG,yDAAyD,CAAC;YACvN;YAEAmX,iBAAiB,CAACC,cAAc,CAACI,KAAK,CAAC,CAAC;UAC1C;QACF;MACF;IACF;IAEA,IAAIC,0BAA0B,GAAG,KAAK;IACtC,IAAIC,eAAe,GAAG,IAAI;IAC1B,SAASC,WAAWA,CAACC,IAAI,EAAE;MACzB,IAAIF,eAAe,KAAK,IAAI,EAAE;QAC5B,IAAI;UACF;UACA;UACA,IAAIG,aAAa,GAAG,CAAC,SAAS,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,EAAEC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;UAC3D,IAAIC,WAAW,GAAGC,MAAM,IAAIA,MAAM,CAACL,aAAa,CAAC,CAAC,CAAC;UACnD;;UAEAH,eAAe,GAAGO,WAAW,CAAC3W,IAAI,CAAC4W,MAAM,EAAE,QAAQ,CAAC,CAACC,YAAY;QACnE,CAAC,CAAC,OAAOC,IAAI,EAAE;UACb;UACA;UACA;UACAV,eAAe,GAAG,SAAAA,CAAUrV,QAAQ,EAAE;YACpC;cACE,IAAIoV,0BAA0B,KAAK,KAAK,EAAE;gBACxCA,0BAA0B,GAAG,IAAI;gBAEjC,IAAI,OAAOY,cAAc,KAAK,WAAW,EAAE;kBACzC5X,KAAK,CAAC,8DAA8D,GAAG,+DAA+D,GAAG,mEAAmE,GAAG,gCAAgC,CAAC;gBAClP;cACF;YACF;YAEA,IAAI6X,OAAO,GAAG,IAAID,cAAc,CAAC,CAAC;YAClCC,OAAO,CAACC,KAAK,CAACC,SAAS,GAAGnW,QAAQ;YAClCiW,OAAO,CAACG,KAAK,CAACC,WAAW,CAAC7U,SAAS,CAAC;UACtC,CAAC;QACH;MACF;MAEA,OAAO6T,eAAe,CAACE,IAAI,CAAC;IAC9B;IAEA,IAAIe,aAAa,GAAG,CAAC;IACrB,IAAIC,iBAAiB,GAAG,KAAK;IAC7B,SAASC,GAAGA,CAACxW,QAAQ,EAAE;MACrB;QACE;QACA;QACA,IAAIyW,iBAAiB,GAAGH,aAAa;QACrCA,aAAa,EAAE;QAEf,IAAI5Z,oBAAoB,CAACH,OAAO,KAAK,IAAI,EAAE;UACzC;UACA;UACAG,oBAAoB,CAACH,OAAO,GAAG,EAAE;QACnC;QAEA,IAAIma,oBAAoB,GAAGha,oBAAoB,CAACC,gBAAgB;QAChE,IAAI8M,MAAM;QAEV,IAAI;UACF;UACA;UACA;UACA;UACA/M,oBAAoB,CAACC,gBAAgB,GAAG,IAAI;UAC5C8M,MAAM,GAAGzJ,QAAQ,CAAC,CAAC,CAAC,CAAC;UACrB;UACA;;UAEA,IAAI,CAAC0W,oBAAoB,IAAIha,oBAAoB,CAACE,uBAAuB,EAAE;YACzE,IAAI+Z,KAAK,GAAGja,oBAAoB,CAACH,OAAO;YAExC,IAAIoa,KAAK,KAAK,IAAI,EAAE;cAClBja,oBAAoB,CAACE,uBAAuB,GAAG,KAAK;cACpDga,aAAa,CAACD,KAAK,CAAC;YACtB;UACF;QACF,CAAC,CAAC,OAAOvY,KAAK,EAAE;UACdyY,WAAW,CAACJ,iBAAiB,CAAC;UAC9B,MAAMrY,KAAK;QACb,CAAC,SAAS;UACR1B,oBAAoB,CAACC,gBAAgB,GAAG+Z,oBAAoB;QAC9D;QAEA,IAAIjN,MAAM,KAAK,IAAI,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAI,OAAOA,MAAM,CAACmC,IAAI,KAAK,UAAU,EAAE;UACtF,IAAIkL,cAAc,GAAGrN,MAAM,CAAC,CAAC;UAC7B;;UAEA,IAAIsN,UAAU,GAAG,KAAK;UACtB,IAAIpL,QAAQ,GAAG;YACbC,IAAI,EAAE,SAAAA,CAAUoL,OAAO,EAAEC,MAAM,EAAE;cAC/BF,UAAU,GAAG,IAAI;cACjBD,cAAc,CAAClL,IAAI,CAAC,UAAUsL,WAAW,EAAE;gBACzCL,WAAW,CAACJ,iBAAiB,CAAC;gBAE9B,IAAIH,aAAa,KAAK,CAAC,EAAE;kBACvB;kBACA;kBACAa,4BAA4B,CAACD,WAAW,EAAEF,OAAO,EAAEC,MAAM,CAAC;gBAC5D,CAAC,MAAM;kBACLD,OAAO,CAACE,WAAW,CAAC;gBACtB;cACF,CAAC,EAAE,UAAU9Y,KAAK,EAAE;gBAClB;gBACAyY,WAAW,CAACJ,iBAAiB,CAAC;gBAC9BQ,MAAM,CAAC7Y,KAAK,CAAC;cACf,CAAC,CAAC;YACJ;UACF,CAAC;UAED;YACE,IAAI,CAACmY,iBAAiB,IAAI,OAAOa,OAAO,KAAK,WAAW,EAAE;cACxD;cACAA,OAAO,CAACJ,OAAO,CAAC,CAAC,CAACpL,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAACA,IAAI,CAAC,YAAY;gBACtD,IAAI,CAACmL,UAAU,EAAE;kBACfR,iBAAiB,GAAG,IAAI;kBAExBnY,KAAK,CAAC,iDAAiD,GAAG,mDAAmD,GAAG,mDAAmD,GAAG,UAAU,GAAG,0CAA0C,CAAC;gBAChO;cACF,CAAC,CAAC;YACJ;UACF;UAEA,OAAOuN,QAAQ;QACjB,CAAC,MAAM;UACL,IAAIuL,WAAW,GAAGzN,MAAM,CAAC,CAAC;UAC1B;;UAEAoN,WAAW,CAACJ,iBAAiB,CAAC;UAE9B,IAAIH,aAAa,KAAK,CAAC,EAAE;YACvB;YACA,IAAIe,MAAM,GAAG3a,oBAAoB,CAACH,OAAO;YAEzC,IAAI8a,MAAM,KAAK,IAAI,EAAE;cACnBT,aAAa,CAACS,MAAM,CAAC;cACrB3a,oBAAoB,CAACH,OAAO,GAAG,IAAI;YACrC,CAAC,CAAC;YACF;;YAGA,IAAI+a,SAAS,GAAG;cACd1L,IAAI,EAAE,SAAAA,CAAUoL,OAAO,EAAEC,MAAM,EAAE;gBAC/B;gBACA;gBACA;gBACA,IAAIva,oBAAoB,CAACH,OAAO,KAAK,IAAI,EAAE;kBACzC;kBACAG,oBAAoB,CAACH,OAAO,GAAG,EAAE;kBACjC4a,4BAA4B,CAACD,WAAW,EAAEF,OAAO,EAAEC,MAAM,CAAC;gBAC5D,CAAC,MAAM;kBACLD,OAAO,CAACE,WAAW,CAAC;gBACtB;cACF;YACF,CAAC;YACD,OAAOI,SAAS;UAClB,CAAC,MAAM;YACL;YACA;YACA,IAAIC,UAAU,GAAG;cACf3L,IAAI,EAAE,SAAAA,CAAUoL,OAAO,EAAEC,MAAM,EAAE;gBAC/BD,OAAO,CAACE,WAAW,CAAC;cACtB;YACF,CAAC;YACD,OAAOK,UAAU;UACnB;QACF;MACF;IACF;IAEA,SAASV,WAAWA,CAACJ,iBAAiB,EAAE;MACtC;QACE,IAAIA,iBAAiB,KAAKH,aAAa,GAAG,CAAC,EAAE;UAC3ClY,KAAK,CAAC,mEAAmE,GAAG,iEAAiE,CAAC;QAChJ;QAEAkY,aAAa,GAAGG,iBAAiB;MACnC;IACF;IAEA,SAASU,4BAA4BA,CAACD,WAAW,EAAEF,OAAO,EAAEC,MAAM,EAAE;MAClE;QACE,IAAIN,KAAK,GAAGja,oBAAoB,CAACH,OAAO;QAExC,IAAIoa,KAAK,KAAK,IAAI,EAAE;UAClB,IAAI;YACFC,aAAa,CAACD,KAAK,CAAC;YACpBrB,WAAW,CAAC,YAAY;cACtB,IAAIqB,KAAK,CAAC5Y,MAAM,KAAK,CAAC,EAAE;gBACtB;gBACArB,oBAAoB,CAACH,OAAO,GAAG,IAAI;gBACnCya,OAAO,CAACE,WAAW,CAAC;cACtB,CAAC,MAAM;gBACL;gBACAC,4BAA4B,CAACD,WAAW,EAAEF,OAAO,EAAEC,MAAM,CAAC;cAC5D;YACF,CAAC,CAAC;UACJ,CAAC,CAAC,OAAO7Y,KAAK,EAAE;YACd6Y,MAAM,CAAC7Y,KAAK,CAAC;UACf;QACF,CAAC,MAAM;UACL4Y,OAAO,CAACE,WAAW,CAAC;QACtB;MACF;IACF;IAEA,IAAIM,UAAU,GAAG,KAAK;IAEtB,SAASZ,aAAaA,CAACD,KAAK,EAAE;MAC5B;QACE,IAAI,CAACa,UAAU,EAAE;UACf;UACAA,UAAU,GAAG,IAAI;UACjB,IAAIpR,CAAC,GAAG,CAAC;UAET,IAAI;YACF,OAAOA,CAAC,GAAGuQ,KAAK,CAAC5Y,MAAM,EAAEqI,CAAC,EAAE,EAAE;cAC5B,IAAIpG,QAAQ,GAAG2W,KAAK,CAACvQ,CAAC,CAAC;cAEvB,GAAG;gBACDpG,QAAQ,GAAGA,QAAQ,CAAC,IAAI,CAAC;cAC3B,CAAC,QAAQA,QAAQ,KAAK,IAAI;YAC5B;YAEA2W,KAAK,CAAC5Y,MAAM,GAAG,CAAC;UAClB,CAAC,CAAC,OAAOK,KAAK,EAAE;YACd;YACAuY,KAAK,GAAGA,KAAK,CAAChB,KAAK,CAACvP,CAAC,GAAG,CAAC,CAAC;YAC1B,MAAMhI,KAAK;UACb,CAAC,SAAS;YACRoZ,UAAU,GAAG,KAAK;UACpB;QACF;MACF;IACF;IAEA,IAAIC,eAAe,GAAIvD,2BAA2B;IAClD,IAAIwD,cAAc,GAAIjD,0BAA0B;IAChD,IAAIkD,aAAa,GAAIpD,2BAA2B;IAChD,IAAIqD,QAAQ,GAAG;MACblZ,GAAG,EAAE6K,WAAW;MAChBsO,OAAO,EAAEhO,eAAe;MACxBH,KAAK,EAAEC,aAAa;MACpBK,OAAO,EAAEA,OAAO;MAChB8N,IAAI,EAAE7N;IACR,CAAC;IAED8N,OAAO,CAACH,QAAQ,GAAGA,QAAQ;IAC3BG,OAAO,CAACtX,SAAS,GAAGA,SAAS;IAC7BsX,OAAO,CAACC,QAAQ,GAAG3c,mBAAmB;IACtC0c,OAAO,CAACE,QAAQ,GAAG1c,mBAAmB;IACtCwc,OAAO,CAACnW,aAAa,GAAGA,aAAa;IACrCmW,OAAO,CAACG,UAAU,GAAG5c,sBAAsB;IAC3Cyc,OAAO,CAACI,QAAQ,GAAGxc,mBAAmB;IACtCoc,OAAO,CAACK,kDAAkD,GAAG1a,oBAAoB;IACjFqa,OAAO,CAACvB,GAAG,GAAGA,GAAG;IACjBuB,OAAO,CAACnR,YAAY,GAAG8Q,cAAc;IACrCK,OAAO,CAAC7N,aAAa,GAAGA,aAAa;IACrC6N,OAAO,CAAChS,aAAa,GAAG0R,eAAe;IACvCM,OAAO,CAACJ,aAAa,GAAGA,aAAa;IACrCI,OAAO,CAAChW,SAAS,GAAGA,SAAS;IAC7BgW,OAAO,CAACxL,UAAU,GAAGA,UAAU;IAC/BwL,OAAO,CAAClR,cAAc,GAAGA,cAAc;IACvCkR,OAAO,CAAC7L,IAAI,GAAGA,IAAI;IACnB6L,OAAO,CAAClL,IAAI,GAAGA,IAAI;IACnBkL,OAAO,CAACrD,eAAe,GAAGA,eAAe;IACzCqD,OAAO,CAACM,YAAY,GAAG7B,GAAG;IAC1BuB,OAAO,CAAC/J,WAAW,GAAGA,WAAW;IACjC+J,OAAO,CAAC9K,UAAU,GAAGA,UAAU;IAC/B8K,OAAO,CAAC5J,aAAa,GAAGA,aAAa;IACrC4J,OAAO,CAACzJ,gBAAgB,GAAGA,gBAAgB;IAC3CyJ,OAAO,CAACpK,SAAS,GAAGA,SAAS;IAC7BoK,OAAO,CAACxJ,KAAK,GAAGA,KAAK;IACrBwJ,OAAO,CAAC7J,mBAAmB,GAAGA,mBAAmB;IACjD6J,OAAO,CAACjK,kBAAkB,GAAGA,kBAAkB;IAC/CiK,OAAO,CAAChK,eAAe,GAAGA,eAAe;IACzCgK,OAAO,CAAC9J,OAAO,GAAGA,OAAO;IACzB8J,OAAO,CAACzK,UAAU,GAAGA,UAAU;IAC/ByK,OAAO,CAACtK,MAAM,GAAGA,MAAM;IACvBsK,OAAO,CAAC3K,QAAQ,GAAGA,QAAQ;IAC3B2K,OAAO,CAACvJ,oBAAoB,GAAGA,oBAAoB;IACnDuJ,OAAO,CAAC1J,aAAa,GAAGA,aAAa;IACrC0J,OAAO,CAACO,OAAO,GAAGtd,YAAY;IACpB;IACV,IACE,OAAOH,8BAA8B,KAAK,WAAW,IACrD,OAAOA,8BAA8B,CAAC0d,0BAA0B,KAC9D,UAAU,EACZ;MACA1d,8BAA8B,CAAC0d,0BAA0B,CAAC,IAAIxd,KAAK,CAAC,CAAC,CAAC;IACxE;EAEE,CAAC,EAAE,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}