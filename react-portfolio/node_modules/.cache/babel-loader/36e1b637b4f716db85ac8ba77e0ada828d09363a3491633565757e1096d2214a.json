{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/MagicBento.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport './MagicBento.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MagicBento = ({\n  children,\n  className = \"\",\n  style = {}\n}) => {\n  _s();\n  const [mousePosition, setMousePosition] = useState({\n    x: 0,\n    y: 0\n  });\n  const [isHovering, setIsHovering] = useState(false);\n  const cardRef = useRef(null);\n  useEffect(() => {\n    const handleMouseMove = e => {\n      if (cardRef.current && isHovering) {\n        const rect = cardRef.current.getBoundingClientRect();\n        const x = e.clientX - rect.left;\n        const y = e.clientY - rect.top;\n        setMousePosition({\n          x,\n          y\n        });\n      }\n    };\n    document.addEventListener('mousemove', handleMouseMove);\n    return () => document.removeEventListener('mousemove', handleMouseMove);\n  }, [isHovering]);\n  const handleMouseEnter = () => {\n    setIsHovering(true);\n  };\n  const handleMouseLeave = () => {\n    setIsHovering(false);\n    setMousePosition({\n      x: 0,\n      y: 0\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: cardRef,\n    className: `magic-bento ${className}`,\n    style: style,\n    onMouseEnter: handleMouseEnter,\n    onMouseLeave: handleMouseLeave,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"magic-gradient\",\n      style: {\n        background: isHovering ? `radial-gradient(600px circle at ${mousePosition.x}px ${mousePosition.y}px, rgba(255,255,255,0.1), transparent 40%)` : 'none'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"magic-border\",\n      style: {\n        background: isHovering ? `radial-gradient(600px circle at ${mousePosition.x}px ${mousePosition.y}px, rgba(255,255,255,0.4), transparent 40%)` : 'rgba(255,255,255,0.1)'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"magic-content\",\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 5\n  }, this);\n};\n_s(MagicBento, \"G7WB745jGh839F8bt35+MDG+Riw=\");\n_c = MagicBento;\nexport default MagicBento;\nvar _c;\n$RefreshReg$(_c, \"MagicBento\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "jsxDEV", "_jsxDEV", "MagicBento", "children", "className", "style", "_s", "mousePosition", "setMousePosition", "x", "y", "isHovering", "setIsHovering", "cardRef", "handleMouseMove", "e", "current", "rect", "getBoundingClientRect", "clientX", "left", "clientY", "top", "document", "addEventListener", "removeEventListener", "handleMouseEnter", "handleMouseLeave", "ref", "onMouseEnter", "onMouseLeave", "background", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/MagicBento.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport './MagicBento.css';\n\nconst MagicBento = ({ children, className = \"\", style = {} }) => {\n  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });\n  const [isHovering, setIsHovering] = useState(false);\n  const cardRef = useRef(null);\n\n  useEffect(() => {\n    const handleMouseMove = (e) => {\n      if (cardRef.current && isHovering) {\n        const rect = cardRef.current.getBoundingClientRect();\n        const x = e.clientX - rect.left;\n        const y = e.clientY - rect.top;\n        \n        setMousePosition({ x, y });\n      }\n    };\n\n    document.addEventListener('mousemove', handleMouseMove);\n    return () => document.removeEventListener('mousemove', handleMouseMove);\n  }, [isHovering]);\n\n  const handleMouseEnter = () => {\n    setIsHovering(true);\n  };\n\n  const handleMouseLeave = () => {\n    setIsHovering(false);\n    setMousePosition({ x: 0, y: 0 });\n  };\n\n  return (\n    <div\n      ref={cardRef}\n      className={`magic-bento ${className}`}\n      style={style}\n      onMouseEnter={handleMouseEnter}\n      onMouseLeave={handleMouseLeave}\n    >\n      {/* Magic gradient overlay */}\n      <div\n        className=\"magic-gradient\"\n        style={{\n          background: isHovering\n            ? `radial-gradient(600px circle at ${mousePosition.x}px ${mousePosition.y}px, rgba(255,255,255,0.1), transparent 40%)`\n            : 'none',\n        }}\n      />\n      \n      {/* Border gradient */}\n      <div\n        className=\"magic-border\"\n        style={{\n          background: isHovering\n            ? `radial-gradient(600px circle at ${mousePosition.x}px ${mousePosition.y}px, rgba(255,255,255,0.4), transparent 40%)`\n            : 'rgba(255,255,255,0.1)',\n        }}\n      />\n      \n      {/* Content */}\n      <div className=\"magic-content\">\n        {children}\n      </div>\n    </div>\n  );\n};\n\nexport default MagicBento;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,UAAU,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,SAAS,GAAG,EAAE;EAAEC,KAAK,GAAG,CAAC;AAAE,CAAC,KAAK;EAAAC,EAAA;EAC/D,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGX,QAAQ,CAAC;IAAEY,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAClE,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMgB,OAAO,GAAGf,MAAM,CAAC,IAAI,CAAC;EAE5BC,SAAS,CAAC,MAAM;IACd,MAAMe,eAAe,GAAIC,CAAC,IAAK;MAC7B,IAAIF,OAAO,CAACG,OAAO,IAAIL,UAAU,EAAE;QACjC,MAAMM,IAAI,GAAGJ,OAAO,CAACG,OAAO,CAACE,qBAAqB,CAAC,CAAC;QACpD,MAAMT,CAAC,GAAGM,CAAC,CAACI,OAAO,GAAGF,IAAI,CAACG,IAAI;QAC/B,MAAMV,CAAC,GAAGK,CAAC,CAACM,OAAO,GAAGJ,IAAI,CAACK,GAAG;QAE9Bd,gBAAgB,CAAC;UAAEC,CAAC;UAAEC;QAAE,CAAC,CAAC;MAC5B;IACF,CAAC;IAEDa,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEV,eAAe,CAAC;IACvD,OAAO,MAAMS,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEX,eAAe,CAAC;EACzE,CAAC,EAAE,CAACH,UAAU,CAAC,CAAC;EAEhB,MAAMe,gBAAgB,GAAGA,CAAA,KAAM;IAC7Bd,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMe,gBAAgB,GAAGA,CAAA,KAAM;IAC7Bf,aAAa,CAAC,KAAK,CAAC;IACpBJ,gBAAgB,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC,CAAC;EAClC,CAAC;EAED,oBACET,OAAA;IACE2B,GAAG,EAAEf,OAAQ;IACbT,SAAS,EAAE,eAAeA,SAAS,EAAG;IACtCC,KAAK,EAAEA,KAAM;IACbwB,YAAY,EAAEH,gBAAiB;IAC/BI,YAAY,EAAEH,gBAAiB;IAAAxB,QAAA,gBAG/BF,OAAA;MACEG,SAAS,EAAC,gBAAgB;MAC1BC,KAAK,EAAE;QACL0B,UAAU,EAAEpB,UAAU,GAClB,mCAAmCJ,aAAa,CAACE,CAAC,MAAMF,aAAa,CAACG,CAAC,6CAA6C,GACpH;MACN;IAAE;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGFlC,OAAA;MACEG,SAAS,EAAC,cAAc;MACxBC,KAAK,EAAE;QACL0B,UAAU,EAAEpB,UAAU,GAClB,mCAAmCJ,aAAa,CAACE,CAAC,MAAMF,aAAa,CAACG,CAAC,6CAA6C,GACpH;MACN;IAAE;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGFlC,OAAA;MAAKG,SAAS,EAAC,eAAe;MAAAD,QAAA,EAC3BA;IAAQ;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC7B,EAAA,CA/DIJ,UAAU;AAAAkC,EAAA,GAAVlC,UAAU;AAiEhB,eAAeA,UAAU;AAAC,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}