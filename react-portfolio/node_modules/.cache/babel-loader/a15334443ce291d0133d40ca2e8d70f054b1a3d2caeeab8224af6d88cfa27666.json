{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Ribbons.js\",\n  _s = $RefreshSig$();\nimport { useRef, useEffect } from 'react';\nimport { Renderer, Camera, Transform, Plane, Program, Mesh, Vec2 } from 'ogl';\nimport './Ribbons.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Ribbons = ({\n  children,\n  className = '',\n  style = {},\n  ribbonColor = 'rgba(78, 205, 196, 0.2)',\n  ribbonSpeed = 2000,\n  ribbonCount = 3\n}) => {\n  _s();\n  const canvasRef = useRef(null);\n  const rendererRef = useRef(null);\n  const animationRef = useRef(null);\n  useEffect(() => {\n    if (!canvasRef.current) return;\n    const canvas = canvasRef.current;\n    const renderer = new Renderer({\n      canvas,\n      alpha: true\n    });\n    const gl = renderer.gl;\n\n    // Set canvas size\n    const resize = () => {\n      const rect = canvas.parentElement.getBoundingClientRect();\n      renderer.setSize(rect.width, rect.height);\n    };\n    resize();\n    window.addEventListener('resize', resize);\n    const camera = new Camera(gl);\n    camera.position.z = 1;\n    const scene = new Transform();\n\n    // Vertex shader\n    const vertex = `\n      attribute vec2 uv;\n      attribute vec2 position;\n      uniform mat4 modelViewMatrix;\n      uniform mat4 projectionMatrix;\n      uniform float uTime;\n      varying vec2 vUv;\n\n      void main() {\n        vUv = uv;\n        vec3 pos = vec3(position, 0.0);\n\n        // Add wave animation\n        pos.y += sin(pos.x * 3.0 + uTime * 0.001) * 0.1;\n        pos.x += cos(pos.y * 2.0 + uTime * 0.0015) * 0.05;\n\n        gl_Position = projectionMatrix * modelViewMatrix * vec4(pos, 1.0);\n      }\n    `;\n\n    // Fragment shader\n    const fragment = `\n      precision highp float;\n      uniform float uTime;\n      uniform vec3 uColor;\n      uniform vec2 uResolution;\n      varying vec2 vUv;\n\n      void main() {\n        vec2 uv = vUv;\n\n        // Create flowing ribbon effect\n        float wave1 = sin(uv.x * 10.0 + uTime * 0.002) * 0.5 + 0.5;\n        float wave2 = sin(uv.y * 8.0 + uTime * 0.003) * 0.5 + 0.5;\n        float wave3 = sin((uv.x + uv.y) * 6.0 + uTime * 0.0025) * 0.5 + 0.5;\n\n        float alpha = wave1 * wave2 * wave3 * 0.3;\n\n        // Add gradient\n        alpha *= (1.0 - distance(uv, vec2(0.5)) * 1.5);\n\n        gl_FragColor = vec4(uColor, alpha);\n      }\n    `;\n\n    // Parse color\n    const parseColor = colorStr => {\n      const match = colorStr.match(/rgba?\\(([^)]+)\\)/);\n      if (match) {\n        const values = match[1].split(',').map(v => parseFloat(v.trim()));\n        return [values[0] / 255, values[1] / 255, values[2] / 255];\n      }\n      return [1, 1, 1];\n    };\n    const color = parseColor(ribbonColor);\n\n    // Create ribbons\n    const ribbons = [];\n    for (let i = 0; i < ribbonCount; i++) {\n      const geometry = new Plane(gl, {\n        width: 2,\n        height: 2,\n        widthSegments: 32,\n        heightSegments: 32\n      });\n      const program = new Program(gl, {\n        vertex,\n        fragment,\n        uniforms: {\n          uTime: {\n            value: 0\n          },\n          uColor: {\n            value: color\n          },\n          uResolution: {\n            value: new Vec2(canvas.width, canvas.height)\n          }\n        },\n        transparent: true,\n        cullFace: null\n      });\n      const mesh = new Mesh(gl, {\n        geometry,\n        program\n      });\n      mesh.position.z = -i * 0.1;\n      mesh.setParent(scene);\n      ribbons.push({\n        mesh,\n        program,\n        offset: i * 1000\n      });\n    }\n    rendererRef.current = renderer;\n\n    // Animation loop\n    const animate = time => {\n      ribbons.forEach(({\n        program,\n        offset\n      }) => {\n        program.uniforms.uTime.value = (time + offset) * (ribbonSpeed / 2000);\n      });\n      renderer.render({\n        scene,\n        camera\n      });\n      animationRef.current = requestAnimationFrame(animate);\n    };\n    animate(0);\n    return () => {\n      window.removeEventListener('resize', resize);\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current);\n      }\n      if (rendererRef.current) {\n        var _rendererRef$current$;\n        (_rendererRef$current$ = rendererRef.current.gl.getExtension('WEBGL_lose_context')) === null || _rendererRef$current$ === void 0 ? void 0 : _rendererRef$current$.loseContext();\n      }\n    };\n  }, [ribbonColor, ribbonSpeed, ribbonCount]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `ribbons-container ${className}`,\n    style: style,\n    children: [/*#__PURE__*/_jsxDEV(\"canvas\", {\n      ref: canvasRef,\n      className: \"ribbons-canvas\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ribbons-content\",\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 151,\n    columnNumber: 5\n  }, this);\n};\n_s(Ribbons, \"/2PXwoRPtedWUdwpv8ha3uOXCYg=\");\n_c = Ribbons;\nexport default Ribbons;\nvar _c;\n$RefreshReg$(_c, \"Ribbons\");", "map": {"version": 3, "names": ["useRef", "useEffect", "<PERSON><PERSON><PERSON>", "Camera", "Transform", "Plane", "Program", "<PERSON><PERSON>", "Vec2", "jsxDEV", "_jsxDEV", "Ribbons", "children", "className", "style", "ribbonColor", "ribbonSpeed", "ribbonCount", "_s", "canvasRef", "rendererRef", "animationRef", "current", "canvas", "renderer", "alpha", "gl", "resize", "rect", "parentElement", "getBoundingClientRect", "setSize", "width", "height", "window", "addEventListener", "camera", "position", "z", "scene", "vertex", "fragment", "parseColor", "colorStr", "match", "values", "split", "map", "v", "parseFloat", "trim", "color", "ribbons", "i", "geometry", "widthSegments", "heightSegments", "program", "uniforms", "uTime", "value", "uColor", "uResolution", "transparent", "cullFace", "mesh", "setParent", "push", "offset", "animate", "time", "for<PERSON>ach", "render", "requestAnimationFrame", "removeEventListener", "cancelAnimationFrame", "_rendererRef$current$", "getExtension", "loseContext", "ref", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Ribbons.js"], "sourcesContent": ["import { useRef, useEffect } from 'react';\nimport { Renderer, Camera, Transform, Plane, Program, Mesh, Vec2 } from 'ogl';\nimport './Ribbons.css';\n\nconst Ribbons = ({\n  children,\n  className = '',\n  style = {},\n  ribbonColor = 'rgba(78, 205, 196, 0.2)',\n  ribbonSpeed = 2000,\n  ribbonCount = 3\n}) => {\n  const canvasRef = useRef(null);\n  const rendererRef = useRef(null);\n  const animationRef = useRef(null);\n\n  useEffect(() => {\n    if (!canvasRef.current) return;\n\n    const canvas = canvasRef.current;\n    const renderer = new Renderer({ canvas, alpha: true });\n    const gl = renderer.gl;\n\n    // Set canvas size\n    const resize = () => {\n      const rect = canvas.parentElement.getBoundingClientRect();\n      renderer.setSize(rect.width, rect.height);\n    };\n\n    resize();\n    window.addEventListener('resize', resize);\n\n    const camera = new Camera(gl);\n    camera.position.z = 1;\n\n    const scene = new Transform();\n\n    // Vertex shader\n    const vertex = `\n      attribute vec2 uv;\n      attribute vec2 position;\n      uniform mat4 modelViewMatrix;\n      uniform mat4 projectionMatrix;\n      uniform float uTime;\n      varying vec2 vUv;\n\n      void main() {\n        vUv = uv;\n        vec3 pos = vec3(position, 0.0);\n\n        // Add wave animation\n        pos.y += sin(pos.x * 3.0 + uTime * 0.001) * 0.1;\n        pos.x += cos(pos.y * 2.0 + uTime * 0.0015) * 0.05;\n\n        gl_Position = projectionMatrix * modelViewMatrix * vec4(pos, 1.0);\n      }\n    `;\n\n    // Fragment shader\n    const fragment = `\n      precision highp float;\n      uniform float uTime;\n      uniform vec3 uColor;\n      uniform vec2 uResolution;\n      varying vec2 vUv;\n\n      void main() {\n        vec2 uv = vUv;\n\n        // Create flowing ribbon effect\n        float wave1 = sin(uv.x * 10.0 + uTime * 0.002) * 0.5 + 0.5;\n        float wave2 = sin(uv.y * 8.0 + uTime * 0.003) * 0.5 + 0.5;\n        float wave3 = sin((uv.x + uv.y) * 6.0 + uTime * 0.0025) * 0.5 + 0.5;\n\n        float alpha = wave1 * wave2 * wave3 * 0.3;\n\n        // Add gradient\n        alpha *= (1.0 - distance(uv, vec2(0.5)) * 1.5);\n\n        gl_FragColor = vec4(uColor, alpha);\n      }\n    `;\n\n    // Parse color\n    const parseColor = (colorStr) => {\n      const match = colorStr.match(/rgba?\\(([^)]+)\\)/);\n      if (match) {\n        const values = match[1].split(',').map(v => parseFloat(v.trim()));\n        return [values[0] / 255, values[1] / 255, values[2] / 255];\n      }\n      return [1, 1, 1];\n    };\n\n    const color = parseColor(ribbonColor);\n\n    // Create ribbons\n    const ribbons = [];\n    for (let i = 0; i < ribbonCount; i++) {\n      const geometry = new Plane(gl, {\n        width: 2,\n        height: 2,\n        widthSegments: 32,\n        heightSegments: 32\n      });\n\n      const program = new Program(gl, {\n        vertex,\n        fragment,\n        uniforms: {\n          uTime: { value: 0 },\n          uColor: { value: color },\n          uResolution: { value: new Vec2(canvas.width, canvas.height) }\n        },\n        transparent: true,\n        cullFace: null\n      });\n\n      const mesh = new Mesh(gl, { geometry, program });\n      mesh.position.z = -i * 0.1;\n      mesh.setParent(scene);\n\n      ribbons.push({ mesh, program, offset: i * 1000 });\n    }\n\n    rendererRef.current = renderer;\n\n    // Animation loop\n    const animate = (time) => {\n      ribbons.forEach(({ program, offset }) => {\n        program.uniforms.uTime.value = (time + offset) * (ribbonSpeed / 2000);\n      });\n\n      renderer.render({ scene, camera });\n      animationRef.current = requestAnimationFrame(animate);\n    };\n\n    animate(0);\n\n    return () => {\n      window.removeEventListener('resize', resize);\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current);\n      }\n      if (rendererRef.current) {\n        rendererRef.current.gl.getExtension('WEBGL_lose_context')?.loseContext();\n      }\n    };\n  }, [ribbonColor, ribbonSpeed, ribbonCount]);\n\n  return (\n    <div className={`ribbons-container ${className}`} style={style}>\n      <canvas\n        ref={canvasRef}\n        className=\"ribbons-canvas\"\n      />\n      <div className=\"ribbons-content\">\n        {children}\n      </div>\n    </div>\n  );\n};\n\nexport default Ribbons;\n"], "mappings": ";;AAAA,SAASA,MAAM,EAAEC,SAAS,QAAQ,OAAO;AACzC,SAASC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,KAAK,EAAEC,OAAO,EAAEC,IAAI,EAAEC,IAAI,QAAQ,KAAK;AAC7E,OAAO,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvB,MAAMC,OAAO,GAAGA,CAAC;EACfC,QAAQ;EACRC,SAAS,GAAG,EAAE;EACdC,KAAK,GAAG,CAAC,CAAC;EACVC,WAAW,GAAG,yBAAyB;EACvCC,WAAW,GAAG,IAAI;EAClBC,WAAW,GAAG;AAChB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,SAAS,GAAGnB,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMoB,WAAW,GAAGpB,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMqB,YAAY,GAAGrB,MAAM,CAAC,IAAI,CAAC;EAEjCC,SAAS,CAAC,MAAM;IACd,IAAI,CAACkB,SAAS,CAACG,OAAO,EAAE;IAExB,MAAMC,MAAM,GAAGJ,SAAS,CAACG,OAAO;IAChC,MAAME,QAAQ,GAAG,IAAItB,QAAQ,CAAC;MAAEqB,MAAM;MAAEE,KAAK,EAAE;IAAK,CAAC,CAAC;IACtD,MAAMC,EAAE,GAAGF,QAAQ,CAACE,EAAE;;IAEtB;IACA,MAAMC,MAAM,GAAGA,CAAA,KAAM;MACnB,MAAMC,IAAI,GAAGL,MAAM,CAACM,aAAa,CAACC,qBAAqB,CAAC,CAAC;MACzDN,QAAQ,CAACO,OAAO,CAACH,IAAI,CAACI,KAAK,EAAEJ,IAAI,CAACK,MAAM,CAAC;IAC3C,CAAC;IAEDN,MAAM,CAAC,CAAC;IACRO,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAER,MAAM,CAAC;IAEzC,MAAMS,MAAM,GAAG,IAAIjC,MAAM,CAACuB,EAAE,CAAC;IAC7BU,MAAM,CAACC,QAAQ,CAACC,CAAC,GAAG,CAAC;IAErB,MAAMC,KAAK,GAAG,IAAInC,SAAS,CAAC,CAAC;;IAE7B;IACA,MAAMoC,MAAM,GAAG;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;IAED;IACA,MAAMC,QAAQ,GAAG;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;IAED;IACA,MAAMC,UAAU,GAAIC,QAAQ,IAAK;MAC/B,MAAMC,KAAK,GAAGD,QAAQ,CAACC,KAAK,CAAC,kBAAkB,CAAC;MAChD,IAAIA,KAAK,EAAE;QACT,MAAMC,MAAM,GAAGD,KAAK,CAAC,CAAC,CAAC,CAACE,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIC,UAAU,CAACD,CAAC,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC;QACjE,OAAO,CAACL,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,EAAEA,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,EAAEA,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;MAC5D;MACA,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAClB,CAAC;IAED,MAAMM,KAAK,GAAGT,UAAU,CAAC3B,WAAW,CAAC;;IAErC;IACA,MAAMqC,OAAO,GAAG,EAAE;IAClB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpC,WAAW,EAAEoC,CAAC,EAAE,EAAE;MACpC,MAAMC,QAAQ,GAAG,IAAIjD,KAAK,CAACqB,EAAE,EAAE;QAC7BM,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTsB,aAAa,EAAE,EAAE;QACjBC,cAAc,EAAE;MAClB,CAAC,CAAC;MAEF,MAAMC,OAAO,GAAG,IAAInD,OAAO,CAACoB,EAAE,EAAE;QAC9Bc,MAAM;QACNC,QAAQ;QACRiB,QAAQ,EAAE;UACRC,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAE,CAAC;UACnBC,MAAM,EAAE;YAAED,KAAK,EAAET;UAAM,CAAC;UACxBW,WAAW,EAAE;YAAEF,KAAK,EAAE,IAAIpD,IAAI,CAACe,MAAM,CAACS,KAAK,EAAET,MAAM,CAACU,MAAM;UAAE;QAC9D,CAAC;QACD8B,WAAW,EAAE,IAAI;QACjBC,QAAQ,EAAE;MACZ,CAAC,CAAC;MAEF,MAAMC,IAAI,GAAG,IAAI1D,IAAI,CAACmB,EAAE,EAAE;QAAE4B,QAAQ;QAAEG;MAAQ,CAAC,CAAC;MAChDQ,IAAI,CAAC5B,QAAQ,CAACC,CAAC,GAAG,CAACe,CAAC,GAAG,GAAG;MAC1BY,IAAI,CAACC,SAAS,CAAC3B,KAAK,CAAC;MAErBa,OAAO,CAACe,IAAI,CAAC;QAAEF,IAAI;QAAER,OAAO;QAAEW,MAAM,EAAEf,CAAC,GAAG;MAAK,CAAC,CAAC;IACnD;IAEAjC,WAAW,CAACE,OAAO,GAAGE,QAAQ;;IAE9B;IACA,MAAM6C,OAAO,GAAIC,IAAI,IAAK;MACxBlB,OAAO,CAACmB,OAAO,CAAC,CAAC;QAAEd,OAAO;QAAEW;MAAO,CAAC,KAAK;QACvCX,OAAO,CAACC,QAAQ,CAACC,KAAK,CAACC,KAAK,GAAG,CAACU,IAAI,GAAGF,MAAM,KAAKpD,WAAW,GAAG,IAAI,CAAC;MACvE,CAAC,CAAC;MAEFQ,QAAQ,CAACgD,MAAM,CAAC;QAAEjC,KAAK;QAAEH;MAAO,CAAC,CAAC;MAClCf,YAAY,CAACC,OAAO,GAAGmD,qBAAqB,CAACJ,OAAO,CAAC;IACvD,CAAC;IAEDA,OAAO,CAAC,CAAC,CAAC;IAEV,OAAO,MAAM;MACXnC,MAAM,CAACwC,mBAAmB,CAAC,QAAQ,EAAE/C,MAAM,CAAC;MAC5C,IAAIN,YAAY,CAACC,OAAO,EAAE;QACxBqD,oBAAoB,CAACtD,YAAY,CAACC,OAAO,CAAC;MAC5C;MACA,IAAIF,WAAW,CAACE,OAAO,EAAE;QAAA,IAAAsD,qBAAA;QACvB,CAAAA,qBAAA,GAAAxD,WAAW,CAACE,OAAO,CAACI,EAAE,CAACmD,YAAY,CAAC,oBAAoB,CAAC,cAAAD,qBAAA,uBAAzDA,qBAAA,CAA2DE,WAAW,CAAC,CAAC;MAC1E;IACF,CAAC;EACH,CAAC,EAAE,CAAC/D,WAAW,EAAEC,WAAW,EAAEC,WAAW,CAAC,CAAC;EAE3C,oBACEP,OAAA;IAAKG,SAAS,EAAE,qBAAqBA,SAAS,EAAG;IAACC,KAAK,EAAEA,KAAM;IAAAF,QAAA,gBAC7DF,OAAA;MACEqE,GAAG,EAAE5D,SAAU;MACfN,SAAS,EAAC;IAAgB;MAAAmE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC,eACFzE,OAAA;MAAKG,SAAS,EAAC,iBAAiB;MAAAD,QAAA,EAC7BA;IAAQ;MAAAoE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjE,EAAA,CA5JIP,OAAO;AAAAyE,EAAA,GAAPzE,OAAO;AA8Jb,eAAeA,OAAO;AAAC,IAAAyE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}