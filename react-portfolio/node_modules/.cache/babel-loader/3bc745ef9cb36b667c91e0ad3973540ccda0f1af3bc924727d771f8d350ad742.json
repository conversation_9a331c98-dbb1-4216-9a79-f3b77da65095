{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ScrollStack.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport './ScrollStack.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ScrollStack = ({\n  children,\n  className = \"\",\n  style = {},\n  stackOffset = 20,\n  stackScale = 0.95,\n  threshold = 0.1\n}) => {\n  _s();\n  const [scrollProgress, setScrollProgress] = useState(0);\n  const containerRef = useRef(null);\n  useEffect(() => {\n    const handleScroll = () => {\n      if (!containerRef.current) return;\n      const rect = containerRef.current.getBoundingClientRect();\n      const windowHeight = window.innerHeight;\n      const containerTop = rect.top;\n      const containerHeight = rect.height;\n\n      // Calculate scroll progress based on container position\n      const startPoint = windowHeight; // Start when container enters viewport\n      const endPoint = -containerHeight + windowHeight * 0.2; // End when almost out of view\n\n      let progress = 0;\n      if (containerTop <= startPoint && containerTop >= endPoint) {\n        progress = (startPoint - containerTop) / (startPoint - endPoint);\n        progress = Math.max(0, Math.min(1, progress));\n      } else if (containerTop < endPoint) {\n        progress = 1;\n      }\n      setScrollProgress(progress);\n    };\n    window.addEventListener('scroll', handleScroll, {\n      passive: true\n    });\n    handleScroll();\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n  const getStackStyle = (index, total) => {\n    // Calculate the base stacking effect\n    const stackedOffset = index * stackOffset; // How much each card is offset when stacked\n    const stackedScale = Math.pow(stackScale, index); // How much each card is scaled when stacked\n\n    // Calculate the final spread position\n    const spreadOffset = index * 120; // Distance between cards when spread\n    const spreadScale = 1; // All cards same size when spread\n\n    // Interpolate based on scroll progress\n    const currentYOffset = stackedOffset + scrollProgress * (spreadOffset - stackedOffset);\n    const currentScale = stackedScale + scrollProgress * (spreadScale - stackedScale);\n\n    // Opacity calculation\n    const baseOpacity = Math.max(0.4, 1 - index * 0.2);\n    const scrollOpacity = 0.4 + scrollProgress * 0.6;\n    const finalOpacity = Math.min(1, baseOpacity + scrollOpacity);\n\n    // Rotation for 3D effect\n    const rotation = (1 - scrollProgress) * index * 3;\n    return {\n      transform: `\n        translateX(-50%)\n        translateY(${currentYOffset}px)\n        scale(${currentScale})\n        rotateX(${rotation}deg)\n        translateZ(${-index * 20}px)\n      `,\n      zIndex: total - index,\n      opacity: finalOpacity,\n      transformOrigin: 'center top'\n    };\n  };\n\n  // Convert children to array if it's not already\n  const childrenArray = React.Children.toArray(children);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: containerRef,\n    className: `scroll-stack ${className}`,\n    style: style,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"scroll-stack-container\",\n      children: childrenArray.map((child, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"scroll-stack-item\",\n        style: getStackStyle(index, childrenArray.length),\n        children: child\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 84,\n    columnNumber: 5\n  }, this);\n};\n_s(ScrollStack, \"s1hDQaLO+Y+X3sQDuGrxH3NJatY=\");\n_c = ScrollStack;\nexport default ScrollStack;\nvar _c;\n$RefreshReg$(_c, \"ScrollStack\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "ScrollStack", "children", "className", "style", "stackOffset", "stackScale", "threshold", "_s", "scrollProgress", "setScrollProgress", "containerRef", "handleScroll", "current", "rect", "getBoundingClientRect", "windowHeight", "window", "innerHeight", "containerTop", "top", "containerHeight", "height", "startPoint", "endPoint", "progress", "Math", "max", "min", "addEventListener", "passive", "removeEventListener", "getStackStyle", "index", "total", "stackedOffset", "stackedScale", "pow", "spreadOffset", "spreadScale", "currentYOffset", "currentScale", "baseOpacity", "scrollOpacity", "finalOpacity", "rotation", "transform", "zIndex", "opacity", "transform<PERSON><PERSON>in", "childrenA<PERSON>y", "Children", "toArray", "ref", "map", "child", "length", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ScrollStack.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport './ScrollStack.css';\n\nconst ScrollStack = ({\n  children,\n  className = \"\",\n  style = {},\n  stackOffset = 20,\n  stackScale = 0.95,\n  threshold = 0.1\n}) => {\n  const [scrollProgress, setScrollProgress] = useState(0);\n  const containerRef = useRef(null);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      if (!containerRef.current) return;\n\n      const rect = containerRef.current.getBoundingClientRect();\n      const windowHeight = window.innerHeight;\n      const containerTop = rect.top;\n      const containerHeight = rect.height;\n\n      // Calculate scroll progress based on container position\n      const startPoint = windowHeight; // Start when container enters viewport\n      const endPoint = -containerHeight + windowHeight * 0.2; // End when almost out of view\n\n      let progress = 0;\n      if (containerTop <= startPoint && containerTop >= endPoint) {\n        progress = (startPoint - containerTop) / (startPoint - endPoint);\n        progress = Math.max(0, Math.min(1, progress));\n      } else if (containerTop < endPoint) {\n        progress = 1;\n      }\n\n      setScrollProgress(progress);\n    };\n\n    window.addEventListener('scroll', handleScroll, { passive: true });\n    handleScroll();\n\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const getStackStyle = (index, total) => {\n    // Calculate the base stacking effect\n    const stackedOffset = index * stackOffset; // How much each card is offset when stacked\n    const stackedScale = Math.pow(stackScale, index); // How much each card is scaled when stacked\n\n    // Calculate the final spread position\n    const spreadOffset = index * 120; // Distance between cards when spread\n    const spreadScale = 1; // All cards same size when spread\n\n    // Interpolate based on scroll progress\n    const currentYOffset = stackedOffset + (scrollProgress * (spreadOffset - stackedOffset));\n    const currentScale = stackedScale + (scrollProgress * (spreadScale - stackedScale));\n\n    // Opacity calculation\n    const baseOpacity = Math.max(0.4, 1 - (index * 0.2));\n    const scrollOpacity = 0.4 + (scrollProgress * 0.6);\n    const finalOpacity = Math.min(1, baseOpacity + scrollOpacity);\n\n    // Rotation for 3D effect\n    const rotation = (1 - scrollProgress) * index * 3;\n\n    return {\n      transform: `\n        translateX(-50%)\n        translateY(${currentYOffset}px)\n        scale(${currentScale})\n        rotateX(${rotation}deg)\n        translateZ(${-index * 20}px)\n      `,\n      zIndex: total - index,\n      opacity: finalOpacity,\n      transformOrigin: 'center top'\n    };\n  };\n\n  // Convert children to array if it's not already\n  const childrenArray = React.Children.toArray(children);\n\n  return (\n    <div\n      ref={containerRef}\n      className={`scroll-stack ${className}`}\n      style={style}\n    >\n\n\n      <div className=\"scroll-stack-container\">\n        {childrenArray.map((child, index) => (\n          <div\n            key={index}\n            className=\"scroll-stack-item\"\n            style={getStackStyle(index, childrenArray.length)}\n          >\n            {child}\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n};\n\nexport default ScrollStack;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,WAAW,GAAGA,CAAC;EACnBC,QAAQ;EACRC,SAAS,GAAG,EAAE;EACdC,KAAK,GAAG,CAAC,CAAC;EACVC,WAAW,GAAG,EAAE;EAChBC,UAAU,GAAG,IAAI;EACjBC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGd,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAMe,YAAY,GAAGb,MAAM,CAAC,IAAI,CAAC;EAEjCD,SAAS,CAAC,MAAM;IACd,MAAMe,YAAY,GAAGA,CAAA,KAAM;MACzB,IAAI,CAACD,YAAY,CAACE,OAAO,EAAE;MAE3B,MAAMC,IAAI,GAAGH,YAAY,CAACE,OAAO,CAACE,qBAAqB,CAAC,CAAC;MACzD,MAAMC,YAAY,GAAGC,MAAM,CAACC,WAAW;MACvC,MAAMC,YAAY,GAAGL,IAAI,CAACM,GAAG;MAC7B,MAAMC,eAAe,GAAGP,IAAI,CAACQ,MAAM;;MAEnC;MACA,MAAMC,UAAU,GAAGP,YAAY,CAAC,CAAC;MACjC,MAAMQ,QAAQ,GAAG,CAACH,eAAe,GAAGL,YAAY,GAAG,GAAG,CAAC,CAAC;;MAExD,IAAIS,QAAQ,GAAG,CAAC;MAChB,IAAIN,YAAY,IAAII,UAAU,IAAIJ,YAAY,IAAIK,QAAQ,EAAE;QAC1DC,QAAQ,GAAG,CAACF,UAAU,GAAGJ,YAAY,KAAKI,UAAU,GAAGC,QAAQ,CAAC;QAChEC,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEH,QAAQ,CAAC,CAAC;MAC/C,CAAC,MAAM,IAAIN,YAAY,GAAGK,QAAQ,EAAE;QAClCC,QAAQ,GAAG,CAAC;MACd;MAEAf,iBAAiB,CAACe,QAAQ,CAAC;IAC7B,CAAC;IAEDR,MAAM,CAACY,gBAAgB,CAAC,QAAQ,EAAEjB,YAAY,EAAE;MAAEkB,OAAO,EAAE;IAAK,CAAC,CAAC;IAClElB,YAAY,CAAC,CAAC;IAEd,OAAO,MAAMK,MAAM,CAACc,mBAAmB,CAAC,QAAQ,EAAEnB,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMoB,aAAa,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACtC;IACA,MAAMC,aAAa,GAAGF,KAAK,GAAG5B,WAAW,CAAC,CAAC;IAC3C,MAAM+B,YAAY,GAAGV,IAAI,CAACW,GAAG,CAAC/B,UAAU,EAAE2B,KAAK,CAAC,CAAC,CAAC;;IAElD;IACA,MAAMK,YAAY,GAAGL,KAAK,GAAG,GAAG,CAAC,CAAC;IAClC,MAAMM,WAAW,GAAG,CAAC,CAAC,CAAC;;IAEvB;IACA,MAAMC,cAAc,GAAGL,aAAa,GAAI1B,cAAc,IAAI6B,YAAY,GAAGH,aAAa,CAAE;IACxF,MAAMM,YAAY,GAAGL,YAAY,GAAI3B,cAAc,IAAI8B,WAAW,GAAGH,YAAY,CAAE;;IAEnF;IACA,MAAMM,WAAW,GAAGhB,IAAI,CAACC,GAAG,CAAC,GAAG,EAAE,CAAC,GAAIM,KAAK,GAAG,GAAI,CAAC;IACpD,MAAMU,aAAa,GAAG,GAAG,GAAIlC,cAAc,GAAG,GAAI;IAClD,MAAMmC,YAAY,GAAGlB,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEc,WAAW,GAAGC,aAAa,CAAC;;IAE7D;IACA,MAAME,QAAQ,GAAG,CAAC,CAAC,GAAGpC,cAAc,IAAIwB,KAAK,GAAG,CAAC;IAEjD,OAAO;MACLa,SAAS,EAAE;AACjB;AACA,qBAAqBN,cAAc;AACnC,gBAAgBC,YAAY;AAC5B,kBAAkBI,QAAQ;AAC1B,qBAAqB,CAACZ,KAAK,GAAG,EAAE;AAChC,OAAO;MACDc,MAAM,EAAEb,KAAK,GAAGD,KAAK;MACrBe,OAAO,EAAEJ,YAAY;MACrBK,eAAe,EAAE;IACnB,CAAC;EACH,CAAC;;EAED;EACA,MAAMC,aAAa,GAAGvD,KAAK,CAACwD,QAAQ,CAACC,OAAO,CAAClD,QAAQ,CAAC;EAEtD,oBACEF,OAAA;IACEqD,GAAG,EAAE1C,YAAa;IAClBR,SAAS,EAAE,gBAAgBA,SAAS,EAAG;IACvCC,KAAK,EAAEA,KAAM;IAAAF,QAAA,eAIbF,OAAA;MAAKG,SAAS,EAAC,wBAAwB;MAAAD,QAAA,EACpCgD,aAAa,CAACI,GAAG,CAAC,CAACC,KAAK,EAAEtB,KAAK,kBAC9BjC,OAAA;QAEEG,SAAS,EAAC,mBAAmB;QAC7BC,KAAK,EAAE4B,aAAa,CAACC,KAAK,EAAEiB,aAAa,CAACM,MAAM,CAAE;QAAAtD,QAAA,EAEjDqD;MAAK,GAJDtB,KAAK;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKP,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpD,EAAA,CApGIP,WAAW;AAAA4D,EAAA,GAAX5D,WAAW;AAsGjB,eAAeA,WAAW;AAAC,IAAA4D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}