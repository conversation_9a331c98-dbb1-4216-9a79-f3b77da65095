{"ast": null, "code": "function t(t, e, i) {\n  return Math.max(t, Math.min(e, i));\n}\nclass Animate {\n  advance(e) {\n    if (!this.isRunning) return;\n    let i = !1;\n    if (this.lerp) this.value = (s = this.value, o = this.to, n = 60 * this.lerp, r = e, function (t, e, i) {\n      return (1 - i) * t + i * e;\n    }(s, o, 1 - Math.exp(-n * r))), Math.round(this.value) === this.to && (this.value = this.to, i = !0);else {\n      this.currentTime += e;\n      const s = t(0, this.currentTime / this.duration, 1);\n      i = s >= 1;\n      const o = i ? 1 : this.easing(s);\n      this.value = this.from + (this.to - this.from) * o;\n    }\n    var s, o, n, r;\n    this.onUpdate?.(this.value, i), i && this.stop();\n  }\n  stop() {\n    this.isRunning = !1;\n  }\n  fromTo(t, e, {\n    lerp: i = .1,\n    duration: s = 1,\n    easing: o = t => t,\n    onStart: n,\n    onUpdate: r\n  }) {\n    this.from = this.value = t, this.to = e, this.lerp = i, this.duration = s, this.easing = o, this.currentTime = 0, this.isRunning = !0, n?.(), this.onUpdate = r;\n  }\n}\nclass Dimensions {\n  constructor({\n    wrapper: t,\n    content: e,\n    autoResize: i = !0,\n    debounce: s = 250\n  } = {}) {\n    this.wrapper = t, this.content = e, i && (this.debouncedResize = function (t, e) {\n      let i;\n      return function () {\n        let s = arguments,\n          o = this;\n        clearTimeout(i), i = setTimeout(function () {\n          t.apply(o, s);\n        }, e);\n      };\n    }(this.resize, s), this.wrapper === window ? window.addEventListener(\"resize\", this.debouncedResize, !1) : (this.wrapperResizeObserver = new ResizeObserver(this.debouncedResize), this.wrapperResizeObserver.observe(this.wrapper)), this.contentResizeObserver = new ResizeObserver(this.debouncedResize), this.contentResizeObserver.observe(this.content)), this.resize();\n  }\n  destroy() {\n    this.wrapperResizeObserver?.disconnect(), this.contentResizeObserver?.disconnect(), window.removeEventListener(\"resize\", this.debouncedResize, !1);\n  }\n  resize = () => {\n    this.onWrapperResize(), this.onContentResize();\n  };\n  onWrapperResize = () => {\n    this.wrapper === window ? (this.width = window.innerWidth, this.height = window.innerHeight) : (this.width = this.wrapper.clientWidth, this.height = this.wrapper.clientHeight);\n  };\n  onContentResize = () => {\n    this.wrapper === window ? (this.scrollHeight = this.content.scrollHeight, this.scrollWidth = this.content.scrollWidth) : (this.scrollHeight = this.wrapper.scrollHeight, this.scrollWidth = this.wrapper.scrollWidth);\n  };\n  get limit() {\n    return {\n      x: this.scrollWidth - this.width,\n      y: this.scrollHeight - this.height\n    };\n  }\n}\nclass Emitter {\n  constructor() {\n    this.events = {};\n  }\n  emit(t, ...e) {\n    let i = this.events[t] || [];\n    for (let t = 0, s = i.length; t < s; t++) i[t](...e);\n  }\n  on(t, e) {\n    return this.events[t]?.push(e) || (this.events[t] = [e]), () => {\n      this.events[t] = this.events[t]?.filter(t => e !== t);\n    };\n  }\n  off(t, e) {\n    this.events[t] = this.events[t]?.filter(t => e !== t);\n  }\n  destroy() {\n    this.events = {};\n  }\n}\nconst e = 100 / 6;\nclass VirtualScroll {\n  constructor(t, {\n    wheelMultiplier: e = 1,\n    touchMultiplier: i = 1\n  }) {\n    this.element = t, this.wheelMultiplier = e, this.touchMultiplier = i, this.touchStart = {\n      x: null,\n      y: null\n    }, this.emitter = new Emitter(), window.addEventListener(\"resize\", this.onWindowResize, !1), this.onWindowResize(), this.element.addEventListener(\"wheel\", this.onWheel, {\n      passive: !1\n    }), this.element.addEventListener(\"touchstart\", this.onTouchStart, {\n      passive: !1\n    }), this.element.addEventListener(\"touchmove\", this.onTouchMove, {\n      passive: !1\n    }), this.element.addEventListener(\"touchend\", this.onTouchEnd, {\n      passive: !1\n    });\n  }\n  on(t, e) {\n    return this.emitter.on(t, e);\n  }\n  destroy() {\n    this.emitter.destroy(), window.removeEventListener(\"resize\", this.onWindowResize, !1), this.element.removeEventListener(\"wheel\", this.onWheel, {\n      passive: !1\n    }), this.element.removeEventListener(\"touchstart\", this.onTouchStart, {\n      passive: !1\n    }), this.element.removeEventListener(\"touchmove\", this.onTouchMove, {\n      passive: !1\n    }), this.element.removeEventListener(\"touchend\", this.onTouchEnd, {\n      passive: !1\n    });\n  }\n  onTouchStart = t => {\n    const {\n      clientX: e,\n      clientY: i\n    } = t.targetTouches ? t.targetTouches[0] : t;\n    this.touchStart.x = e, this.touchStart.y = i, this.lastDelta = {\n      x: 0,\n      y: 0\n    }, this.emitter.emit(\"scroll\", {\n      deltaX: 0,\n      deltaY: 0,\n      event: t\n    });\n  };\n  onTouchMove = t => {\n    const {\n        clientX: e,\n        clientY: i\n      } = t.targetTouches ? t.targetTouches[0] : t,\n      s = -(e - this.touchStart.x) * this.touchMultiplier,\n      o = -(i - this.touchStart.y) * this.touchMultiplier;\n    this.touchStart.x = e, this.touchStart.y = i, this.lastDelta = {\n      x: s,\n      y: o\n    }, this.emitter.emit(\"scroll\", {\n      deltaX: s,\n      deltaY: o,\n      event: t\n    });\n  };\n  onTouchEnd = t => {\n    this.emitter.emit(\"scroll\", {\n      deltaX: this.lastDelta.x,\n      deltaY: this.lastDelta.y,\n      event: t\n    });\n  };\n  onWheel = t => {\n    let {\n      deltaX: i,\n      deltaY: s,\n      deltaMode: o\n    } = t;\n    i *= 1 === o ? e : 2 === o ? this.windowWidth : 1, s *= 1 === o ? e : 2 === o ? this.windowHeight : 1, i *= this.wheelMultiplier, s *= this.wheelMultiplier, this.emitter.emit(\"scroll\", {\n      deltaX: i,\n      deltaY: s,\n      event: t\n    });\n  };\n  onWindowResize = () => {\n    this.windowWidth = window.innerWidth, this.windowHeight = window.innerHeight;\n  };\n}\nclass Lenis {\n  constructor({\n    wrapper: t = window,\n    content: e = document.documentElement,\n    wheelEventsTarget: i = t,\n    eventsTarget: s = i,\n    smoothWheel: o = !0,\n    syncTouch: n = !1,\n    syncTouchLerp: r = .075,\n    touchInertiaMultiplier: l = 35,\n    duration: h,\n    easing: a = t => Math.min(1, 1.001 - Math.pow(2, -10 * t)),\n    lerp: c = !h && .1,\n    infinite: d = !1,\n    orientation: p = \"vertical\",\n    gestureOrientation: u = \"vertical\",\n    touchMultiplier: m = 1,\n    wheelMultiplier: v = 1,\n    autoResize: g = !0,\n    __experimental__naiveDimensions: S = !1\n  } = {}) {\n    this.__isSmooth = !1, this.__isScrolling = !1, this.__isStopped = !1, this.__isLocked = !1, this.onVirtualScroll = ({\n      deltaX: t,\n      deltaY: e,\n      event: i\n    }) => {\n      if (i.ctrlKey) return;\n      const s = i.type.includes(\"touch\"),\n        o = i.type.includes(\"wheel\");\n      if (this.options.syncTouch && s && \"touchstart\" === i.type && !this.isStopped && !this.isLocked) return void this.reset();\n      const n = 0 === t && 0 === e,\n        r = \"vertical\" === this.options.gestureOrientation && 0 === e || \"horizontal\" === this.options.gestureOrientation && 0 === t;\n      if (n || r) return;\n      let l = i.composedPath();\n      if (l = l.slice(0, l.indexOf(this.rootElement)), l.find(t => {\n        var e, i, n, r, l;\n        return (null === (e = t.hasAttribute) || void 0 === e ? void 0 : e.call(t, \"data-lenis-prevent\")) || s && (null === (i = t.hasAttribute) || void 0 === i ? void 0 : i.call(t, \"data-lenis-prevent-touch\")) || o && (null === (n = t.hasAttribute) || void 0 === n ? void 0 : n.call(t, \"data-lenis-prevent-wheel\")) || (null === (r = t.classList) || void 0 === r ? void 0 : r.contains(\"lenis\")) && !(null === (l = t.classList) || void 0 === l ? void 0 : l.contains(\"lenis-stopped\"));\n      })) return;\n      if (this.isStopped || this.isLocked) return void i.preventDefault();\n      if (this.isSmooth = this.options.syncTouch && s || this.options.smoothWheel && o, !this.isSmooth) return this.isScrolling = !1, void this.animate.stop();\n      i.preventDefault();\n      let h = e;\n      \"both\" === this.options.gestureOrientation ? h = Math.abs(e) > Math.abs(t) ? e : t : \"horizontal\" === this.options.gestureOrientation && (h = t);\n      const a = s && this.options.syncTouch,\n        c = s && \"touchend\" === i.type && Math.abs(h) > 5;\n      c && (h = this.velocity * this.options.touchInertiaMultiplier), this.scrollTo(this.targetScroll + h, Object.assign({\n        programmatic: !1\n      }, a ? {\n        lerp: c ? this.options.syncTouchLerp : 1\n      } : {\n        lerp: this.options.lerp,\n        duration: this.options.duration,\n        easing: this.options.easing\n      }));\n    }, this.onNativeScroll = () => {\n      if (!this.__preventNextScrollEvent && !this.isScrolling) {\n        const t = this.animatedScroll;\n        this.animatedScroll = this.targetScroll = this.actualScroll, this.velocity = 0, this.direction = Math.sign(this.animatedScroll - t), this.emit();\n      }\n    }, window.lenisVersion = \"1.0.42\", t !== document.documentElement && t !== document.body || (t = window), this.options = {\n      wrapper: t,\n      content: e,\n      wheelEventsTarget: i,\n      eventsTarget: s,\n      smoothWheel: o,\n      syncTouch: n,\n      syncTouchLerp: r,\n      touchInertiaMultiplier: l,\n      duration: h,\n      easing: a,\n      lerp: c,\n      infinite: d,\n      gestureOrientation: u,\n      orientation: p,\n      touchMultiplier: m,\n      wheelMultiplier: v,\n      autoResize: g,\n      __experimental__naiveDimensions: S\n    }, this.animate = new Animate(), this.emitter = new Emitter(), this.dimensions = new Dimensions({\n      wrapper: t,\n      content: e,\n      autoResize: g\n    }), this.toggleClassName(\"lenis\", !0), this.velocity = 0, this.isLocked = !1, this.isStopped = !1, this.isSmooth = n || o, this.isScrolling = !1, this.targetScroll = this.animatedScroll = this.actualScroll, this.options.wrapper.addEventListener(\"scroll\", this.onNativeScroll, !1), this.virtualScroll = new VirtualScroll(s, {\n      touchMultiplier: m,\n      wheelMultiplier: v\n    }), this.virtualScroll.on(\"scroll\", this.onVirtualScroll);\n  }\n  destroy() {\n    this.emitter.destroy(), this.options.wrapper.removeEventListener(\"scroll\", this.onNativeScroll, !1), this.virtualScroll.destroy(), this.dimensions.destroy(), this.toggleClassName(\"lenis\", !1), this.toggleClassName(\"lenis-smooth\", !1), this.toggleClassName(\"lenis-scrolling\", !1), this.toggleClassName(\"lenis-stopped\", !1), this.toggleClassName(\"lenis-locked\", !1);\n  }\n  on(t, e) {\n    return this.emitter.on(t, e);\n  }\n  off(t, e) {\n    return this.emitter.off(t, e);\n  }\n  setScroll(t) {\n    this.isHorizontal ? this.rootElement.scrollLeft = t : this.rootElement.scrollTop = t;\n  }\n  resize() {\n    this.dimensions.resize();\n  }\n  emit() {\n    this.emitter.emit(\"scroll\", this);\n  }\n  reset() {\n    this.isLocked = !1, this.isScrolling = !1, this.animatedScroll = this.targetScroll = this.actualScroll, this.velocity = 0, this.animate.stop();\n  }\n  start() {\n    this.isStopped && (this.isStopped = !1, this.reset());\n  }\n  stop() {\n    this.isStopped || (this.isStopped = !0, this.animate.stop(), this.reset());\n  }\n  raf(t) {\n    const e = t - (this.time || t);\n    this.time = t, this.animate.advance(.001 * e);\n  }\n  scrollTo(e, {\n    offset: i = 0,\n    immediate: s = !1,\n    lock: o = !1,\n    duration: n = this.options.duration,\n    easing: r = this.options.easing,\n    lerp: l = !n && this.options.lerp,\n    onComplete: h,\n    force: a = !1,\n    programmatic: c = !0\n  } = {}) {\n    if (!this.isStopped && !this.isLocked || a) {\n      if ([\"top\", \"left\", \"start\"].includes(e)) e = 0;else if ([\"bottom\", \"right\", \"end\"].includes(e)) e = this.limit;else {\n        let t;\n        if (\"string\" == typeof e ? t = document.querySelector(e) : (null == e ? void 0 : e.nodeType) && (t = e), t) {\n          if (this.options.wrapper !== window) {\n            const t = this.options.wrapper.getBoundingClientRect();\n            i -= this.isHorizontal ? t.left : t.top;\n          }\n          const s = t.getBoundingClientRect();\n          e = (this.isHorizontal ? s.left : s.top) + this.animatedScroll;\n        }\n      }\n      if (\"number\" == typeof e) {\n        if (e += i, e = Math.round(e), this.options.infinite ? c && (this.targetScroll = this.animatedScroll = this.scroll) : e = t(0, e, this.limit), s) return this.animatedScroll = this.targetScroll = e, this.setScroll(this.scroll), this.reset(), void (null == h || h(this));\n        if (!c) {\n          if (e === this.targetScroll) return;\n          this.targetScroll = e;\n        }\n        this.animate.fromTo(this.animatedScroll, e, {\n          duration: n,\n          easing: r,\n          lerp: l,\n          onStart: () => {\n            o && (this.isLocked = !0), this.isScrolling = !0;\n          },\n          onUpdate: (t, e) => {\n            this.isScrolling = !0, this.velocity = t - this.animatedScroll, this.direction = Math.sign(this.velocity), this.animatedScroll = t, this.setScroll(this.scroll), c && (this.targetScroll = t), e || this.emit(), e && (this.reset(), this.emit(), null == h || h(this), this.__preventNextScrollEvent = !0, requestAnimationFrame(() => {\n              delete this.__preventNextScrollEvent;\n            }));\n          }\n        });\n      }\n    }\n  }\n  get rootElement() {\n    return this.options.wrapper === window ? document.documentElement : this.options.wrapper;\n  }\n  get limit() {\n    return this.options.__experimental__naiveDimensions ? this.isHorizontal ? this.rootElement.scrollWidth - this.rootElement.clientWidth : this.rootElement.scrollHeight - this.rootElement.clientHeight : this.dimensions.limit[this.isHorizontal ? \"x\" : \"y\"];\n  }\n  get isHorizontal() {\n    return \"horizontal\" === this.options.orientation;\n  }\n  get actualScroll() {\n    return this.isHorizontal ? this.rootElement.scrollLeft : this.rootElement.scrollTop;\n  }\n  get scroll() {\n    return this.options.infinite ? (t = this.animatedScroll, e = this.limit, (t % e + e) % e) : this.animatedScroll;\n    var t, e;\n  }\n  get progress() {\n    return 0 === this.limit ? 1 : this.scroll / this.limit;\n  }\n  get isSmooth() {\n    return this.__isSmooth;\n  }\n  set isSmooth(t) {\n    this.__isSmooth !== t && (this.__isSmooth = t, this.toggleClassName(\"lenis-smooth\", t));\n  }\n  get isScrolling() {\n    return this.__isScrolling;\n  }\n  set isScrolling(t) {\n    this.__isScrolling !== t && (this.__isScrolling = t, this.toggleClassName(\"lenis-scrolling\", t));\n  }\n  get isStopped() {\n    return this.__isStopped;\n  }\n  set isStopped(t) {\n    this.__isStopped !== t && (this.__isStopped = t, this.toggleClassName(\"lenis-stopped\", t));\n  }\n  get isLocked() {\n    return this.__isLocked;\n  }\n  set isLocked(t) {\n    this.__isLocked !== t && (this.__isLocked = t, this.toggleClassName(\"lenis-locked\", t));\n  }\n  get className() {\n    let t = \"lenis\";\n    return this.isStopped && (t += \" lenis-stopped\"), this.isLocked && (t += \" lenis-locked\"), this.isScrolling && (t += \" lenis-scrolling\"), this.isSmooth && (t += \" lenis-smooth\"), t;\n  }\n  toggleClassName(t, e) {\n    this.rootElement.classList.toggle(t, e), this.emitter.emit(\"className change\", this);\n  }\n}\nexport { Lenis as default };", "map": {"version": 3, "names": ["t", "e", "i", "Math", "max", "min", "Animate", "advance", "isRunning", "lerp", "value", "s", "o", "to", "n", "r", "exp", "round", "currentTime", "duration", "easing", "from", "onUpdate", "stop", "fromTo", "onStart", "Dimensions", "constructor", "wrapper", "content", "autoResize", "debounce", "debouncedResize", "arguments", "clearTimeout", "setTimeout", "apply", "resize", "window", "addEventListener", "wrapperResizeObserver", "ResizeObserver", "observe", "contentResizeObserver", "destroy", "disconnect", "removeEventListener", "onWrapperResize", "onContentResize", "width", "innerWidth", "height", "innerHeight", "clientWidth", "clientHeight", "scrollHeight", "scrollWidth", "limit", "x", "y", "Emitter", "events", "emit", "length", "on", "push", "filter", "off", "VirtualScroll", "wheelMultiplier", "touchMultiplier", "element", "touchStart", "emitter", "onWindowResize", "onWheel", "passive", "onTouchStart", "onTouchMove", "onTouchEnd", "clientX", "clientY", "targetTouches", "<PERSON><PERSON><PERSON><PERSON>", "deltaX", "deltaY", "event", "deltaMode", "windowWidth", "windowHeight", "<PERSON><PERSON>", "document", "documentElement", "wheelEventsTarget", "eventsTarget", "smoothWheel", "syncTouch", "syncTouchLerp", "touchInertiaMultiplier", "l", "h", "a", "pow", "c", "infinite", "d", "orientation", "p", "gestureOrientation", "u", "m", "v", "g", "__experimental__naiveDimensions", "S", "__isSmooth", "__isScrolling", "__isStopped", "__isLocked", "onVirtualScroll", "ctrl<PERSON>ey", "type", "includes", "options", "isStopped", "isLocked", "reset", "<PERSON><PERSON><PERSON>", "slice", "indexOf", "rootElement", "find", "hasAttribute", "call", "classList", "contains", "preventDefault", "isSmooth", "isScrolling", "animate", "abs", "velocity", "scrollTo", "targetScroll", "Object", "assign", "programmatic", "onNativeScroll", "__preventNextScrollEvent", "animatedScroll", "actualScroll", "direction", "sign", "lenisVersion", "body", "dimensions", "toggleClassName", "virtualScroll", "setScroll", "isHorizontal", "scrollLeft", "scrollTop", "start", "raf", "time", "offset", "immediate", "lock", "onComplete", "force", "querySelector", "nodeType", "getBoundingClientRect", "left", "top", "scroll", "requestAnimationFrame", "progress", "className", "toggle", "default"], "sources": ["/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/node_modules/@studio-freight/lenis/src/maths.js", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/node_modules/@studio-freight/lenis/src/animate.js", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/node_modules/@studio-freight/lenis/src/dimensions.js", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/node_modules/@studio-freight/lenis/src/debounce.js", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/node_modules/@studio-freight/lenis/src/emitter.js", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/node_modules/@studio-freight/lenis/src/virtual-scroll.js", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/node_modules/@studio-freight/src/index.ts"], "sourcesContent": ["// Clamp a value between a minimum and maximum value\r\nexport function clamp(min, input, max) {\r\n  return Math.max(min, Math.min(input, max))\r\n}\r\n\r\n// Truncate a floating-point number to a specified number of decimal places\r\nexport function truncate(value, decimals = 0) {\r\n  return parseFloat(value.toFixed(decimals))\r\n}\r\n\r\n// Linearly interpolate between two values using an amount (0 <= t <= 1)\r\nexport function lerp(x, y, t) {\r\n  return (1 - t) * x + t * y\r\n}\r\n\r\n// http://www.rorydriscoll.com/2016/03/07/frame-rate-independent-damping-using-lerp/\r\nexport function damp(x, y, lambda, dt) {\r\n  return lerp(x, y, 1 - Math.exp(-lambda * dt))\r\n}\r\n\r\n// Calculate the modulo of the dividend and divisor while keeping the result within the same sign as the divisor\r\n// https://anguscroll.com/just/just-modulo\r\nexport function modulo(n, d) {\r\n  return ((n % d) + d) % d\r\n}\r\n", "import { clamp, damp } from './maths'\r\n\r\n// Animate class to handle value animations with lerping or easing\r\nexport class Animate {\r\n  // Advance the animation by the given delta time\r\n  advance(deltaTime) {\r\n    if (!this.isRunning) return\r\n\r\n    let completed = false\r\n\r\n    if (this.lerp) {\r\n      this.value = damp(this.value, this.to, this.lerp * 60, deltaTime)\r\n      if (Math.round(this.value) === this.to) {\r\n        this.value = this.to\r\n        completed = true\r\n      }\r\n    } else {\r\n      this.currentTime += deltaTime\r\n      const linearProgress = clamp(0, this.currentTime / this.duration, 1)\r\n\r\n      completed = linearProgress >= 1\r\n      const easedProgress = completed ? 1 : this.easing(linearProgress)\r\n      this.value = this.from + (this.to - this.from) * easedProgress\r\n    }\r\n\r\n    // Call the onUpdate callback with the current value and completed status\r\n    this.onUpdate?.(this.value, completed)\r\n\r\n    if (completed) {\r\n      this.stop()\r\n    }\r\n  }\r\n\r\n  // Stop the animation\r\n  stop() {\r\n    this.isRunning = false\r\n  }\r\n\r\n  // Set up the animation from a starting value to an ending value\r\n  // with optional parameters for lerping, duration, easing, and onUpdate callback\r\n  fromTo(\r\n    from,\r\n    to,\r\n    { lerp = 0.1, duration = 1, easing = (t) => t, onStart, onUpdate }\r\n  ) {\r\n    this.from = this.value = from\r\n    this.to = to\r\n    this.lerp = lerp\r\n    this.duration = duration\r\n    this.easing = easing\r\n    this.currentTime = 0\r\n    this.isRunning = true\r\n\r\n    onStart?.()\r\n    this.onUpdate = onUpdate\r\n  }\r\n}\r\n", "import { debounce } from './debounce'\r\n\r\nexport class Dimensions {\r\n  constructor({\r\n    wrapper,\r\n    content,\r\n    autoResize = true,\r\n    debounce: debounceValue = 250,\r\n  } = {}) {\r\n    this.wrapper = wrapper\r\n    this.content = content\r\n\r\n    if (autoResize) {\r\n      this.debouncedResize = debounce(this.resize, debounceValue)\r\n\r\n      if (this.wrapper === window) {\r\n        window.addEventListener('resize', this.debouncedResize, false)\r\n      } else {\r\n        this.wrapperResizeObserver = new ResizeObserver(this.debouncedResize)\r\n        this.wrapperResizeObserver.observe(this.wrapper)\r\n      }\r\n\r\n      this.contentResizeObserver = new ResizeObserver(this.debouncedResize)\r\n      this.contentResizeObserver.observe(this.content)\r\n    }\r\n\r\n    this.resize()\r\n  }\r\n\r\n  destroy() {\r\n    this.wrapperResizeObserver?.disconnect()\r\n    this.contentResizeObserver?.disconnect()\r\n    window.removeEventListener('resize', this.debouncedResize, false)\r\n  }\r\n\r\n  resize = () => {\r\n    this.onWrapperResize()\r\n    this.onContentResize()\r\n  }\r\n\r\n  onWrapperResize = () => {\r\n    if (this.wrapper === window) {\r\n      this.width = window.innerWidth\r\n      this.height = window.innerHeight\r\n    } else {\r\n      this.width = this.wrapper.clientWidth\r\n      this.height = this.wrapper.clientHeight\r\n    }\r\n  }\r\n\r\n  onContentResize = () => {\r\n    if (this.wrapper === window) {\r\n      this.scrollHeight = this.content.scrollHeight\r\n      this.scrollWidth = this.content.scrollWidth\r\n    } else {\r\n      this.scrollHeight = this.wrapper.scrollHeight\r\n      this.scrollWidth = this.wrapper.scrollWidth\r\n    }\r\n  }\r\n\r\n  get limit() {\r\n    return {\r\n      x: this.scrollWidth - this.width,\r\n      y: this.scrollHeight - this.height,\r\n    }\r\n  }\r\n}\r\n", "export function debounce(callback, delay) {\r\n  let timer\r\n  return function () {\r\n    let args = arguments\r\n    let context = this\r\n    clearTimeout(timer)\r\n    timer = setTimeout(function () {\r\n      callback.apply(context, args)\r\n    }, delay)\r\n  }\r\n}\r\n", "export class Emitter {\r\n  constructor() {\r\n    this.events = {}\r\n  }\r\n\r\n  emit(event, ...args) {\r\n    let callbacks = this.events[event] || []\r\n    for (let i = 0, length = callbacks.length; i < length; i++) {\r\n      callbacks[i](...args)\r\n    }\r\n  }\r\n\r\n  on(event, cb) {\r\n    // Add the callback to the event's callback list, or create a new list with the callback\r\n    this.events[event]?.push(cb) || (this.events[event] = [cb])\r\n\r\n    // Return an unsubscribe function\r\n    return () => {\r\n      this.events[event] = this.events[event]?.filter((i) => cb !== i)\r\n    }\r\n  }\r\n\r\n  off(event, callback) {\r\n    this.events[event] = this.events[event]?.filter((i) => callback !== i)\r\n  }\r\n\r\n  destroy() {\r\n    this.events = {}\r\n  }\r\n}\r\n", "import { Emitter } from './emitter'\r\n\r\nconst LINE_HEIGHT = 100 / 6\r\n\r\nexport class VirtualScroll {\r\n  constructor(element, { wheelMultiplier = 1, touchMultiplier = 1 }) {\r\n    this.element = element\r\n    this.wheelMultiplier = wheelMultiplier\r\n    this.touchMultiplier = touchMultiplier\r\n\r\n    this.touchStart = {\r\n      x: null,\r\n      y: null,\r\n    }\r\n\r\n    this.emitter = new Emitter()\r\n    window.addEventListener('resize', this.onWindowResize, false)\r\n    this.onWindowResize()\r\n\r\n    this.element.addEventListener('wheel', this.onWheel, { passive: false })\r\n    this.element.addEventListener('touchstart', this.onTouchStart, {\r\n      passive: false,\r\n    })\r\n    this.element.addEventListener('touchmove', this.onTouchMove, {\r\n      passive: false,\r\n    })\r\n    this.element.addEventListener('touchend', this.onTouchEnd, {\r\n      passive: false,\r\n    })\r\n  }\r\n\r\n  // Add an event listener for the given event and callback\r\n  on(event, callback) {\r\n    return this.emitter.on(event, callback)\r\n  }\r\n\r\n  // Remove all event listeners and clean up\r\n  destroy() {\r\n    this.emitter.destroy()\r\n\r\n    window.removeEventListener('resize', this.onWindowResize, false)\r\n\r\n    this.element.removeEventListener('wheel', this.onWheel, {\r\n      passive: false,\r\n    })\r\n    this.element.removeEventListener('touchstart', this.onTouchStart, {\r\n      passive: false,\r\n    })\r\n    this.element.removeEventListener('touchmove', this.onTouchMove, {\r\n      passive: false,\r\n    })\r\n    this.element.removeEventListener('touchend', this.onTouchEnd, {\r\n      passive: false,\r\n    })\r\n  }\r\n\r\n  // Event handler for 'touchstart' event\r\n  onTouchStart = (event) => {\r\n    const { clientX, clientY } = event.targetTouches\r\n      ? event.targetTouches[0]\r\n      : event\r\n\r\n    this.touchStart.x = clientX\r\n    this.touchStart.y = clientY\r\n\r\n    this.lastDelta = {\r\n      x: 0,\r\n      y: 0,\r\n    }\r\n\r\n    this.emitter.emit('scroll', {\r\n      deltaX: 0,\r\n      deltaY: 0,\r\n      event,\r\n    })\r\n  }\r\n\r\n  // Event handler for 'touchmove' event\r\n  onTouchMove = (event) => {\r\n    const { clientX, clientY } = event.targetTouches\r\n      ? event.targetTouches[0]\r\n      : event\r\n\r\n    const deltaX = -(clientX - this.touchStart.x) * this.touchMultiplier\r\n    const deltaY = -(clientY - this.touchStart.y) * this.touchMultiplier\r\n\r\n    this.touchStart.x = clientX\r\n    this.touchStart.y = clientY\r\n\r\n    this.lastDelta = {\r\n      x: deltaX,\r\n      y: deltaY,\r\n    }\r\n\r\n    this.emitter.emit('scroll', {\r\n      deltaX,\r\n      deltaY,\r\n      event,\r\n    })\r\n  }\r\n\r\n  onTouchEnd = (event) => {\r\n    this.emitter.emit('scroll', {\r\n      deltaX: this.lastDelta.x,\r\n      deltaY: this.lastDelta.y,\r\n      event,\r\n    })\r\n  }\r\n\r\n  // Event handler for 'wheel' event\r\n  onWheel = (event) => {\r\n    let { deltaX, deltaY, deltaMode } = event\r\n\r\n    const multiplierX =\r\n      deltaMode === 1 ? LINE_HEIGHT : deltaMode === 2 ? this.windowWidth : 1\r\n    const multiplierY =\r\n      deltaMode === 1 ? LINE_HEIGHT : deltaMode === 2 ? this.windowHeight : 1\r\n\r\n    deltaX *= multiplierX\r\n    deltaY *= multiplierY\r\n\r\n    deltaX *= this.wheelMultiplier\r\n    deltaY *= this.wheelMultiplier\r\n\r\n    this.emitter.emit('scroll', { deltaX, deltaY, event })\r\n  }\r\n\r\n  onWindowResize = () => {\r\n    this.windowWidth = window.innerWidth\r\n    this.windowHeight = window.innerHeight\r\n  }\r\n}\r\n", "import { version } from '../package.json'\r\nimport { Animate } from './animate'\r\nimport { Dimensions } from './dimensions'\r\nimport { Emitter } from './emitter'\r\nimport { clamp, modulo } from './maths'\r\nimport { VirtualScroll } from './virtual-scroll'\r\n\r\n// Technical explanation\r\n// - listen to 'wheel' events\r\n// - prevent 'wheel' event to prevent scroll\r\n// - normalize wheel delta\r\n// - add delta to targetScroll\r\n// - animate scroll to targetScroll (smooth context)\r\n// - if animation is not running, listen to 'scroll' events (native context)\r\n\r\ntype EasingFunction = (t: number) => number\r\ntype Orientation = 'vertical' | 'horizontal'\r\ntype GestureOrientation = 'vertical' | 'horizontal' | 'both'\r\n\r\nexport type LenisOptions = {\r\n  wrapper?: Window | HTMLElement\r\n  content?: HTMLElement\r\n  wheelEventsTarget?: Window | HTMLElement\r\n  eventsTarget?: Window | HTMLElement\r\n  smoothWheel?: boolean\r\n  syncTouch?: boolean\r\n  syncTouchLerp?: number\r\n  touchInertiaMultiplier?: number\r\n  duration?: number\r\n  easing?: EasingFunction\r\n  lerp?: number\r\n  infinite?: boolean\r\n  orientation?: Orientation\r\n  gestureOrientation?: GestureOrientation\r\n  touchMultiplier?: number\r\n  wheelMultiplier?: number\r\n  autoResize?: boolean\r\n  __experimental__naiveDimensions?: boolean\r\n}\r\n\r\nexport default class Lenis {\r\n  __isSmooth: boolean = false // true if scroll should be animated\r\n  __isScrolling: boolean = false // true when scroll is animating\r\n  __isStopped: boolean = false // true if user should not be able to scroll - enable/disable programmatically\r\n  __isLocked: boolean = false // same as isStopped but enabled/disabled when scroll reaches target\r\n\r\n  constructor({\r\n    wrapper = window,\r\n    content = document.documentElement,\r\n    wheelEventsTarget = wrapper, // deprecated\r\n    eventsTarget = wheelEventsTarget,\r\n    smoothWheel = true,\r\n    syncTouch = false,\r\n    syncTouchLerp = 0.075,\r\n    touchInertiaMultiplier = 35,\r\n    duration, // in seconds\r\n    easing = (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),\r\n    lerp = !duration && 0.1,\r\n    infinite = false,\r\n    orientation = 'vertical', // vertical, horizontal\r\n    gestureOrientation = 'vertical', // vertical, horizontal, both\r\n    touchMultiplier = 1,\r\n    wheelMultiplier = 1,\r\n    autoResize = true,\r\n    __experimental__naiveDimensions = false,\r\n  }: LenisOptions = {}) {\r\n    window.lenisVersion = version\r\n\r\n    // if wrapper is html or body, fallback to window\r\n    if (wrapper === document.documentElement || wrapper === document.body) {\r\n      wrapper = window\r\n    }\r\n\r\n    this.options = {\r\n      wrapper,\r\n      content,\r\n      wheelEventsTarget,\r\n      eventsTarget,\r\n      smoothWheel,\r\n      syncTouch,\r\n      syncTouchLerp,\r\n      touchInertiaMultiplier,\r\n      duration,\r\n      easing,\r\n      lerp,\r\n      infinite,\r\n      gestureOrientation,\r\n      orientation,\r\n      touchMultiplier,\r\n      wheelMultiplier,\r\n      autoResize,\r\n      __experimental__naiveDimensions,\r\n    }\r\n\r\n    this.animate = new Animate()\r\n    this.emitter = new Emitter()\r\n    this.dimensions = new Dimensions({ wrapper, content, autoResize })\r\n    this.toggleClassName('lenis', true)\r\n\r\n    this.velocity = 0\r\n    this.isLocked = false\r\n    this.isStopped = false\r\n    this.isSmooth = syncTouch || smoothWheel\r\n    this.isScrolling = false\r\n    this.targetScroll = this.animatedScroll = this.actualScroll\r\n\r\n    this.options.wrapper.addEventListener('scroll', this.onNativeScroll, false)\r\n\r\n    this.virtualScroll = new VirtualScroll(eventsTarget, {\r\n      touchMultiplier,\r\n      wheelMultiplier,\r\n    })\r\n    this.virtualScroll.on('scroll', this.onVirtualScroll)\r\n  }\r\n\r\n  destroy() {\r\n    this.emitter.destroy()\r\n\r\n    this.options.wrapper.removeEventListener(\r\n      'scroll',\r\n      this.onNativeScroll,\r\n      false\r\n    )\r\n\r\n    this.virtualScroll.destroy()\r\n    this.dimensions.destroy()\r\n\r\n    this.toggleClassName('lenis', false)\r\n    this.toggleClassName('lenis-smooth', false)\r\n    this.toggleClassName('lenis-scrolling', false)\r\n    this.toggleClassName('lenis-stopped', false)\r\n    this.toggleClassName('lenis-locked', false)\r\n  }\r\n\r\n  on(event: string, callback: Function) {\r\n    return this.emitter.on(event, callback)\r\n  }\r\n\r\n  off(event: string, callback: Function) {\r\n    return this.emitter.off(event, callback)\r\n  }\r\n\r\n  private setScroll(scroll) {\r\n    // apply scroll value immediately\r\n    if (this.isHorizontal) {\r\n      this.rootElement.scrollLeft = scroll\r\n    } else {\r\n      this.rootElement.scrollTop = scroll\r\n    }\r\n  }\r\n\r\n  private onVirtualScroll = ({ deltaX, deltaY, event }) => {\r\n    // keep zoom feature\r\n    if (event.ctrlKey) return\r\n\r\n    const isTouch = event.type.includes('touch')\r\n    const isWheel = event.type.includes('wheel')\r\n\r\n    const isTapToStop =\r\n      this.options.syncTouch &&\r\n      isTouch &&\r\n      event.type === 'touchstart' &&\r\n      !this.isStopped &&\r\n      !this.isLocked\r\n\r\n    if (isTapToStop) {\r\n      this.reset()\r\n      return\r\n    }\r\n\r\n    const isClick = deltaX === 0 && deltaY === 0 // click event\r\n\r\n    // const isPullToRefresh =\r\n    //   this.options.gestureOrientation === 'vertical' &&\r\n    //   this.scroll === 0 &&\r\n    //   !this.options.infinite &&\r\n    //   deltaY <= 5 // touch pull to refresh, not reliable yet\r\n\r\n    const isUnknownGesture =\r\n      (this.options.gestureOrientation === 'vertical' && deltaY === 0) ||\r\n      (this.options.gestureOrientation === 'horizontal' && deltaX === 0)\r\n\r\n    if (isClick || isUnknownGesture) {\r\n      // console.log('prevent')\r\n      return\r\n    }\r\n\r\n    // catch if scrolling on nested scroll elements\r\n    let composedPath = event.composedPath()\r\n    composedPath = composedPath.slice(0, composedPath.indexOf(this.rootElement)) // remove parents elements\r\n\r\n    if (\r\n      !!composedPath.find(\r\n        (node) =>\r\n          node.hasAttribute?.('data-lenis-prevent') ||\r\n          (isTouch && node.hasAttribute?.('data-lenis-prevent-touch')) ||\r\n          (isWheel && node.hasAttribute?.('data-lenis-prevent-wheel')) ||\r\n          (node.classList?.contains('lenis') &&\r\n            !node.classList?.contains('lenis-stopped')) // nested lenis instance\r\n      )\r\n    )\r\n      return\r\n\r\n    if (this.isStopped || this.isLocked) {\r\n      event.preventDefault() // this will stop forwarding the event to the parent, this is problematic\r\n      return\r\n    }\r\n\r\n    this.isSmooth =\r\n      (this.options.syncTouch && isTouch) ||\r\n      (this.options.smoothWheel && isWheel)\r\n\r\n    if (!this.isSmooth) {\r\n      this.isScrolling = false\r\n      this.animate.stop()\r\n      return\r\n    }\r\n\r\n    event.preventDefault()\r\n\r\n    let delta = deltaY\r\n    if (this.options.gestureOrientation === 'both') {\r\n      delta = Math.abs(deltaY) > Math.abs(deltaX) ? deltaY : deltaX\r\n    } else if (this.options.gestureOrientation === 'horizontal') {\r\n      delta = deltaX\r\n    }\r\n\r\n    const syncTouch = isTouch && this.options.syncTouch\r\n    const isTouchEnd = isTouch && event.type === 'touchend'\r\n\r\n    const hasTouchInertia = isTouchEnd && Math.abs(delta) > 5\r\n\r\n    if (hasTouchInertia) {\r\n      delta = this.velocity * this.options.touchInertiaMultiplier\r\n    }\r\n\r\n    this.scrollTo(this.targetScroll + delta, {\r\n      programmatic: false,\r\n      ...(syncTouch\r\n        ? {\r\n            lerp: hasTouchInertia ? this.options.syncTouchLerp : 1,\r\n          }\r\n        : {\r\n            lerp: this.options.lerp,\r\n            duration: this.options.duration,\r\n            easing: this.options.easing,\r\n          }),\r\n    })\r\n  }\r\n\r\n  resize() {\r\n    this.dimensions.resize()\r\n  }\r\n\r\n  private emit() {\r\n    this.emitter.emit('scroll', this)\r\n  }\r\n\r\n  private onNativeScroll = () => {\r\n    if (this.__preventNextScrollEvent) return\r\n\r\n    if (!this.isScrolling) {\r\n      const lastScroll = this.animatedScroll\r\n      this.animatedScroll = this.targetScroll = this.actualScroll\r\n      this.velocity = 0\r\n      this.direction = Math.sign(this.animatedScroll - lastScroll)\r\n      this.emit()\r\n    }\r\n  }\r\n\r\n  private reset() {\r\n    this.isLocked = false\r\n    this.isScrolling = false\r\n    this.animatedScroll = this.targetScroll = this.actualScroll\r\n    this.velocity = 0\r\n    this.animate.stop()\r\n  }\r\n\r\n  start() {\r\n    if (!this.isStopped) return\r\n    this.isStopped = false\r\n\r\n    this.reset()\r\n  }\r\n\r\n  stop() {\r\n    if (this.isStopped) return\r\n    this.isStopped = true\r\n    this.animate.stop()\r\n\r\n    this.reset()\r\n  }\r\n\r\n  raf(time: number) {\r\n    const deltaTime = time - (this.time || time)\r\n    this.time = time\r\n\r\n    this.animate.advance(deltaTime * 0.001)\r\n  }\r\n\r\n  scrollTo(\r\n    target: number | string | HTMLElement,\r\n    {\r\n      offset = 0,\r\n      immediate = false,\r\n      lock = false,\r\n      duration = this.options.duration,\r\n      easing = this.options.easing,\r\n      lerp = !duration && this.options.lerp,\r\n      onComplete,\r\n      force = false, // scroll even if stopped\r\n      programmatic = true, // called from outside of the class\r\n    }: {\r\n      offset?: number\r\n      immediate?: boolean\r\n      lock?: boolean\r\n      duration?: number\r\n      easing?: EasingFunction\r\n      lerp?: number\r\n      onComplete?: (lenis: Lenis) => void\r\n      force?: boolean\r\n      programmatic?: boolean\r\n    } = {}\r\n  ) {\r\n    if ((this.isStopped || this.isLocked) && !force) return\r\n\r\n    // keywords\r\n    if (['top', 'left', 'start'].includes(target)) {\r\n      target = 0\r\n    } else if (['bottom', 'right', 'end'].includes(target)) {\r\n      target = this.limit\r\n    } else {\r\n      let node\r\n\r\n      if (typeof target === 'string') {\r\n        // CSS selector\r\n        node = document.querySelector(target)\r\n      } else if (target?.nodeType) {\r\n        // Node element\r\n        node = target\r\n      }\r\n\r\n      if (node) {\r\n        if (this.options.wrapper !== window) {\r\n          // nested scroll offset correction\r\n          const wrapperRect = this.options.wrapper.getBoundingClientRect()\r\n          offset -= this.isHorizontal ? wrapperRect.left : wrapperRect.top\r\n        }\r\n\r\n        const rect = node.getBoundingClientRect()\r\n\r\n        target =\r\n          (this.isHorizontal ? rect.left : rect.top) + this.animatedScroll\r\n      }\r\n    }\r\n\r\n    if (typeof target !== 'number') return\r\n\r\n    target += offset\r\n    target = Math.round(target)\r\n\r\n    if (this.options.infinite) {\r\n      if (programmatic) {\r\n        this.targetScroll = this.animatedScroll = this.scroll\r\n      }\r\n    } else {\r\n      target = clamp(0, target, this.limit)\r\n    }\r\n\r\n    if (immediate) {\r\n      this.animatedScroll = this.targetScroll = target\r\n      this.setScroll(this.scroll)\r\n      this.reset()\r\n      onComplete?.(this)\r\n      return\r\n    }\r\n\r\n    if (!programmatic) {\r\n      if (target === this.targetScroll) return\r\n\r\n      this.targetScroll = target\r\n    }\r\n\r\n    this.animate.fromTo(this.animatedScroll, target, {\r\n      duration,\r\n      easing,\r\n      lerp,\r\n      onStart: () => {\r\n        // started\r\n        if (lock) this.isLocked = true\r\n        this.isScrolling = true\r\n      },\r\n      onUpdate: (value: number, completed: boolean) => {\r\n        this.isScrolling = true\r\n\r\n        // updated\r\n        this.velocity = value - this.animatedScroll\r\n        this.direction = Math.sign(this.velocity)\r\n\r\n        this.animatedScroll = value\r\n        this.setScroll(this.scroll)\r\n\r\n        if (programmatic) {\r\n          // wheel during programmatic should stop it\r\n          this.targetScroll = value\r\n        }\r\n\r\n        if (!completed) this.emit()\r\n\r\n        if (completed) {\r\n          this.reset()\r\n          this.emit()\r\n          onComplete?.(this)\r\n\r\n          // avoid emitting event twice\r\n          this.__preventNextScrollEvent = true\r\n          requestAnimationFrame(() => {\r\n            delete this.__preventNextScrollEvent\r\n          })\r\n        }\r\n      },\r\n    })\r\n  }\r\n\r\n  get rootElement() {\r\n    return this.options.wrapper === window\r\n      ? document.documentElement\r\n      : this.options.wrapper\r\n  }\r\n\r\n  get limit() {\r\n    if (this.options.__experimental__naiveDimensions) {\r\n      if (this.isHorizontal) {\r\n        return this.rootElement.scrollWidth - this.rootElement.clientWidth\r\n      } else {\r\n        return this.rootElement.scrollHeight - this.rootElement.clientHeight\r\n      }\r\n    } else {\r\n      return this.dimensions.limit[this.isHorizontal ? 'x' : 'y']\r\n    }\r\n  }\r\n\r\n  get isHorizontal() {\r\n    return this.options.orientation === 'horizontal'\r\n  }\r\n\r\n  get actualScroll() {\r\n    // value browser takes into account\r\n    return this.isHorizontal\r\n      ? this.rootElement.scrollLeft\r\n      : this.rootElement.scrollTop\r\n  }\r\n\r\n  get scroll() {\r\n    return this.options.infinite\r\n      ? modulo(this.animatedScroll, this.limit)\r\n      : this.animatedScroll\r\n  }\r\n\r\n  get progress() {\r\n    // avoid progress to be NaN\r\n    return this.limit === 0 ? 1 : this.scroll / this.limit\r\n  }\r\n\r\n  get isSmooth() {\r\n    return this.__isSmooth\r\n  }\r\n\r\n  private set isSmooth(value: boolean) {\r\n    if (this.__isSmooth !== value) {\r\n      this.__isSmooth = value\r\n      this.toggleClassName('lenis-smooth', value)\r\n    }\r\n  }\r\n\r\n  get isScrolling() {\r\n    return this.__isScrolling\r\n  }\r\n\r\n  private set isScrolling(value: boolean) {\r\n    if (this.__isScrolling !== value) {\r\n      this.__isScrolling = value\r\n      this.toggleClassName('lenis-scrolling', value)\r\n    }\r\n  }\r\n\r\n  get isStopped() {\r\n    return this.__isStopped\r\n  }\r\n\r\n  private set isStopped(value: boolean) {\r\n    if (this.__isStopped !== value) {\r\n      this.__isStopped = value\r\n      this.toggleClassName('lenis-stopped', value)\r\n    }\r\n  }\r\n\r\n  get isLocked() {\r\n    return this.__isLocked\r\n  }\r\n\r\n  private set isLocked(value: boolean) {\r\n    if (this.__isLocked !== value) {\r\n      this.__isLocked = value\r\n      this.toggleClassName('lenis-locked', value)\r\n    }\r\n  }\r\n\r\n  get className() {\r\n    let className = 'lenis'\r\n    if (this.isStopped) className += ' lenis-stopped'\r\n    if (this.isLocked) className += ' lenis-locked'\r\n    if (this.isScrolling) className += ' lenis-scrolling'\r\n    if (this.isSmooth) className += ' lenis-smooth'\r\n    return className\r\n  }\r\n\r\n  private toggleClassName(name: string, value: boolean) {\r\n    this.rootElement.classList.toggle(name, value)\r\n    this.emitter.emit('className change', this)\r\n  }\r\n}\r\n"], "mappings": "AACO,SAASA,EAAMA,CAAA,EAAKC,CAAA,EAAOC,CAAA;EAChC,OAAOC,IAAA,CAAKC,GAAA,CAAIJ,CAAA,EAAKG,IAAA,CAAKE,GAAA,CAAIJ,CAAA,EAAOC,CAAA,EACvC;AAAA;ACAO,MAAMI,OAAA;EAEXC,QAAQN,CAAA;IACN,KAAK,KAAKO,SAAA,EAAW;IAErB,IAAIN,CAAA,IAAY;IAEhB,IAAI,KAAKO,IAAA,EACP,KAAKC,KAAA,IDKUC,CAAA,GCLG,KAAKD,KAAA,EDKLE,CAAA,GCLY,KAAKC,EAAA,EDKdC,CAAA,GCL8B,KAAZ,KAAKL,IAAA,EDKfM,CAAA,GCL0Bd,CAAA,EDAtD,UAAcD,CAAA,EAAGC,CAAA,EAAGC,CAAA;MACzB,QAAQ,IAAIA,CAAA,IAAKF,CAAA,GAAIE,CAAA,GAAID,CAC3B;IAAA,CAIS,CAAKU,CAAA,EAAGC,CAAA,EAAG,IAAIT,IAAA,CAAKa,GAAA,EAAKF,CAAA,GAASC,CAAA,KCLjCZ,IAAA,CAAKc,KAAA,CAAM,KAAKP,KAAA,MAAW,KAAKG,EAAA,KAClC,KAAKH,KAAA,GAAQ,KAAKG,EAAA,EAClBX,CAAA,IAAY,QAET;MACL,KAAKgB,WAAA,IAAejB,CAAA;MACpB,MAAMU,CAAA,GAAiBX,CAAA,CAAM,GAAG,KAAKkB,WAAA,GAAc,KAAKC,QAAA,EAAU;MAElEjB,CAAA,GAAYS,CAAA,IAAkB;MAC9B,MAAMC,CAAA,GAAgBV,CAAA,GAAY,IAAI,KAAKkB,MAAA,CAAOT,CAAA;MAClD,KAAKD,KAAA,GAAQ,KAAKW,IAAA,IAAQ,KAAKR,EAAA,GAAK,KAAKQ,IAAA,IAAQT,CAClD;IAAA;IDPE,IAAcD,CAAA,EAAGC,CAAA,EAAGE,CAAA,EAAQC,CAAA;ICU/B,KAAKO,QAAA,GAAW,KAAKZ,KAAA,EAAOR,CAAA,GAExBA,CAAA,IACF,KAAKqB,IAAA,EAER;EAAA;EAGDA,KAAA;IACE,KAAKf,SAAA,IAAY,CAClB;EAAA;EAIDgB,OACExB,CAAA,EACAC,CAAA;IACAQ,IAAA,EAAEP,CAAA,GAAO;IAAGiB,QAAA,EAAER,CAAA,GAAW;IAACS,MAAA,EAAER,CAAA,GAAUZ,CAAA,IAAMA,CAAA;IAACyB,OAAA,EAAEX,CAAA;IAAOQ,QAAA,EAAEP;EAAA;IAExD,KAAKM,IAAA,GAAO,KAAKX,KAAA,GAAQV,CAAA,EACzB,KAAKa,EAAA,GAAKZ,CAAA,EACV,KAAKQ,IAAA,GAAOP,CAAA,EACZ,KAAKiB,QAAA,GAAWR,CAAA,EAChB,KAAKS,MAAA,GAASR,CAAA,EACd,KAAKM,WAAA,GAAc,GACnB,KAAKV,SAAA,IAAY,GAEjBM,CAAA,MACA,KAAKQ,QAAA,GAAWP,CACjB;EAAA;AAAA;ACrDI,MAAMW,UAAA;EACXC,YAAA;IAAYC,OAAA,EACV5B,CAAA;IAAO6B,OAAA,EACP5B,CAAA;IAAO6B,UAAA,EACP5B,CAAA,IAAa;IACb6B,QAAA,EAAUpB,CAAA,GAAgB;EAAA,IACxB;IACF,KAAKiB,OAAA,GAAU5B,CAAA,EACf,KAAK6B,OAAA,GAAU5B,CAAA,EAEXC,CAAA,KACF,KAAK8B,eAAA,GCbJ,UAAkBhC,CAAA,EAAUC,CAAA;MACjC,IAAIC,CAAA;MACJ,OAAO;QACL,IAAIS,CAAA,GAAOsB,SAAA;UACPrB,CAAA,GAAU;QACdsB,YAAA,CAAahC,CAAA,GACbA,CAAA,GAAQiC,UAAA,CAAW;UACjBnC,CAAA,CAASoC,KAAA,CAAMxB,CAAA,EAASD,CAAA,CACzB;QAAA,GAAEV,CAAA,CACJ;MAAA,CACH;IAAA,CDG6B,CAAS,KAAKoC,MAAA,EAAQ1B,CAAA,GAEzC,KAAKiB,OAAA,KAAYU,MAAA,GACnBA,MAAA,CAAOC,gBAAA,CAAiB,UAAU,KAAKP,eAAA,GAAiB,MAExD,KAAKQ,qBAAA,GAAwB,IAAIC,cAAA,CAAe,KAAKT,eAAA,GACrD,KAAKQ,qBAAA,CAAsBE,OAAA,CAAQ,KAAKd,OAAA,IAG1C,KAAKe,qBAAA,GAAwB,IAAIF,cAAA,CAAe,KAAKT,eAAA,GACrD,KAAKW,qBAAA,CAAsBD,OAAA,CAAQ,KAAKb,OAAA,IAG1C,KAAKQ,MAAA,EACN;EAAA;EAEDO,QAAA;IACE,KAAKJ,qBAAA,EAAuBK,UAAA,IAC5B,KAAKF,qBAAA,EAAuBE,UAAA,IAC5BP,MAAA,CAAOQ,mBAAA,CAAoB,UAAU,KAAKd,eAAA,GAAiB,EAC5D;EAAA;EAEDK,MAAA,GAASA,CAAA;IACP,KAAKU,eAAA,IACL,KAAKC,eAAA,EAAiB;EAAA;EAGxBD,eAAA,GAAkBA,CAAA;IACZ,KAAKnB,OAAA,KAAYU,MAAA,IACnB,KAAKW,KAAA,GAAQX,MAAA,CAAOY,UAAA,EACpB,KAAKC,MAAA,GAASb,MAAA,CAAOc,WAAA,KAErB,KAAKH,KAAA,GAAQ,KAAKrB,OAAA,CAAQyB,WAAA,EAC1B,KAAKF,MAAA,GAAS,KAAKvB,OAAA,CAAQ0B,YAAA,CAC5B;EAAA;EAGHN,eAAA,GAAkBA,CAAA;IACZ,KAAKpB,OAAA,KAAYU,MAAA,IACnB,KAAKiB,YAAA,GAAe,KAAK1B,OAAA,CAAQ0B,YAAA,EACjC,KAAKC,WAAA,GAAc,KAAK3B,OAAA,CAAQ2B,WAAA,KAEhC,KAAKD,YAAA,GAAe,KAAK3B,OAAA,CAAQ2B,YAAA,EACjC,KAAKC,WAAA,GAAc,KAAK5B,OAAA,CAAQ4B,WAAA,CACjC;EAAA;EAGH,IAAAC,KAAIA,CAAA;IACF,OAAO;MACLC,CAAA,EAAG,KAAKF,WAAA,GAAc,KAAKP,KAAA;MAC3BU,CAAA,EAAG,KAAKJ,YAAA,GAAe,KAAKJ;IAAA,CAE/B;EAAA;AAAA;AEjEI,MAAMS,OAAA;EACXjC,YAAA;IACE,KAAKkC,MAAA,GAAS,CAAE,CACjB;EAAA;EAEDC,KAAK9D,CAAA,KAAUC,CAAA;IACb,IAAIC,CAAA,GAAY,KAAK2D,MAAA,CAAO7D,CAAA,KAAU;IACtC,KAAK,IAAIA,CAAA,GAAI,GAAGW,CAAA,GAAST,CAAA,CAAU6D,MAAA,EAAQ/D,CAAA,GAAIW,CAAA,EAAQX,CAAA,IACrDE,CAAA,CAAUF,CAAA,KAAMC,CAAA,CAEnB;EAAA;EAED+D,GAAGhE,CAAA,EAAOC,CAAA;IAKR,OAHA,KAAK4D,MAAA,CAAO7D,CAAA,GAAQiE,IAAA,CAAKhE,CAAA,MAAQ,KAAK4D,MAAA,CAAO7D,CAAA,IAAS,CAACC,CAAA,IAGhD;MACL,KAAK4D,MAAA,CAAO7D,CAAA,IAAS,KAAK6D,MAAA,CAAO7D,CAAA,GAAQkE,MAAA,CAAQlE,CAAA,IAAMC,CAAA,KAAOD,CAAA,CAAE;IAAA,CAEnE;EAAA;EAEDmE,IAAInE,CAAA,EAAOC,CAAA;IACT,KAAK4D,MAAA,CAAO7D,CAAA,IAAS,KAAK6D,MAAA,CAAO7D,CAAA,GAAQkE,MAAA,CAAQlE,CAAA,IAAMC,CAAA,KAAaD,CAAA,CACrE;EAAA;EAED4C,QAAA;IACE,KAAKiB,MAAA,GAAS,CAAE,CACjB;EAAA;AAAA;AC1BH,MAAM5D,CAAA,GAAc,MAAM;AAEnB,MAAMmE,aAAA;EACXzC,YAAY3B,CAAA;IAASqE,eAAA,EAAEpE,CAAA,GAAkB;IAACqE,eAAA,EAAEpE,CAAA,GAAkB;EAAA;IAC5D,KAAKqE,OAAA,GAAUvE,CAAA,EACf,KAAKqE,eAAA,GAAkBpE,CAAA,EACvB,KAAKqE,eAAA,GAAkBpE,CAAA,EAEvB,KAAKsE,UAAA,GAAa;MAChBd,CAAA,EAAG;MACHC,CAAA,EAAG;IAAA,GAGL,KAAKc,OAAA,GAAU,IAAIb,OAAA,IACnBtB,MAAA,CAAOC,gBAAA,CAAiB,UAAU,KAAKmC,cAAA,GAAgB,IACvD,KAAKA,cAAA,IAEL,KAAKH,OAAA,CAAQhC,gBAAA,CAAiB,SAAS,KAAKoC,OAAA,EAAS;MAAEC,OAAA,GAAS;IAAA,IAChE,KAAKL,OAAA,CAAQhC,gBAAA,CAAiB,cAAc,KAAKsC,YAAA,EAAc;MAC7DD,OAAA,GAAS;IAAA,IAEX,KAAKL,OAAA,CAAQhC,gBAAA,CAAiB,aAAa,KAAKuC,WAAA,EAAa;MAC3DF,OAAA,GAAS;IAAA,IAEX,KAAKL,OAAA,CAAQhC,gBAAA,CAAiB,YAAY,KAAKwC,UAAA,EAAY;MACzDH,OAAA,GAAS;IAAA,EAEZ;EAAA;EAGDZ,GAAGhE,CAAA,EAAOC,CAAA;IACR,OAAO,KAAKwE,OAAA,CAAQT,EAAA,CAAGhE,CAAA,EAAOC,CAAA,CAC/B;EAAA;EAGD2C,QAAA;IACE,KAAK6B,OAAA,CAAQ7B,OAAA,IAEbN,MAAA,CAAOQ,mBAAA,CAAoB,UAAU,KAAK4B,cAAA,GAAgB,IAE1D,KAAKH,OAAA,CAAQzB,mBAAA,CAAoB,SAAS,KAAK6B,OAAA,EAAS;MACtDC,OAAA,GAAS;IAAA,IAEX,KAAKL,OAAA,CAAQzB,mBAAA,CAAoB,cAAc,KAAK+B,YAAA,EAAc;MAChED,OAAA,GAAS;IAAA,IAEX,KAAKL,OAAA,CAAQzB,mBAAA,CAAoB,aAAa,KAAKgC,WAAA,EAAa;MAC9DF,OAAA,GAAS;IAAA,IAEX,KAAKL,OAAA,CAAQzB,mBAAA,CAAoB,YAAY,KAAKiC,UAAA,EAAY;MAC5DH,OAAA,GAAS;IAAA,EAEZ;EAAA;EAGDC,YAAA,GAAgB7E,CAAA;IACd;MAAMgF,OAAA,EAAE/E,CAAA;MAAOgF,OAAA,EAAE/E;IAAA,IAAYF,CAAA,CAAMkF,aAAA,GAC/BlF,CAAA,CAAMkF,aAAA,CAAc,KACpBlF,CAAA;IAEJ,KAAKwE,UAAA,CAAWd,CAAA,GAAIzD,CAAA,EACpB,KAAKuE,UAAA,CAAWb,CAAA,GAAIzD,CAAA,EAEpB,KAAKiF,SAAA,GAAY;MACfzB,CAAA,EAAG;MACHC,CAAA,EAAG;IAAA,GAGL,KAAKc,OAAA,CAAQX,IAAA,CAAK,UAAU;MAC1BsB,MAAA,EAAQ;MACRC,MAAA,EAAQ;MACRC,KAAA,EAAAtF;IAAA,EACA;EAAA;EAIJ8E,WAAA,GAAe9E,CAAA;IACb;QAAMgF,OAAA,EAAE/E,CAAA;QAAOgF,OAAA,EAAE/E;MAAA,IAAYF,CAAA,CAAMkF,aAAA,GAC/BlF,CAAA,CAAMkF,aAAA,CAAc,KACpBlF,CAAA;MAEEW,CAAA,KAAWV,CAAA,GAAU,KAAKuE,UAAA,CAAWd,CAAA,IAAK,KAAKY,eAAA;MAC/C1D,CAAA,KAAWV,CAAA,GAAU,KAAKsE,UAAA,CAAWb,CAAA,IAAK,KAAKW,eAAA;IAErD,KAAKE,UAAA,CAAWd,CAAA,GAAIzD,CAAA,EACpB,KAAKuE,UAAA,CAAWb,CAAA,GAAIzD,CAAA,EAEpB,KAAKiF,SAAA,GAAY;MACfzB,CAAA,EAAG/C,CAAA;MACHgD,CAAA,EAAG/C;IAAA,GAGL,KAAK6D,OAAA,CAAQX,IAAA,CAAK,UAAU;MAC1BsB,MAAA,EAAAzE,CAAA;MACA0E,MAAA,EAAAzE,CAAA;MACA0E,KAAA,EAAAtF;IAAA,EACA;EAAA;EAGJ+E,UAAA,GAAc/E,CAAA;IACZ,KAAKyE,OAAA,CAAQX,IAAA,CAAK,UAAU;MAC1BsB,MAAA,EAAQ,KAAKD,SAAA,CAAUzB,CAAA;MACvB2B,MAAA,EAAQ,KAAKF,SAAA,CAAUxB,CAAA;MACvB2B,KAAA,EAAAtF;IAAA,EACA;EAAA;EAIJ2E,OAAA,GAAW3E,CAAA;IACT;MAAIoF,MAAA,EAAElF,CAAA;MAAMmF,MAAA,EAAE1E,CAAA;MAAM4E,SAAA,EAAE3E;IAAA,IAAcZ,CAAA;IAOpCE,CAAA,IAJgB,MAAdU,CAAA,GAAkBX,CAAA,GAA4B,MAAdW,CAAA,GAAkB,KAAK4E,WAAA,GAAc,GAKvE7E,CAAA,IAHgB,MAAdC,CAAA,GAAkBX,CAAA,GAA4B,MAAdW,CAAA,GAAkB,KAAK6E,YAAA,GAAe,GAKxEvF,CAAA,IAAU,KAAKmE,eAAA,EACf1D,CAAA,IAAU,KAAK0D,eAAA,EAEf,KAAKI,OAAA,CAAQX,IAAA,CAAK,UAAU;MAAEsB,MAAA,EAAAlF,CAAA;MAAQmF,MAAA,EAAA1E,CAAA;MAAQ2E,KAAA,EAAAtF;IAAA,EAAQ;EAAA;EAGxD0E,cAAA,GAAiBA,CAAA;IACf,KAAKc,WAAA,GAAclD,MAAA,CAAOY,UAAA,EAC1B,KAAKuC,YAAA,GAAenD,MAAA,CAAOc,WAAW;EAAA;AAAA;ACzF5B,MAAOsC,KAAA;EAMnB/D,YAAA;IAAYC,OAAA,EACV5B,CAAA,GAAUsC,MAAA;IAAMT,OAAA,EAChB5B,CAAA,GAAU0F,QAAA,CAASC,eAAA;IAAeC,iBAAA,EAClC3F,CAAA,GAAoBF,CAAA;IAAO8F,YAAA,EAC3BnF,CAAA,GAAeT,CAAA;IAAiB6F,WAAA,EAChCnF,CAAA,IAAc;IAAIoF,SAAA,EAClBlF,CAAA,IAAY;IAAKmF,aAAA,EACjBlF,CAAA,GAAgB;IAAKmF,sBAAA,EACrBC,CAAA,GAAyB;IAAEhF,QAAA,EAC3BiF,CAAA;IAAQhF,MAAA,EACRiF,CAAA,GAAUrG,CAAA,IAAMG,IAAA,CAAKE,GAAA,CAAI,GAAG,QAAQF,IAAA,CAAKmG,GAAA,CAAI,IAAI,KAAKtG,CAAA;IAAGS,IAAA,EACzD8F,CAAA,IAAQH,CAAA,IAAY;IAAGI,QAAA,EACvBC,CAAA,IAAW;IAAKC,WAAA,EAChBC,CAAA,GAAc;IAAUC,kBAAA,EACxBC,CAAA,GAAqB;IAAUvC,eAAA,EAC/BwC,CAAA,GAAkB;IAACzC,eAAA,EACnB0C,CAAA,GAAkB;IAACjF,UAAA,EACnBkF,CAAA,IAAa;IAAIC,+BAAA,EACjBC,CAAA,IAAkC;EAAA,IAClB;IAxBlB,KAAUC,UAAA,IAAY,GACtB,KAAaC,aAAA,IAAY,GACzB,KAAWC,WAAA,IAAY,GACvB,KAAUC,UAAA,IAAY,GA2Gd,KAAeC,eAAA,GAAG;MAAGnC,MAAA,EAAApF,CAAA;MAAQqF,MAAA,EAAApF,CAAA;MAAQqF,KAAA,EAAApF;IAAA;MAE3C,IAAIA,CAAA,CAAMsH,OAAA,EAAS;MAEnB,MAAM7G,CAAA,GAAUT,CAAA,CAAMuH,IAAA,CAAKC,QAAA,CAAS;QAC9B9G,CAAA,GAAUV,CAAA,CAAMuH,IAAA,CAAKC,QAAA,CAAS;MASpC,IANE,KAAKC,OAAA,CAAQ3B,SAAA,IACbrF,CAAA,IACe,iBAAfT,CAAA,CAAMuH,IAAA,KACL,KAAKG,SAAA,KACL,KAAKC,QAAA,EAIN,YADA,KAAKC,KAAA;MAIP,MAAMhH,CAAA,GAAqB,MAAXd,CAAA,IAA2B,MAAXC,CAAA;QAQ1Bc,CAAA,GACiC,eAApC,KAAK4G,OAAA,CAAQf,kBAAA,IAAgD,MAAX3G,CAAA,IACd,iBAApC,KAAK0H,OAAA,CAAQf,kBAAA,IAAkD,MAAX5G,CAAA;MAEvD,IAAIc,CAAA,IAAWC,CAAA,EAEb;MAIF,IAAIoF,CAAA,GAAejG,CAAA,CAAM6H,YAAA;MAGzB,IAFA5B,CAAA,GAAeA,CAAA,CAAa6B,KAAA,CAAM,GAAG7B,CAAA,CAAa8B,OAAA,CAAQ,KAAKC,WAAA,IAG3D/B,CAAA,CAAagC,IAAA,CACZnI,CAAA;QAAA,IAAAC,CAAA,EAAAC,CAAA,EAAAY,CAAA,EAAAC,CAAA,EAAAoF,CAAA;QACC,QAAiB,UAAjBlG,CAAA,GAAAD,CAAA,CAAKoI,YAAA,UAAY,MAAAnI,CAAA,YAAAA,CAAA,CAAAoI,IAAA,CAAArI,CAAA,EAAG,0BACnBW,CAAA,KAA+B,UAApBT,CAAA,GAAAF,CAAA,CAAKoI,YAAA,UAAe,MAAAlI,CAAA,YAAAA,CAAA,CAAAmI,IAAA,CAAArI,CAAA,kCAC/BY,CAAA,KAA+B,UAApBE,CAAA,GAAAd,CAAA,CAAKoI,YAAA,UAAe,MAAAtH,CAAA,YAAAA,CAAA,CAAAuH,IAAA,CAAArI,CAAA,mCACf,UAAAe,CAAA,GAAhBf,CAAA,CAAKsI,SAAA,UAAW,MAAAvH,CAAA,YAAAA,CAAA,CAAAwH,QAAA,CAAS,eACT,UAAdpC,CAAA,GAAAnG,CAAA,CAAKsI,SAAA,UAAS,MAAAnC,CAAA,YAAAA,CAAA,CAAEoC,QAAA,CAAS,iBAAiB;MAAA,IAGjD;MAEF,IAAI,KAAKX,SAAA,IAAa,KAAKC,QAAA,EAEzB,YADA3H,CAAA,CAAMsI,cAAA;MAQR,IAJA,KAAKC,QAAA,GACF,KAAKd,OAAA,CAAQ3B,SAAA,IAAarF,CAAA,IAC1B,KAAKgH,OAAA,CAAQ5B,WAAA,IAAenF,CAAA,GAE1B,KAAK6H,QAAA,EAGR,OAFA,KAAKC,WAAA,IAAc,QACnB,KAAKC,OAAA,CAAQpH,IAAA;MAIfrB,CAAA,CAAMsI,cAAA;MAEN,IAAIpC,CAAA,GAAQnG,CAAA;MAC4B,WAApC,KAAK0H,OAAA,CAAQf,kBAAA,GACfR,CAAA,GAAQjG,IAAA,CAAKyI,GAAA,CAAI3I,CAAA,IAAUE,IAAA,CAAKyI,GAAA,CAAI5I,CAAA,IAAUC,CAAA,GAASD,CAAA,GACV,iBAApC,KAAK2H,OAAA,CAAQf,kBAAA,KACtBR,CAAA,GAAQpG,CAAA;MAGV,MAAMqG,CAAA,GAAY1F,CAAA,IAAW,KAAKgH,OAAA,CAAQ3B,SAAA;QAGpCO,CAAA,GAFa5F,CAAA,IAA0B,eAAfT,CAAA,CAAMuH,IAAA,IAEEtH,IAAA,CAAKyI,GAAA,CAAIxC,CAAA,IAAS;MAEpDG,CAAA,KACFH,CAAA,GAAQ,KAAKyC,QAAA,GAAW,KAAKlB,OAAA,CAAQzB,sBAAA,GAGvC,KAAK4C,QAAA,CAAS,KAAKC,YAAA,GAAe3C,CAAA,EAAK4C,MAAA,CAAAC,MAAA;QACrCC,YAAA,GAAc;MAAA,GACV7C,CAAA,GACA;QACE5F,IAAA,EAAM8F,CAAA,GAAkB,KAAKoB,OAAA,CAAQ1B,aAAA,GAAgB;MAAA,IAEvD;QACExF,IAAA,EAAM,KAAKkH,OAAA,CAAQlH,IAAA;QACnBU,QAAA,EAAU,KAAKwG,OAAA,CAAQxG,QAAA;QACvBC,MAAA,EAAQ,KAAKuG,OAAA,CAAQvG;MAAA,GAE3B;IAAA,GAWI,KAAc+H,cAAA,GAAG;MACvB,KAAI,KAAKC,wBAAA,KAEJ,KAAKV,WAAA,EAAa;QACrB,MAAM1I,CAAA,GAAa,KAAKqJ,cAAA;QACxB,KAAKA,cAAA,GAAiB,KAAKN,YAAA,GAAe,KAAKO,YAAA,EAC/C,KAAKT,QAAA,GAAW,GAChB,KAAKU,SAAA,GAAYpJ,IAAA,CAAKqJ,IAAA,CAAK,KAAKH,cAAA,GAAiBrJ,CAAA,GACjD,KAAK8D,IAAA,EACN;MAAA;IAAA,GAzMDxB,MAAA,CAAOmH,YAAA,aAGHzJ,CAAA,KAAY2F,QAAA,CAASC,eAAA,IAAmB5F,CAAA,KAAY2F,QAAA,CAAS+D,IAAA,KAC/D1J,CAAA,GAAUsC,MAAA,GAGZ,KAAKqF,OAAA,GAAU;MACb/F,OAAA,EAAA5B,CAAA;MACA6B,OAAA,EAAA5B,CAAA;MACA4F,iBAAA,EAAA3F,CAAA;MACA4F,YAAA,EAAAnF,CAAA;MACAoF,WAAA,EAAAnF,CAAA;MACAoF,SAAA,EAAAlF,CAAA;MACAmF,aAAA,EAAAlF,CAAA;MACAmF,sBAAA,EAAAC,CAAA;MACAhF,QAAA,EAAAiF,CAAA;MACAhF,MAAA,EAAAiF,CAAA;MACA5F,IAAA,EAAA8F,CAAA;MACAC,QAAA,EAAAC,CAAA;MACAG,kBAAA,EAAAC,CAAA;MACAH,WAAA,EAAAC,CAAA;MACArC,eAAA,EAAAwC,CAAA;MACAzC,eAAA,EAAA0C,CAAA;MACAjF,UAAA,EAAAkF,CAAA;MACAC,+BAAA,EAAAC;IAAA,GAGF,KAAKyB,OAAA,GAAU,IAAIrI,OAAA,IACnB,KAAKmE,OAAA,GAAU,IAAIb,OAAA,IACnB,KAAK+F,UAAA,GAAa,IAAIjI,UAAA,CAAW;MAAEE,OAAA,EAAA5B,CAAA;MAAS6B,OAAA,EAAA5B,CAAA;MAAS6B,UAAA,EAAAkF;IAAA,IACrD,KAAK4C,eAAA,CAAgB,UAAS,IAE9B,KAAKf,QAAA,GAAW,GAChB,KAAKhB,QAAA,IAAW,GAChB,KAAKD,SAAA,IAAY,GACjB,KAAKa,QAAA,GAAW3H,CAAA,IAAaF,CAAA,EAC7B,KAAK8H,WAAA,IAAc,GACnB,KAAKK,YAAA,GAAe,KAAKM,cAAA,GAAiB,KAAKC,YAAA,EAE/C,KAAK3B,OAAA,CAAQ/F,OAAA,CAAQW,gBAAA,CAAiB,UAAU,KAAK4G,cAAA,GAAgB,IAErE,KAAKU,aAAA,GAAgB,IAAIzF,aAAA,CAAczD,CAAA,EAAc;MACnD2D,eAAA,EAAAwC,CAAA;MACAzC,eAAA,EAAA0C;IAAA,IAEF,KAAK8C,aAAA,CAAc7F,EAAA,CAAG,UAAU,KAAKuD,eAAA,CACtC;EAAA;EAED3E,QAAA;IACE,KAAK6B,OAAA,CAAQ7B,OAAA,IAEb,KAAK+E,OAAA,CAAQ/F,OAAA,CAAQkB,mBAAA,CACnB,UACA,KAAKqG,cAAA,GACL,IAGF,KAAKU,aAAA,CAAcjH,OAAA,IACnB,KAAK+G,UAAA,CAAW/G,OAAA,IAEhB,KAAKgH,eAAA,CAAgB,UAAS,IAC9B,KAAKA,eAAA,CAAgB,iBAAgB,IACrC,KAAKA,eAAA,CAAgB,oBAAmB,IACxC,KAAKA,eAAA,CAAgB,kBAAiB,IACtC,KAAKA,eAAA,CAAgB,iBAAgB,EACtC;EAAA;EAED5F,GAAGhE,CAAA,EAAeC,CAAA;IAChB,OAAO,KAAKwE,OAAA,CAAQT,EAAA,CAAGhE,CAAA,EAAOC,CAAA,CAC/B;EAAA;EAEDkE,IAAInE,CAAA,EAAeC,CAAA;IACjB,OAAO,KAAKwE,OAAA,CAAQN,GAAA,CAAInE,CAAA,EAAOC,CAAA,CAChC;EAAA;EAEO6J,UAAU9J,CAAA;IAEZ,KAAK+J,YAAA,GACP,KAAK7B,WAAA,CAAY8B,UAAA,GAAahK,CAAA,GAE9B,KAAKkI,WAAA,CAAY+B,SAAA,GAAYjK,CAEhC;EAAA;EAqGDqC,OAAA;IACE,KAAKsH,UAAA,CAAWtH,MAAA,EACjB;EAAA;EAEOyB,KAAA;IACN,KAAKW,OAAA,CAAQX,IAAA,CAAK,UAAU,KAC7B;EAAA;EAcOgE,MAAA;IACN,KAAKD,QAAA,IAAW,GAChB,KAAKa,WAAA,IAAc,GACnB,KAAKW,cAAA,GAAiB,KAAKN,YAAA,GAAe,KAAKO,YAAA,EAC/C,KAAKT,QAAA,GAAW,GAChB,KAAKF,OAAA,CAAQpH,IAAA,EACd;EAAA;EAED2I,MAAA;IACO,KAAKtC,SAAA,KACV,KAAKA,SAAA,IAAY,GAEjB,KAAKE,KAAA,GACN;EAAA;EAEDvG,KAAA;IACM,KAAKqG,SAAA,KACT,KAAKA,SAAA,IAAY,GACjB,KAAKe,OAAA,CAAQpH,IAAA,IAEb,KAAKuG,KAAA,GACN;EAAA;EAEDqC,IAAInK,CAAA;IACF,MAAMC,CAAA,GAAYD,CAAA,IAAQ,KAAKoK,IAAA,IAAQpK,CAAA;IACvC,KAAKoK,IAAA,GAAOpK,CAAA,EAEZ,KAAK2I,OAAA,CAAQpI,OAAA,CAAoB,OAAZN,CAAA,CACtB;EAAA;EAED6I,SACE7I,CAAA;IACAoK,MAAA,EACEnK,CAAA,GAAS;IAACoK,SAAA,EACV3J,CAAA,IAAY;IAAK4J,IAAA,EACjB3J,CAAA,IAAO;IAAKO,QAAA,EACZL,CAAA,GAAW,KAAK6G,OAAA,CAAQxG,QAAA;IAAQC,MAAA,EAChCL,CAAA,GAAS,KAAK4G,OAAA,CAAQvG,MAAA;IAAMX,IAAA,EAC5B0F,CAAA,IAAQrF,CAAA,IAAY,KAAK6G,OAAA,CAAQlH,IAAA;IAAI+J,UAAA,EACrCpE,CAAA;IAAUqE,KAAA,EACVpE,CAAA,IAAQ;IAAK6C,YAAA,EACb3C,CAAA,IAAe;EAAA,IAWb;IAEJ,KAAK,KAAKqB,SAAA,KAAa,KAAKC,QAAA,IAAcxB,CAAA,EAA1C;MAGA,IAAI,CAAC,OAAO,QAAQ,SAASqB,QAAA,CAASzH,CAAA,GACpCA,CAAA,GAAS,OACJ,IAAI,CAAC,UAAU,SAAS,OAAOyH,QAAA,CAASzH,CAAA,GAC7CA,CAAA,GAAS,KAAKwD,KAAA,MACT;QACL,IAAIzD,CAAA;QAUJ,IARsB,mBAAXC,CAAA,GAETD,CAAA,GAAO2F,QAAA,CAAS+E,aAAA,CAAczK,CAAA,KACrB,QAAAA,CAAA,QAAM,IAANA,CAAA,CAAQ0K,QAAA,MAEjB3K,CAAA,GAAOC,CAAA,GAGLD,CAAA,EAAM;UACR,IAAI,KAAK2H,OAAA,CAAQ/F,OAAA,KAAYU,MAAA,EAAQ;YAEnC,MAAMtC,CAAA,GAAc,KAAK2H,OAAA,CAAQ/F,OAAA,CAAQgJ,qBAAA;YACzC1K,CAAA,IAAU,KAAK6J,YAAA,GAAe/J,CAAA,CAAY6K,IAAA,GAAO7K,CAAA,CAAY8K,GAC9D;UAAA;UAED,MAAMnK,CAAA,GAAOX,CAAA,CAAK4K,qBAAA;UAElB3K,CAAA,IACG,KAAK8J,YAAA,GAAepJ,CAAA,CAAKkK,IAAA,GAAOlK,CAAA,CAAKmK,GAAA,IAAO,KAAKzB,cACrD;QAAA;MACF;MAED,IAAsB,mBAAXpJ,CAAA,EAAX;QAaA,IAXAA,CAAA,IAAUC,CAAA,EACVD,CAAA,GAASE,IAAA,CAAKc,KAAA,CAAMhB,CAAA,GAEhB,KAAK0H,OAAA,CAAQnB,QAAA,GACXD,CAAA,KACF,KAAKwC,YAAA,GAAe,KAAKM,cAAA,GAAiB,KAAK0B,MAAA,IAGjD9K,CAAA,GAASD,CAAA,CAAM,GAAGC,CAAA,EAAQ,KAAKwD,KAAA,GAG7B9C,CAAA,EAKF,OAJA,KAAK0I,cAAA,GAAiB,KAAKN,YAAA,GAAe9I,CAAA,EAC1C,KAAK6J,SAAA,CAAU,KAAKiB,MAAA,GACpB,KAAKjD,KAAA,UACL,QAAA1B,CAAA,IAAAA,CAAA,CAAa;QAIf,KAAKG,CAAA,EAAc;UACjB,IAAItG,CAAA,KAAW,KAAK8I,YAAA,EAAc;UAElC,KAAKA,YAAA,GAAe9I,CACrB;QAAA;QAED,KAAK0I,OAAA,CAAQnH,MAAA,CAAO,KAAK6H,cAAA,EAAgBpJ,CAAA,EAAQ;UAC/CkB,QAAA,EAAAL,CAAA;UACAM,MAAA,EAAAL,CAAA;UACAN,IAAA,EAAA0F,CAAA;UACA1E,OAAA,EAASA,CAAA;YAEHb,CAAA,KAAM,KAAKiH,QAAA,IAAW,IAC1B,KAAKa,WAAA,IAAc,CAAI;UAAA;UAEzBpH,QAAA,EAAUA,CAACtB,CAAA,EAAeC,CAAA;YACxB,KAAKyI,WAAA,IAAc,GAGnB,KAAKG,QAAA,GAAW7I,CAAA,GAAQ,KAAKqJ,cAAA,EAC7B,KAAKE,SAAA,GAAYpJ,IAAA,CAAKqJ,IAAA,CAAK,KAAKX,QAAA,GAEhC,KAAKQ,cAAA,GAAiBrJ,CAAA,EACtB,KAAK8J,SAAA,CAAU,KAAKiB,MAAA,GAEhBxE,CAAA,KAEF,KAAKwC,YAAA,GAAe/I,CAAA,GAGjBC,CAAA,IAAW,KAAK6D,IAAA,IAEjB7D,CAAA,KACF,KAAK6H,KAAA,IACL,KAAKhE,IAAA,IACL,QAAAsC,CAAA,IAAAA,CAAA,CAAa,OAGb,KAAKgD,wBAAA,IAA2B,GAChC4B,qBAAA,CAAsB;cAAA,OACb,KAAK5B,wBAAwB;YAAA,GAEvC;UAAA;QAAA,EA/DiC;MAAA;IAhCiB;EAkGxD;EAED,IAAAlB,WAAIA,CAAA;IACF,OAAO,KAAKP,OAAA,CAAQ/F,OAAA,KAAYU,MAAA,GAC5BqD,QAAA,CAASC,eAAA,GACT,KAAK+B,OAAA,CAAQ/F,OAClB;EAAA;EAED,IAAA6B,KAAIA,CAAA;IACF,OAAI,KAAKkE,OAAA,CAAQV,+BAAA,GACX,KAAK8C,YAAA,GACA,KAAK7B,WAAA,CAAY1E,WAAA,GAAc,KAAK0E,WAAA,CAAY7E,WAAA,GAEhD,KAAK6E,WAAA,CAAY3E,YAAA,GAAe,KAAK2E,WAAA,CAAY5E,YAAA,GAGnD,KAAKqG,UAAA,CAAWlG,KAAA,CAAM,KAAKsG,YAAA,GAAe,MAAM,IAE1D;EAAA;EAED,IAAAA,YAAIA,CAAA;IACF,OAAoC,iBAA7B,KAAKpC,OAAA,CAAQjB,WACrB;EAAA;EAED,IAAA4C,YAAIA,CAAA;IAEF,OAAO,KAAKS,YAAA,GACR,KAAK7B,WAAA,CAAY8B,UAAA,GACjB,KAAK9B,WAAA,CAAY+B,SACtB;EAAA;EAED,IAAAc,MAAIA,CAAA;IACF,OAAO,KAAKpD,OAAA,CAAQnB,QAAA,INhbDxG,CAAA,GMibR,KAAKqJ,cAAA,ENjbMpJ,CAAA,GMibU,KAAKwD,KAAA,GNhb9BzD,CAAA,GAAIC,CAAA,GAAKA,CAAA,IAAKA,CAAA,IMibjB,KAAKoJ,cAAA;INlbN,IAAgBrJ,CAAA,EAAGC,CMmbvB;EAAA;EAED,IAAAgL,QAAIA,CAAA;IAEF,OAAsB,MAAf,KAAKxH,KAAA,GAAc,IAAI,KAAKsH,MAAA,GAAS,KAAKtH,KAClD;EAAA;EAED,IAAAgF,QAAIA,CAAA;IACF,OAAO,KAAKtB,UACb;EAAA;EAED,IAAAsB,QAAYA,CAASzI,CAAA;IACf,KAAKmH,UAAA,KAAenH,CAAA,KACtB,KAAKmH,UAAA,GAAanH,CAAA,EAClB,KAAK4J,eAAA,CAAgB,gBAAgB5J,CAAA,EAExC;EAAA;EAED,IAAA0I,WAAIA,CAAA;IACF,OAAO,KAAKtB,aACb;EAAA;EAED,IAAAsB,WAAYA,CAAY1I,CAAA;IAClB,KAAKoH,aAAA,KAAkBpH,CAAA,KACzB,KAAKoH,aAAA,GAAgBpH,CAAA,EACrB,KAAK4J,eAAA,CAAgB,mBAAmB5J,CAAA,EAE3C;EAAA;EAED,IAAA4H,SAAIA,CAAA;IACF,OAAO,KAAKP,WACb;EAAA;EAED,IAAAO,SAAYA,CAAU5H,CAAA;IAChB,KAAKqH,WAAA,KAAgBrH,CAAA,KACvB,KAAKqH,WAAA,GAAcrH,CAAA,EACnB,KAAK4J,eAAA,CAAgB,iBAAiB5J,CAAA,EAEzC;EAAA;EAED,IAAA6H,QAAIA,CAAA;IACF,OAAO,KAAKP,UACb;EAAA;EAED,IAAAO,QAAYA,CAAS7H,CAAA;IACf,KAAKsH,UAAA,KAAetH,CAAA,KACtB,KAAKsH,UAAA,GAAatH,CAAA,EAClB,KAAK4J,eAAA,CAAgB,gBAAgB5J,CAAA,EAExC;EAAA;EAED,IAAAkL,SAAIA,CAAA;IACF,IAAIlL,CAAA,GAAY;IAKhB,OAJI,KAAK4H,SAAA,KAAW5H,CAAA,IAAa,mBAC7B,KAAK6H,QAAA,KAAU7H,CAAA,IAAa,kBAC5B,KAAK0I,WAAA,KAAa1I,CAAA,IAAa,qBAC/B,KAAKyI,QAAA,KAAUzI,CAAA,IAAa,kBACzBA,CACR;EAAA;EAEO4J,gBAAgB5J,CAAA,EAAcC,CAAA;IACpC,KAAKiI,WAAA,CAAYI,SAAA,CAAU6C,MAAA,CAAOnL,CAAA,EAAMC,CAAA,GACxC,KAAKwE,OAAA,CAAQX,IAAA,CAAK,oBAAoB,KACvC;EAAA;AAAA;AAAA,SAAA4B,KAAA,IAAA0F,OAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}