{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ScrollStack.js\",\n  _s = $RefreshSig$();\nimport { useLayoutEffect, useRef, useCallback } from \"react\";\nimport \"./ScrollStack.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const ScrollStackItem = ({\n  children,\n  itemClassName = \"\"\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: `scroll-stack-card ${itemClassName}`.trim(),\n  children: children\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 5,\n  columnNumber: 3\n}, this);\n_c = ScrollStackItem;\nconst ScrollStack = ({\n  children,\n  className = \"\",\n  itemDistance = 100,\n  itemScale = 0.03,\n  itemStackDistance = 30,\n  stackPosition = \"20%\",\n  scaleEndPosition = \"10%\",\n  baseScale = 0.85,\n  scaleDuration = 0.5,\n  rotationAmount = 0,\n  blurAmount = 0,\n  onStackComplete\n}) => {\n  _s();\n  const scrollerRef = useRef(null);\n  const stackCompletedRef = useRef(false);\n  const animationFrameRef = useRef(null);\n  const cardsRef = useRef([]);\n  const lastTransformsRef = useRef(new Map());\n  const isUpdatingRef = useRef(false);\n  const calculateProgress = useCallback((scrollTop, start, end) => {\n    if (scrollTop < start) return 0;\n    if (scrollTop > end) return 1;\n    return (scrollTop - start) / (end - start);\n  }, []);\n  const parsePercentage = useCallback((value, containerHeight) => {\n    if (typeof value === 'string' && value.includes('%')) {\n      return parseFloat(value) / 100 * containerHeight;\n    }\n    return parseFloat(value);\n  }, []);\n  const updateCardTransforms = useCallback(() => {\n    const scroller = scrollerRef.current;\n    if (!scroller || !cardsRef.current.length || isUpdatingRef.current) return;\n    isUpdatingRef.current = true;\n    const scrollTop = scroller.scrollTop;\n    const containerHeight = scroller.clientHeight;\n    const stackPositionPx = parsePercentage(stackPosition, containerHeight);\n    const scaleEndPositionPx = parsePercentage(scaleEndPosition, containerHeight);\n    const endElement = scroller.querySelector('.scroll-stack-end');\n    const endElementTop = endElement ? endElement.offsetTop : 0;\n    cardsRef.current.forEach((card, i) => {\n      if (!card) return;\n      const cardTop = card.offsetTop;\n      const triggerStart = cardTop - stackPositionPx - itemStackDistance * i;\n      const triggerEnd = cardTop - scaleEndPositionPx;\n      const pinStart = cardTop - stackPositionPx - itemStackDistance * i;\n      const pinEnd = endElementTop - containerHeight / 2;\n      const scaleProgress = calculateProgress(scrollTop, triggerStart, triggerEnd);\n      const targetScale = baseScale + i * itemScale;\n      const scale = 1 - scaleProgress * (1 - targetScale);\n      const rotation = rotationAmount ? i * rotationAmount * scaleProgress : 0;\n      let blur = 0;\n      if (blurAmount) {\n        let topCardIndex = 0;\n        for (let j = 0; j < cardsRef.current.length; j++) {\n          const jCardTop = cardsRef.current[j].offsetTop;\n          const jTriggerStart = jCardTop - stackPositionPx - itemStackDistance * j;\n          if (scrollTop >= jTriggerStart) {\n            topCardIndex = j;\n          }\n        }\n        if (i < topCardIndex) {\n          const depthInStack = topCardIndex - i;\n          blur = Math.max(0, depthInStack * blurAmount);\n        }\n      }\n      let translateY = 0;\n      const isPinned = scrollTop >= pinStart && scrollTop <= pinEnd;\n      if (isPinned) {\n        translateY = scrollTop - cardTop + stackPositionPx + itemStackDistance * i;\n      } else if (scrollTop > pinEnd) {\n        translateY = pinEnd - cardTop + stackPositionPx + itemStackDistance * i;\n      }\n      const newTransform = {\n        translateY: Math.round(translateY * 100) / 100,\n        scale: Math.round(scale * 1000) / 1000,\n        rotation: Math.round(rotation * 100) / 100,\n        blur: Math.round(blur * 100) / 100\n      };\n      const lastTransform = lastTransformsRef.current.get(i);\n      const hasChanged = !lastTransform || Math.abs(lastTransform.translateY - newTransform.translateY) > 0.1 || Math.abs(lastTransform.scale - newTransform.scale) > 0.001 || Math.abs(lastTransform.rotation - newTransform.rotation) > 0.1 || Math.abs(lastTransform.blur - newTransform.blur) > 0.1;\n      if (hasChanged) {\n        const transform = `translate3d(0, ${newTransform.translateY}px, 0) scale(${newTransform.scale}) rotate(${newTransform.rotation}deg)`;\n        const filter = newTransform.blur > 0 ? `blur(${newTransform.blur}px)` : '';\n        card.style.transform = transform;\n        card.style.filter = filter;\n        lastTransformsRef.current.set(i, newTransform);\n      }\n      if (i === cardsRef.current.length - 1) {\n        const isInView = scrollTop >= pinStart && scrollTop <= pinEnd;\n        if (isInView && !stackCompletedRef.current) {\n          stackCompletedRef.current = true;\n          onStackComplete === null || onStackComplete === void 0 ? void 0 : onStackComplete();\n        } else if (!isInView && stackCompletedRef.current) {\n          stackCompletedRef.current = false;\n        }\n      }\n    });\n    isUpdatingRef.current = false;\n  }, [itemScale, itemStackDistance, stackPosition, scaleEndPosition, baseScale, rotationAmount, blurAmount, onStackComplete, calculateProgress, parsePercentage]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: containerRef,\n    className: `scroll-stack ${className}`,\n    style: style,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: '20px',\n        right: '20px',\n        background: 'rgba(0,0,0,0.8)',\n        color: 'white',\n        padding: '10px',\n        borderRadius: '5px',\n        fontSize: '14px',\n        zIndex: 10000,\n        fontFamily: 'monospace'\n      },\n      children: [\"ScrollStack Debug:\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 27\n      }, this), \"Progress: \", (scrollProgress * 100).toFixed(0), \"%\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 55\n      }, this), \"Cards: \", childrenArray.length]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"scroll-stack-container\",\n      children: childrenArray.map((child, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"scroll-stack-item\",\n        style: getItemStyle(index, childrenArray.length),\n        children: child\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 145,\n    columnNumber: 5\n  }, this);\n};\n_s(ScrollStack, \"0qP9JuxBecf7DwmNlG7Ugpno6iM=\");\n_c2 = ScrollStack;\nexport default ScrollStack;\nvar _c, _c2;\n$RefreshReg$(_c, \"ScrollStackItem\");\n$RefreshReg$(_c2, \"ScrollStack\");", "map": {"version": 3, "names": ["useLayoutEffect", "useRef", "useCallback", "jsxDEV", "_jsxDEV", "ScrollStackItem", "children", "itemClassName", "className", "trim", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "ScrollStack", "itemDistance", "itemScale", "itemStackDistance", "stackPosition", "scaleEndPosition", "baseScale", "scaleDuration", "rotationAmount", "blurAmount", "onStackComplete", "_s", "scrollerRef", "stackCompletedRef", "animationFrameRef", "cardsRef", "lastTransformsRef", "Map", "isUpdatingRef", "calculateProgress", "scrollTop", "start", "end", "parsePercentage", "value", "containerHeight", "includes", "parseFloat", "updateCardTransforms", "scroller", "current", "length", "clientHeight", "stackPositionPx", "scaleEndPositionPx", "endElement", "querySelector", "endElementTop", "offsetTop", "for<PERSON>ach", "card", "i", "cardTop", "triggerStart", "triggerEnd", "pinStart", "pinEnd", "scaleProgress", "targetScale", "scale", "rotation", "blur", "topCardIndex", "j", "jCardTop", "jTriggerStart", "depthInStack", "Math", "max", "translateY", "isPinned", "newTransform", "round", "lastTransform", "get", "has<PERSON><PERSON>ed", "abs", "transform", "filter", "style", "set", "isInView", "ref", "containerRef", "position", "top", "right", "background", "color", "padding", "borderRadius", "fontSize", "zIndex", "fontFamily", "scrollProgress", "toFixed", "childrenA<PERSON>y", "map", "child", "index", "getItemStyle", "_c2", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ScrollStack.js"], "sourcesContent": ["import { useLayoutEffect, useRef, useCallback } from \"react\";\nimport \"./ScrollStack.css\";\n\nexport const ScrollStackItem = ({ children, itemClassName = \"\" }) => (\n  <div className={`scroll-stack-card ${itemClassName}`.trim()}>{children}</div>\n);\n\nconst ScrollStack = ({\n  children,\n  className = \"\",\n  itemDistance = 100,\n  itemScale = 0.03,\n  itemStackDistance = 30,\n  stackPosition = \"20%\",\n  scaleEndPosition = \"10%\",\n  baseScale = 0.85,\n  scaleDuration = 0.5,\n  rotationAmount = 0,\n  blurAmount = 0,\n  onStackComplete,\n}) => {\n  const scrollerRef = useRef(null);\n  const stackCompletedRef = useRef(false);\n  const animationFrameRef = useRef(null);\n  const cardsRef = useRef([]);\n  const lastTransformsRef = useRef(new Map());\n  const isUpdatingRef = useRef(false);\n\n  const calculateProgress = useCallback((scrollTop, start, end) => {\n    if (scrollTop < start) return 0;\n    if (scrollTop > end) return 1;\n    return (scrollTop - start) / (end - start);\n  }, []);\n\n  const parsePercentage = useCallback((value, containerHeight) => {\n    if (typeof value === 'string' && value.includes('%')) {\n      return (parseFloat(value) / 100) * containerHeight;\n    }\n    return parseFloat(value);\n  }, []);\n\n  const updateCardTransforms = useCallback(() => {\n    const scroller = scrollerRef.current;\n    if (!scroller || !cardsRef.current.length || isUpdatingRef.current) return;\n\n    isUpdatingRef.current = true;\n\n    const scrollTop = scroller.scrollTop;\n    const containerHeight = scroller.clientHeight;\n    const stackPositionPx = parsePercentage(stackPosition, containerHeight);\n    const scaleEndPositionPx = parsePercentage(scaleEndPosition, containerHeight);\n    const endElement = scroller.querySelector('.scroll-stack-end');\n    const endElementTop = endElement ? endElement.offsetTop : 0;\n\n    cardsRef.current.forEach((card, i) => {\n      if (!card) return;\n\n      const cardTop = card.offsetTop;\n      const triggerStart = cardTop - stackPositionPx - (itemStackDistance * i);\n      const triggerEnd = cardTop - scaleEndPositionPx;\n      const pinStart = cardTop - stackPositionPx - (itemStackDistance * i);\n      const pinEnd = endElementTop - containerHeight / 2;\n\n      const scaleProgress = calculateProgress(scrollTop, triggerStart, triggerEnd);\n      const targetScale = baseScale + (i * itemScale);\n      const scale = 1 - scaleProgress * (1 - targetScale);\n      const rotation = rotationAmount ? i * rotationAmount * scaleProgress : 0;\n\n      let blur = 0;\n      if (blurAmount) {\n        let topCardIndex = 0;\n        for (let j = 0; j < cardsRef.current.length; j++) {\n          const jCardTop = cardsRef.current[j].offsetTop;\n          const jTriggerStart = jCardTop - stackPositionPx - (itemStackDistance * j);\n          if (scrollTop >= jTriggerStart) {\n            topCardIndex = j;\n          }\n        }\n\n        if (i < topCardIndex) {\n          const depthInStack = topCardIndex - i;\n          blur = Math.max(0, depthInStack * blurAmount);\n        }\n      }\n\n      let translateY = 0;\n      const isPinned = scrollTop >= pinStart && scrollTop <= pinEnd;\n\n      if (isPinned) {\n        translateY = scrollTop - cardTop + stackPositionPx + (itemStackDistance * i);\n      } else if (scrollTop > pinEnd) {\n        translateY = pinEnd - cardTop + stackPositionPx + (itemStackDistance * i);\n      }\n\n      const newTransform = {\n        translateY: Math.round(translateY * 100) / 100,\n        scale: Math.round(scale * 1000) / 1000,\n        rotation: Math.round(rotation * 100) / 100,\n        blur: Math.round(blur * 100) / 100\n      };\n\n      const lastTransform = lastTransformsRef.current.get(i);\n      const hasChanged = !lastTransform ||\n        Math.abs(lastTransform.translateY - newTransform.translateY) > 0.1 ||\n        Math.abs(lastTransform.scale - newTransform.scale) > 0.001 ||\n        Math.abs(lastTransform.rotation - newTransform.rotation) > 0.1 ||\n        Math.abs(lastTransform.blur - newTransform.blur) > 0.1;\n\n      if (hasChanged) {\n        const transform = `translate3d(0, ${newTransform.translateY}px, 0) scale(${newTransform.scale}) rotate(${newTransform.rotation}deg)`;\n        const filter = newTransform.blur > 0 ? `blur(${newTransform.blur}px)` : '';\n\n        card.style.transform = transform;\n        card.style.filter = filter;\n\n        lastTransformsRef.current.set(i, newTransform);\n      }\n\n      if (i === cardsRef.current.length - 1) {\n        const isInView = scrollTop >= pinStart && scrollTop <= pinEnd;\n        if (isInView && !stackCompletedRef.current) {\n          stackCompletedRef.current = true;\n          onStackComplete?.();\n        } else if (!isInView && stackCompletedRef.current) {\n          stackCompletedRef.current = false;\n        }\n      }\n    });\n\n    isUpdatingRef.current = false;\n  }, [\n    itemScale,\n    itemStackDistance,\n    stackPosition,\n    scaleEndPosition,\n    baseScale,\n    rotationAmount,\n    blurAmount,\n    onStackComplete,\n    calculateProgress,\n    parsePercentage,\n  ]);\n\n  return (\n    <div\n      ref={containerRef}\n      className={`scroll-stack ${className}`}\n      style={style}\n    >\n      {/* Debug indicator */}\n      <div style={{\n        position: 'fixed',\n        top: '20px',\n        right: '20px',\n        background: 'rgba(0,0,0,0.8)',\n        color: 'white',\n        padding: '10px',\n        borderRadius: '5px',\n        fontSize: '14px',\n        zIndex: 10000,\n        fontFamily: 'monospace'\n      }}>\n        ScrollStack Debug:<br/>\n        Progress: {(scrollProgress * 100).toFixed(0)}%<br/>\n        Cards: {childrenArray.length}\n      </div>\n\n      <div className=\"scroll-stack-container\">\n        {childrenArray.map((child, index) => (\n          <div\n            key={index}\n            className=\"scroll-stack-item\"\n            style={getItemStyle(index, childrenArray.length)}\n          >\n            {child}\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n};\n\nexport default ScrollStack;\n"], "mappings": ";;AAAA,SAASA,eAAe,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AAC5D,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,OAAO,MAAMC,eAAe,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,aAAa,GAAG;AAAG,CAAC,kBAC9DH,OAAA;EAAKI,SAAS,EAAE,qBAAqBD,aAAa,EAAE,CAACE,IAAI,CAAC,CAAE;EAAAH,QAAA,EAAEA;AAAQ;EAAAI,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAAM,CAC7E;AAACC,EAAA,GAFWT,eAAe;AAI5B,MAAMU,WAAW,GAAGA,CAAC;EACnBT,QAAQ;EACRE,SAAS,GAAG,EAAE;EACdQ,YAAY,GAAG,GAAG;EAClBC,SAAS,GAAG,IAAI;EAChBC,iBAAiB,GAAG,EAAE;EACtBC,aAAa,GAAG,KAAK;EACrBC,gBAAgB,GAAG,KAAK;EACxBC,SAAS,GAAG,IAAI;EAChBC,aAAa,GAAG,GAAG;EACnBC,cAAc,GAAG,CAAC;EAClBC,UAAU,GAAG,CAAC;EACdC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,WAAW,GAAG1B,MAAM,CAAC,IAAI,CAAC;EAChC,MAAM2B,iBAAiB,GAAG3B,MAAM,CAAC,KAAK,CAAC;EACvC,MAAM4B,iBAAiB,GAAG5B,MAAM,CAAC,IAAI,CAAC;EACtC,MAAM6B,QAAQ,GAAG7B,MAAM,CAAC,EAAE,CAAC;EAC3B,MAAM8B,iBAAiB,GAAG9B,MAAM,CAAC,IAAI+B,GAAG,CAAC,CAAC,CAAC;EAC3C,MAAMC,aAAa,GAAGhC,MAAM,CAAC,KAAK,CAAC;EAEnC,MAAMiC,iBAAiB,GAAGhC,WAAW,CAAC,CAACiC,SAAS,EAAEC,KAAK,EAAEC,GAAG,KAAK;IAC/D,IAAIF,SAAS,GAAGC,KAAK,EAAE,OAAO,CAAC;IAC/B,IAAID,SAAS,GAAGE,GAAG,EAAE,OAAO,CAAC;IAC7B,OAAO,CAACF,SAAS,GAAGC,KAAK,KAAKC,GAAG,GAAGD,KAAK,CAAC;EAC5C,CAAC,EAAE,EAAE,CAAC;EAEN,MAAME,eAAe,GAAGpC,WAAW,CAAC,CAACqC,KAAK,EAAEC,eAAe,KAAK;IAC9D,IAAI,OAAOD,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACE,QAAQ,CAAC,GAAG,CAAC,EAAE;MACpD,OAAQC,UAAU,CAACH,KAAK,CAAC,GAAG,GAAG,GAAIC,eAAe;IACpD;IACA,OAAOE,UAAU,CAACH,KAAK,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,oBAAoB,GAAGzC,WAAW,CAAC,MAAM;IAC7C,MAAM0C,QAAQ,GAAGjB,WAAW,CAACkB,OAAO;IACpC,IAAI,CAACD,QAAQ,IAAI,CAACd,QAAQ,CAACe,OAAO,CAACC,MAAM,IAAIb,aAAa,CAACY,OAAO,EAAE;IAEpEZ,aAAa,CAACY,OAAO,GAAG,IAAI;IAE5B,MAAMV,SAAS,GAAGS,QAAQ,CAACT,SAAS;IACpC,MAAMK,eAAe,GAAGI,QAAQ,CAACG,YAAY;IAC7C,MAAMC,eAAe,GAAGV,eAAe,CAACnB,aAAa,EAAEqB,eAAe,CAAC;IACvE,MAAMS,kBAAkB,GAAGX,eAAe,CAAClB,gBAAgB,EAAEoB,eAAe,CAAC;IAC7E,MAAMU,UAAU,GAAGN,QAAQ,CAACO,aAAa,CAAC,mBAAmB,CAAC;IAC9D,MAAMC,aAAa,GAAGF,UAAU,GAAGA,UAAU,CAACG,SAAS,GAAG,CAAC;IAE3DvB,QAAQ,CAACe,OAAO,CAACS,OAAO,CAAC,CAACC,IAAI,EAAEC,CAAC,KAAK;MACpC,IAAI,CAACD,IAAI,EAAE;MAEX,MAAME,OAAO,GAAGF,IAAI,CAACF,SAAS;MAC9B,MAAMK,YAAY,GAAGD,OAAO,GAAGT,eAAe,GAAI9B,iBAAiB,GAAGsC,CAAE;MACxE,MAAMG,UAAU,GAAGF,OAAO,GAAGR,kBAAkB;MAC/C,MAAMW,QAAQ,GAAGH,OAAO,GAAGT,eAAe,GAAI9B,iBAAiB,GAAGsC,CAAE;MACpE,MAAMK,MAAM,GAAGT,aAAa,GAAGZ,eAAe,GAAG,CAAC;MAElD,MAAMsB,aAAa,GAAG5B,iBAAiB,CAACC,SAAS,EAAEuB,YAAY,EAAEC,UAAU,CAAC;MAC5E,MAAMI,WAAW,GAAG1C,SAAS,GAAImC,CAAC,GAAGvC,SAAU;MAC/C,MAAM+C,KAAK,GAAG,CAAC,GAAGF,aAAa,IAAI,CAAC,GAAGC,WAAW,CAAC;MACnD,MAAME,QAAQ,GAAG1C,cAAc,GAAGiC,CAAC,GAAGjC,cAAc,GAAGuC,aAAa,GAAG,CAAC;MAExE,IAAII,IAAI,GAAG,CAAC;MACZ,IAAI1C,UAAU,EAAE;QACd,IAAI2C,YAAY,GAAG,CAAC;QACpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtC,QAAQ,CAACe,OAAO,CAACC,MAAM,EAAEsB,CAAC,EAAE,EAAE;UAChD,MAAMC,QAAQ,GAAGvC,QAAQ,CAACe,OAAO,CAACuB,CAAC,CAAC,CAACf,SAAS;UAC9C,MAAMiB,aAAa,GAAGD,QAAQ,GAAGrB,eAAe,GAAI9B,iBAAiB,GAAGkD,CAAE;UAC1E,IAAIjC,SAAS,IAAImC,aAAa,EAAE;YAC9BH,YAAY,GAAGC,CAAC;UAClB;QACF;QAEA,IAAIZ,CAAC,GAAGW,YAAY,EAAE;UACpB,MAAMI,YAAY,GAAGJ,YAAY,GAAGX,CAAC;UACrCU,IAAI,GAAGM,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEF,YAAY,GAAG/C,UAAU,CAAC;QAC/C;MACF;MAEA,IAAIkD,UAAU,GAAG,CAAC;MAClB,MAAMC,QAAQ,GAAGxC,SAAS,IAAIyB,QAAQ,IAAIzB,SAAS,IAAI0B,MAAM;MAE7D,IAAIc,QAAQ,EAAE;QACZD,UAAU,GAAGvC,SAAS,GAAGsB,OAAO,GAAGT,eAAe,GAAI9B,iBAAiB,GAAGsC,CAAE;MAC9E,CAAC,MAAM,IAAIrB,SAAS,GAAG0B,MAAM,EAAE;QAC7Ba,UAAU,GAAGb,MAAM,GAAGJ,OAAO,GAAGT,eAAe,GAAI9B,iBAAiB,GAAGsC,CAAE;MAC3E;MAEA,MAAMoB,YAAY,GAAG;QACnBF,UAAU,EAAEF,IAAI,CAACK,KAAK,CAACH,UAAU,GAAG,GAAG,CAAC,GAAG,GAAG;QAC9CV,KAAK,EAAEQ,IAAI,CAACK,KAAK,CAACb,KAAK,GAAG,IAAI,CAAC,GAAG,IAAI;QACtCC,QAAQ,EAAEO,IAAI,CAACK,KAAK,CAACZ,QAAQ,GAAG,GAAG,CAAC,GAAG,GAAG;QAC1CC,IAAI,EAAEM,IAAI,CAACK,KAAK,CAACX,IAAI,GAAG,GAAG,CAAC,GAAG;MACjC,CAAC;MAED,MAAMY,aAAa,GAAG/C,iBAAiB,CAACc,OAAO,CAACkC,GAAG,CAACvB,CAAC,CAAC;MACtD,MAAMwB,UAAU,GAAG,CAACF,aAAa,IAC/BN,IAAI,CAACS,GAAG,CAACH,aAAa,CAACJ,UAAU,GAAGE,YAAY,CAACF,UAAU,CAAC,GAAG,GAAG,IAClEF,IAAI,CAACS,GAAG,CAACH,aAAa,CAACd,KAAK,GAAGY,YAAY,CAACZ,KAAK,CAAC,GAAG,KAAK,IAC1DQ,IAAI,CAACS,GAAG,CAACH,aAAa,CAACb,QAAQ,GAAGW,YAAY,CAACX,QAAQ,CAAC,GAAG,GAAG,IAC9DO,IAAI,CAACS,GAAG,CAACH,aAAa,CAACZ,IAAI,GAAGU,YAAY,CAACV,IAAI,CAAC,GAAG,GAAG;MAExD,IAAIc,UAAU,EAAE;QACd,MAAME,SAAS,GAAG,kBAAkBN,YAAY,CAACF,UAAU,gBAAgBE,YAAY,CAACZ,KAAK,YAAYY,YAAY,CAACX,QAAQ,MAAM;QACpI,MAAMkB,MAAM,GAAGP,YAAY,CAACV,IAAI,GAAG,CAAC,GAAG,QAAQU,YAAY,CAACV,IAAI,KAAK,GAAG,EAAE;QAE1EX,IAAI,CAAC6B,KAAK,CAACF,SAAS,GAAGA,SAAS;QAChC3B,IAAI,CAAC6B,KAAK,CAACD,MAAM,GAAGA,MAAM;QAE1BpD,iBAAiB,CAACc,OAAO,CAACwC,GAAG,CAAC7B,CAAC,EAAEoB,YAAY,CAAC;MAChD;MAEA,IAAIpB,CAAC,KAAK1B,QAAQ,CAACe,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;QACrC,MAAMwC,QAAQ,GAAGnD,SAAS,IAAIyB,QAAQ,IAAIzB,SAAS,IAAI0B,MAAM;QAC7D,IAAIyB,QAAQ,IAAI,CAAC1D,iBAAiB,CAACiB,OAAO,EAAE;UAC1CjB,iBAAiB,CAACiB,OAAO,GAAG,IAAI;UAChCpB,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAG,CAAC;QACrB,CAAC,MAAM,IAAI,CAAC6D,QAAQ,IAAI1D,iBAAiB,CAACiB,OAAO,EAAE;UACjDjB,iBAAiB,CAACiB,OAAO,GAAG,KAAK;QACnC;MACF;IACF,CAAC,CAAC;IAEFZ,aAAa,CAACY,OAAO,GAAG,KAAK;EAC/B,CAAC,EAAE,CACD5B,SAAS,EACTC,iBAAiB,EACjBC,aAAa,EACbC,gBAAgB,EAChBC,SAAS,EACTE,cAAc,EACdC,UAAU,EACVC,eAAe,EACfS,iBAAiB,EACjBI,eAAe,CAChB,CAAC;EAEF,oBACElC,OAAA;IACEmF,GAAG,EAAEC,YAAa;IAClBhF,SAAS,EAAE,gBAAgBA,SAAS,EAAG;IACvC4E,KAAK,EAAEA,KAAM;IAAA9E,QAAA,gBAGbF,OAAA;MAAKgF,KAAK,EAAE;QACVK,QAAQ,EAAE,OAAO;QACjBC,GAAG,EAAE,MAAM;QACXC,KAAK,EAAE,MAAM;QACbC,UAAU,EAAE,iBAAiB;QAC7BC,KAAK,EAAE,OAAO;QACdC,OAAO,EAAE,MAAM;QACfC,YAAY,EAAE,KAAK;QACnBC,QAAQ,EAAE,MAAM;QAChBC,MAAM,EAAE,KAAK;QACbC,UAAU,EAAE;MACd,CAAE;MAAA5F,QAAA,GAAC,oBACiB,eAAAF,OAAA;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,cACb,EAAC,CAACsF,cAAc,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC,eAAAhG,OAAA;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,WAC5C,EAACwF,aAAa,CAACvD,MAAM;IAAA;MAAApC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eAENT,OAAA;MAAKI,SAAS,EAAC,wBAAwB;MAAAF,QAAA,EACpC+F,aAAa,CAACC,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBAC9BpG,OAAA;QAEEI,SAAS,EAAC,mBAAmB;QAC7B4E,KAAK,EAAEqB,YAAY,CAACD,KAAK,EAAEH,aAAa,CAACvD,MAAM,CAAE;QAAAxC,QAAA,EAEhDiG;MAAK,GAJDC,KAAK;QAAA9F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKP,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACa,EAAA,CA7KIX,WAAW;AAAA2F,GAAA,GAAX3F,WAAW;AA+KjB,eAAeA,WAAW;AAAC,IAAAD,EAAA,EAAA4F,GAAA;AAAAC,YAAA,CAAA7F,EAAA;AAAA6F,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}