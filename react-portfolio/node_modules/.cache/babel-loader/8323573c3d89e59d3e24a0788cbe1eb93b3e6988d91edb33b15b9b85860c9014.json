{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Hero.js\";\nimport React from 'react';\nimport TextPressure from './TextPressure';\nimport ShinyText from './ShinyText';\nimport Navigation from './Navigation';\nimport ScrollFloat from './ScrollFloat';\nimport './Hero.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Hero = () => {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"hero\",\n    id: \"home\",\n    children: [/*#__PURE__*/_jsxDEV(Navigation, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hero-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-title\",\n        children: /*#__PURE__*/_jsxDEV(TextPressure, {\n          text: \"DevDoses\",\n          fontSize: \"6rem\",\n          pressureIntensity: 0.5,\n          initialOpacity: 1,\n          className: \"main-title\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-subtitle\",\n        children: /*#__PURE__*/_jsxDEV(ShinyText, {\n          text: \"Creative Developer Portfolio\",\n          fontSize: \"1.5rem\",\n          shimmerDuration: 3000,\n          className: \"subtitle\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-description\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Building amazing digital experiences with modern web technologies\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hero-bg\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"gradient-orb orb-1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"gradient-orb orb-2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"gradient-orb orb-3\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this);\n};\n_c = Hero;\nexport default Hero;\nvar _c;\n$RefreshReg$(_c, \"Hero\");", "map": {"version": 3, "names": ["React", "TextPressure", "ShinyText", "Navigation", "ScrollFloat", "jsxDEV", "_jsxDEV", "Hero", "className", "id", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "text", "fontSize", "pressureIntensity", "initialOpacity", "shimmerDuration", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Hero.js"], "sourcesContent": ["import React from 'react';\nimport TextPressure from './TextPressure';\nimport ShinyText from './ShinyText';\nimport Navigation from './Navigation';\nimport ScrollFloat from './ScrollFloat';\nimport './Hero.css';\n\nconst Hero = () => {\n  return (\n    <section className=\"hero\" id=\"home\">\n      {/* Navigation Menu */}\n      <Navigation />\n\n      {/* Main Content */}\n      <div className=\"hero-content\">\n        <div className=\"hero-title\">\n          <TextPressure\n            text=\"DevDoses\"\n            fontSize=\"6rem\"\n            pressureIntensity={0.5}\n            initialOpacity={1}\n            className=\"main-title\"\n          />\n        </div>\n\n        <div className=\"hero-subtitle\">\n          <ShinyText\n            text=\"Creative Developer Portfolio\"\n            fontSize=\"1.5rem\"\n            shimmerDuration={3000}\n            className=\"subtitle\"\n          />\n        </div>\n\n        <div className=\"hero-description\">\n          <p>Building amazing digital experiences with modern web technologies</p>\n        </div>\n      </div>\n\n      {/* Background Elements */}\n      <div className=\"hero-bg\">\n        <div className=\"gradient-orb orb-1\"></div>\n        <div className=\"gradient-orb orb-2\"></div>\n        <div className=\"gradient-orb orb-3\"></div>\n      </div>\n    </section>\n  );\n};\n\nexport default Hero;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAO,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpB,MAAMC,IAAI,GAAGA,CAAA,KAAM;EACjB,oBACED,OAAA;IAASE,SAAS,EAAC,MAAM;IAACC,EAAE,EAAC,MAAM;IAAAC,QAAA,gBAEjCJ,OAAA,CAACH,UAAU;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGdR,OAAA;MAAKE,SAAS,EAAC,cAAc;MAAAE,QAAA,gBAC3BJ,OAAA;QAAKE,SAAS,EAAC,YAAY;QAAAE,QAAA,eACzBJ,OAAA,CAACL,YAAY;UACXc,IAAI,EAAC,UAAU;UACfC,QAAQ,EAAC,MAAM;UACfC,iBAAiB,EAAE,GAAI;UACvBC,cAAc,EAAE,CAAE;UAClBV,SAAS,EAAC;QAAY;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENR,OAAA;QAAKE,SAAS,EAAC,eAAe;QAAAE,QAAA,eAC5BJ,OAAA,CAACJ,SAAS;UACRa,IAAI,EAAC,8BAA8B;UACnCC,QAAQ,EAAC,QAAQ;UACjBG,eAAe,EAAE,IAAK;UACtBX,SAAS,EAAC;QAAU;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENR,OAAA;QAAKE,SAAS,EAAC,kBAAkB;QAAAE,QAAA,eAC/BJ,OAAA;UAAAI,QAAA,EAAG;QAAiE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNR,OAAA;MAAKE,SAAS,EAAC,SAAS;MAAAE,QAAA,gBACtBJ,OAAA;QAAKE,SAAS,EAAC;MAAoB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC1CR,OAAA;QAAKE,SAAS,EAAC;MAAoB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC1CR,OAAA;QAAKE,SAAS,EAAC;MAAoB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACM,EAAA,GAxCIb,IAAI;AA0CV,eAAeA,IAAI;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}