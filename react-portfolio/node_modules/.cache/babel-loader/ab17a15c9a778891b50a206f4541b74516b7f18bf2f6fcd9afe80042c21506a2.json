{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ScrollStack.js\",\n  _s = $RefreshSig$();\nimport { useLayoutEffect, useRef, useCallback } from \"react\";\nimport \"./ScrollStack.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const ScrollStackItem = ({\n  children,\n  itemClassName = \"\"\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: `scroll-stack-card ${itemClassName}`.trim(),\n  children: children\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 5,\n  columnNumber: 3\n}, this);\n_c = ScrollStackItem;\nconst ScrollStack = ({\n  children,\n  className = \"\",\n  itemDistance = 100,\n  itemScale = 0.03,\n  itemStackDistance = 30,\n  stackPosition = \"20%\",\n  scaleEndPosition = \"10%\",\n  baseScale = 0.85,\n  rotationAmount = 0,\n  blurAmount = 0,\n  onStackComplete\n}) => {\n  _s();\n  const scrollerRef = useRef(null);\n  const stackCompletedRef = useRef(false);\n  const animationFrameRef = useRef(null);\n  const lenisRef = useRef(null);\n  const cardsRef = useRef([]);\n  const lastTransformsRef = useRef(new Map());\n  const isUpdatingRef = useRef(false);\n  const calculateProgress = useCallback((scrollTop, start, end) => {\n    if (scrollTop < start) return 0;\n    if (scrollTop > end) return 1;\n    return (scrollTop - start) / (end - start);\n  }, []);\n  const parsePercentage = useCallback((value, containerHeight) => {\n    if (typeof value === 'string' && value.includes('%')) {\n      return parseFloat(value) / 100 * containerHeight;\n    }\n    return parseFloat(value);\n  }, []);\n  const updateCardTransforms = useCallback(() => {\n    const scroller = scrollerRef.current;\n    if (!scroller || !cardsRef.current.length || isUpdatingRef.current) return;\n    isUpdatingRef.current = true;\n\n    // Use window scroll instead of container scroll\n    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;\n    const containerHeight = window.innerHeight;\n    const stackPositionPx = parsePercentage(stackPosition, containerHeight);\n    const scaleEndPositionPx = parsePercentage(scaleEndPosition, containerHeight);\n    const endElement = scroller.querySelector('.scroll-stack-end');\n    const endElementTop = endElement ? endElement.offsetTop : 0;\n    cardsRef.current.forEach((card, i) => {\n      if (!card) return;\n      const cardTop = card.offsetTop;\n      const triggerStart = cardTop - stackPositionPx - itemStackDistance * i;\n      const triggerEnd = cardTop - scaleEndPositionPx;\n      const pinStart = cardTop - stackPositionPx - itemStackDistance * i;\n      const pinEnd = endElementTop - containerHeight / 2;\n      const scaleProgress = calculateProgress(scrollTop, triggerStart, triggerEnd);\n      const targetScale = baseScale + i * itemScale;\n      const scale = 1 - scaleProgress * (1 - targetScale);\n      const rotation = rotationAmount ? i * rotationAmount * scaleProgress : 0;\n      let blur = 0;\n      if (blurAmount) {\n        let topCardIndex = 0;\n        for (let j = 0; j < cardsRef.current.length; j++) {\n          const jCardTop = cardsRef.current[j].offsetTop;\n          const jTriggerStart = jCardTop - stackPositionPx - itemStackDistance * j;\n          if (scrollTop >= jTriggerStart) {\n            topCardIndex = j;\n          }\n        }\n        if (i < topCardIndex) {\n          const depthInStack = topCardIndex - i;\n          blur = Math.max(0, depthInStack * blurAmount);\n        }\n      }\n      let translateY = 0;\n      const isPinned = scrollTop >= pinStart && scrollTop <= pinEnd;\n      if (isPinned) {\n        translateY = scrollTop - cardTop + stackPositionPx + itemStackDistance * i;\n      } else if (scrollTop > pinEnd) {\n        translateY = pinEnd - cardTop + stackPositionPx + itemStackDistance * i;\n      }\n      const newTransform = {\n        translateY: Math.round(translateY * 100) / 100,\n        scale: Math.round(scale * 1000) / 1000,\n        rotation: Math.round(rotation * 100) / 100,\n        blur: Math.round(blur * 100) / 100\n      };\n      const lastTransform = lastTransformsRef.current.get(i);\n      const hasChanged = !lastTransform || Math.abs(lastTransform.translateY - newTransform.translateY) > 0.1 || Math.abs(lastTransform.scale - newTransform.scale) > 0.001 || Math.abs(lastTransform.rotation - newTransform.rotation) > 0.1 || Math.abs(lastTransform.blur - newTransform.blur) > 0.1;\n      if (hasChanged) {\n        const transform = `translate3d(0, ${newTransform.translateY}px, 0) scale(${newTransform.scale}) rotate(${newTransform.rotation}deg)`;\n        const filter = newTransform.blur > 0 ? `blur(${newTransform.blur}px)` : '';\n        card.style.transform = transform;\n        card.style.filter = filter;\n        lastTransformsRef.current.set(i, newTransform);\n\n        // Debug log\n        if (i === 0) {\n          console.log(`ScrollStack Card ${i}: Y=${newTransform.translateY}, Scale=${newTransform.scale}, ScrollTop=${scrollTop.toFixed(0)}`);\n        }\n      }\n      if (i === cardsRef.current.length - 1) {\n        const isInView = scrollTop >= pinStart && scrollTop <= pinEnd;\n        if (isInView && !stackCompletedRef.current) {\n          stackCompletedRef.current = true;\n          onStackComplete === null || onStackComplete === void 0 ? void 0 : onStackComplete();\n        } else if (!isInView && stackCompletedRef.current) {\n          stackCompletedRef.current = false;\n        }\n      }\n    });\n    isUpdatingRef.current = false;\n  }, [itemScale, itemStackDistance, stackPosition, scaleEndPosition, baseScale, rotationAmount, blurAmount, onStackComplete, calculateProgress, parsePercentage]);\n  const handleScroll = useCallback(() => {\n    updateCardTransforms();\n  }, [updateCardTransforms]);\n  const setupScrollListener = useCallback(() => {\n    // Use window scroll instead of Lenis\n    window.addEventListener('scroll', handleScroll, {\n      passive: true\n    });\n\n    // Initial call to set up transforms\n    updateCardTransforms();\n    return () => {\n      window.removeEventListener('scroll', handleScroll);\n    };\n  }, [handleScroll, updateCardTransforms]);\n  useLayoutEffect(() => {\n    const scroller = scrollerRef.current;\n    if (!scroller) return;\n    const cards = Array.from(scroller.querySelectorAll(\".scroll-stack-card\"));\n    cardsRef.current = cards;\n    const transformsCache = lastTransformsRef.current;\n    cards.forEach((card, i) => {\n      if (i < cards.length - 1) {\n        card.style.marginBottom = `${itemDistance}px`;\n      }\n      card.style.willChange = 'transform, filter';\n      card.style.transformOrigin = 'top center';\n      card.style.backfaceVisibility = 'hidden';\n      card.style.transform = 'translateZ(0)';\n      card.style.webkitTransform = 'translateZ(0)';\n      card.style.perspective = '1000px';\n      card.style.webkitPerspective = '1000px';\n    });\n    const cleanup = setupScrollListener();\n    return () => {\n      cleanup();\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n      }\n      stackCompletedRef.current = false;\n      cardsRef.current = [];\n      transformsCache.clear();\n      isUpdatingRef.current = false;\n    };\n  }, [itemDistance, itemScale, itemStackDistance, stackPosition, scaleEndPosition, baseScale, rotationAmount, blurAmount, onStackComplete, setupScrollListener, updateCardTransforms]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `scroll-stack-scroller ${className}`.trim(),\n    ref: scrollerRef,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"scroll-stack-inner\",\n      children: [children, /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"scroll-stack-end\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 218,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 214,\n    columnNumber: 5\n  }, this);\n};\n_s(ScrollStack, \"5Jo7tsukcjLNzgYEr8R9MDgKKJU=\");\n_c2 = ScrollStack;\nexport default ScrollStack;\nvar _c, _c2;\n$RefreshReg$(_c, \"ScrollStackItem\");\n$RefreshReg$(_c2, \"ScrollStack\");", "map": {"version": 3, "names": ["useLayoutEffect", "useRef", "useCallback", "jsxDEV", "_jsxDEV", "ScrollStackItem", "children", "itemClassName", "className", "trim", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "ScrollStack", "itemDistance", "itemScale", "itemStackDistance", "stackPosition", "scaleEndPosition", "baseScale", "rotationAmount", "blurAmount", "onStackComplete", "_s", "scrollerRef", "stackCompletedRef", "animationFrameRef", "lenisRef", "cardsRef", "lastTransformsRef", "Map", "isUpdatingRef", "calculateProgress", "scrollTop", "start", "end", "parsePercentage", "value", "containerHeight", "includes", "parseFloat", "updateCardTransforms", "scroller", "current", "length", "window", "pageYOffset", "document", "documentElement", "innerHeight", "stackPositionPx", "scaleEndPositionPx", "endElement", "querySelector", "endElementTop", "offsetTop", "for<PERSON>ach", "card", "i", "cardTop", "triggerStart", "triggerEnd", "pinStart", "pinEnd", "scaleProgress", "targetScale", "scale", "rotation", "blur", "topCardIndex", "j", "jCardTop", "jTriggerStart", "depthInStack", "Math", "max", "translateY", "isPinned", "newTransform", "round", "lastTransform", "get", "has<PERSON><PERSON>ed", "abs", "transform", "filter", "style", "set", "console", "log", "toFixed", "isInView", "handleScroll", "setupScrollListener", "addEventListener", "passive", "removeEventListener", "cards", "Array", "from", "querySelectorAll", "transforms<PERSON>ache", "marginBottom", "<PERSON><PERSON><PERSON><PERSON>", "transform<PERSON><PERSON>in", "backfaceVisibility", "webkitTransform", "perspective", "webkitPerspective", "cleanup", "cancelAnimationFrame", "clear", "ref", "_c2", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ScrollStack.js"], "sourcesContent": ["import { useLayoutEffect, useRef, useCallback } from \"react\";\nimport \"./ScrollStack.css\";\n\nexport const ScrollStackItem = ({ children, itemClassName = \"\" }) => (\n  <div className={`scroll-stack-card ${itemClassName}`.trim()}>{children}</div>\n);\n\nconst ScrollStack = ({\n  children,\n  className = \"\",\n  itemDistance = 100,\n  itemScale = 0.03,\n  itemStackDistance = 30,\n  stackPosition = \"20%\",\n  scaleEndPosition = \"10%\",\n  baseScale = 0.85,\n  rotationAmount = 0,\n  blurAmount = 0,\n  onStackComplete,\n}) => {\n  const scrollerRef = useRef(null);\n  const stackCompletedRef = useRef(false);\n  const animationFrameRef = useRef(null);\n  const lenisRef = useRef(null);\n  const cardsRef = useRef([]);\n  const lastTransformsRef = useRef(new Map());\n  const isUpdatingRef = useRef(false);\n\n  const calculateProgress = useCallback((scrollTop, start, end) => {\n    if (scrollTop < start) return 0;\n    if (scrollTop > end) return 1;\n    return (scrollTop - start) / (end - start);\n  }, []);\n\n  const parsePercentage = useCallback((value, containerHeight) => {\n    if (typeof value === 'string' && value.includes('%')) {\n      return (parseFloat(value) / 100) * containerHeight;\n    }\n    return parseFloat(value);\n  }, []);\n\n  const updateCardTransforms = useCallback(() => {\n    const scroller = scrollerRef.current;\n    if (!scroller || !cardsRef.current.length || isUpdatingRef.current) return;\n\n    isUpdatingRef.current = true;\n\n    // Use window scroll instead of container scroll\n    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;\n    const containerHeight = window.innerHeight;\n    const stackPositionPx = parsePercentage(stackPosition, containerHeight);\n    const scaleEndPositionPx = parsePercentage(scaleEndPosition, containerHeight);\n    const endElement = scroller.querySelector('.scroll-stack-end');\n    const endElementTop = endElement ? endElement.offsetTop : 0;\n\n    cardsRef.current.forEach((card, i) => {\n      if (!card) return;\n\n      const cardTop = card.offsetTop;\n      const triggerStart = cardTop - stackPositionPx - (itemStackDistance * i);\n      const triggerEnd = cardTop - scaleEndPositionPx;\n      const pinStart = cardTop - stackPositionPx - (itemStackDistance * i);\n      const pinEnd = endElementTop - containerHeight / 2;\n\n      const scaleProgress = calculateProgress(scrollTop, triggerStart, triggerEnd);\n      const targetScale = baseScale + (i * itemScale);\n      const scale = 1 - scaleProgress * (1 - targetScale);\n      const rotation = rotationAmount ? i * rotationAmount * scaleProgress : 0;\n\n      let blur = 0;\n      if (blurAmount) {\n        let topCardIndex = 0;\n        for (let j = 0; j < cardsRef.current.length; j++) {\n          const jCardTop = cardsRef.current[j].offsetTop;\n          const jTriggerStart = jCardTop - stackPositionPx - (itemStackDistance * j);\n          if (scrollTop >= jTriggerStart) {\n            topCardIndex = j;\n          }\n        }\n\n        if (i < topCardIndex) {\n          const depthInStack = topCardIndex - i;\n          blur = Math.max(0, depthInStack * blurAmount);\n        }\n      }\n\n      let translateY = 0;\n      const isPinned = scrollTop >= pinStart && scrollTop <= pinEnd;\n\n      if (isPinned) {\n        translateY = scrollTop - cardTop + stackPositionPx + (itemStackDistance * i);\n      } else if (scrollTop > pinEnd) {\n        translateY = pinEnd - cardTop + stackPositionPx + (itemStackDistance * i);\n      }\n\n      const newTransform = {\n        translateY: Math.round(translateY * 100) / 100,\n        scale: Math.round(scale * 1000) / 1000,\n        rotation: Math.round(rotation * 100) / 100,\n        blur: Math.round(blur * 100) / 100\n      };\n\n      const lastTransform = lastTransformsRef.current.get(i);\n      const hasChanged = !lastTransform ||\n        Math.abs(lastTransform.translateY - newTransform.translateY) > 0.1 ||\n        Math.abs(lastTransform.scale - newTransform.scale) > 0.001 ||\n        Math.abs(lastTransform.rotation - newTransform.rotation) > 0.1 ||\n        Math.abs(lastTransform.blur - newTransform.blur) > 0.1;\n\n      if (hasChanged) {\n        const transform = `translate3d(0, ${newTransform.translateY}px, 0) scale(${newTransform.scale}) rotate(${newTransform.rotation}deg)`;\n        const filter = newTransform.blur > 0 ? `blur(${newTransform.blur}px)` : '';\n\n        card.style.transform = transform;\n        card.style.filter = filter;\n\n        lastTransformsRef.current.set(i, newTransform);\n\n        // Debug log\n        if (i === 0) {\n          console.log(`ScrollStack Card ${i}: Y=${newTransform.translateY}, Scale=${newTransform.scale}, ScrollTop=${scrollTop.toFixed(0)}`);\n        }\n      }\n\n      if (i === cardsRef.current.length - 1) {\n        const isInView = scrollTop >= pinStart && scrollTop <= pinEnd;\n        if (isInView && !stackCompletedRef.current) {\n          stackCompletedRef.current = true;\n          onStackComplete?.();\n        } else if (!isInView && stackCompletedRef.current) {\n          stackCompletedRef.current = false;\n        }\n      }\n    });\n\n    isUpdatingRef.current = false;\n  }, [\n    itemScale,\n    itemStackDistance,\n    stackPosition,\n    scaleEndPosition,\n    baseScale,\n    rotationAmount,\n    blurAmount,\n    onStackComplete,\n    calculateProgress,\n    parsePercentage,\n  ]);\n\n  const handleScroll = useCallback(() => {\n    updateCardTransforms();\n  }, [updateCardTransforms]);\n\n  const setupScrollListener = useCallback(() => {\n    // Use window scroll instead of Lenis\n    window.addEventListener('scroll', handleScroll, { passive: true });\n\n    // Initial call to set up transforms\n    updateCardTransforms();\n\n    return () => {\n      window.removeEventListener('scroll', handleScroll);\n    };\n  }, [handleScroll, updateCardTransforms]);\n\n  useLayoutEffect(() => {\n    const scroller = scrollerRef.current;\n    if (!scroller) return;\n\n    const cards = Array.from(scroller.querySelectorAll(\".scroll-stack-card\"));\n    cardsRef.current = cards;\n    const transformsCache = lastTransformsRef.current;\n\n    cards.forEach((card, i) => {\n      if (i < cards.length - 1) {\n        card.style.marginBottom = `${itemDistance}px`;\n      }\n      card.style.willChange = 'transform, filter';\n      card.style.transformOrigin = 'top center';\n      card.style.backfaceVisibility = 'hidden';\n      card.style.transform = 'translateZ(0)';\n      card.style.webkitTransform = 'translateZ(0)';\n      card.style.perspective = '1000px';\n      card.style.webkitPerspective = '1000px';\n    });\n\n    const cleanup = setupScrollListener();\n\n    return () => {\n      cleanup();\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n      }\n      stackCompletedRef.current = false;\n      cardsRef.current = [];\n      transformsCache.clear();\n      isUpdatingRef.current = false;\n    };\n  }, [\n    itemDistance,\n    itemScale,\n    itemStackDistance,\n    stackPosition,\n    scaleEndPosition,\n    baseScale,\n    rotationAmount,\n    blurAmount,\n    onStackComplete,\n    setupScrollListener,\n    updateCardTransforms,\n  ]);\n\n  return (\n    <div\n      className={`scroll-stack-scroller ${className}`.trim()}\n      ref={scrollerRef}\n    >\n      <div className=\"scroll-stack-inner\">\n        {children}\n        <div className=\"scroll-stack-end\" />\n      </div>\n    </div>\n  );\n};\n\nexport default ScrollStack;\n"], "mappings": ";;AAAA,SAASA,eAAe,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AAC5D,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,OAAO,MAAMC,eAAe,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,aAAa,GAAG;AAAG,CAAC,kBAC9DH,OAAA;EAAKI,SAAS,EAAE,qBAAqBD,aAAa,EAAE,CAACE,IAAI,CAAC,CAAE;EAAAH,QAAA,EAAEA;AAAQ;EAAAI,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAAM,CAC7E;AAACC,EAAA,GAFWT,eAAe;AAI5B,MAAMU,WAAW,GAAGA,CAAC;EACnBT,QAAQ;EACRE,SAAS,GAAG,EAAE;EACdQ,YAAY,GAAG,GAAG;EAClBC,SAAS,GAAG,IAAI;EAChBC,iBAAiB,GAAG,EAAE;EACtBC,aAAa,GAAG,KAAK;EACrBC,gBAAgB,GAAG,KAAK;EACxBC,SAAS,GAAG,IAAI;EAChBC,cAAc,GAAG,CAAC;EAClBC,UAAU,GAAG,CAAC;EACdC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,WAAW,GAAGzB,MAAM,CAAC,IAAI,CAAC;EAChC,MAAM0B,iBAAiB,GAAG1B,MAAM,CAAC,KAAK,CAAC;EACvC,MAAM2B,iBAAiB,GAAG3B,MAAM,CAAC,IAAI,CAAC;EACtC,MAAM4B,QAAQ,GAAG5B,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAM6B,QAAQ,GAAG7B,MAAM,CAAC,EAAE,CAAC;EAC3B,MAAM8B,iBAAiB,GAAG9B,MAAM,CAAC,IAAI+B,GAAG,CAAC,CAAC,CAAC;EAC3C,MAAMC,aAAa,GAAGhC,MAAM,CAAC,KAAK,CAAC;EAEnC,MAAMiC,iBAAiB,GAAGhC,WAAW,CAAC,CAACiC,SAAS,EAAEC,KAAK,EAAEC,GAAG,KAAK;IAC/D,IAAIF,SAAS,GAAGC,KAAK,EAAE,OAAO,CAAC;IAC/B,IAAID,SAAS,GAAGE,GAAG,EAAE,OAAO,CAAC;IAC7B,OAAO,CAACF,SAAS,GAAGC,KAAK,KAAKC,GAAG,GAAGD,KAAK,CAAC;EAC5C,CAAC,EAAE,EAAE,CAAC;EAEN,MAAME,eAAe,GAAGpC,WAAW,CAAC,CAACqC,KAAK,EAAEC,eAAe,KAAK;IAC9D,IAAI,OAAOD,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACE,QAAQ,CAAC,GAAG,CAAC,EAAE;MACpD,OAAQC,UAAU,CAACH,KAAK,CAAC,GAAG,GAAG,GAAIC,eAAe;IACpD;IACA,OAAOE,UAAU,CAACH,KAAK,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,oBAAoB,GAAGzC,WAAW,CAAC,MAAM;IAC7C,MAAM0C,QAAQ,GAAGlB,WAAW,CAACmB,OAAO;IACpC,IAAI,CAACD,QAAQ,IAAI,CAACd,QAAQ,CAACe,OAAO,CAACC,MAAM,IAAIb,aAAa,CAACY,OAAO,EAAE;IAEpEZ,aAAa,CAACY,OAAO,GAAG,IAAI;;IAE5B;IACA,MAAMV,SAAS,GAAGY,MAAM,CAACC,WAAW,IAAIC,QAAQ,CAACC,eAAe,CAACf,SAAS;IAC1E,MAAMK,eAAe,GAAGO,MAAM,CAACI,WAAW;IAC1C,MAAMC,eAAe,GAAGd,eAAe,CAACnB,aAAa,EAAEqB,eAAe,CAAC;IACvE,MAAMa,kBAAkB,GAAGf,eAAe,CAAClB,gBAAgB,EAAEoB,eAAe,CAAC;IAC7E,MAAMc,UAAU,GAAGV,QAAQ,CAACW,aAAa,CAAC,mBAAmB,CAAC;IAC9D,MAAMC,aAAa,GAAGF,UAAU,GAAGA,UAAU,CAACG,SAAS,GAAG,CAAC;IAE3D3B,QAAQ,CAACe,OAAO,CAACa,OAAO,CAAC,CAACC,IAAI,EAAEC,CAAC,KAAK;MACpC,IAAI,CAACD,IAAI,EAAE;MAEX,MAAME,OAAO,GAAGF,IAAI,CAACF,SAAS;MAC9B,MAAMK,YAAY,GAAGD,OAAO,GAAGT,eAAe,GAAIlC,iBAAiB,GAAG0C,CAAE;MACxE,MAAMG,UAAU,GAAGF,OAAO,GAAGR,kBAAkB;MAC/C,MAAMW,QAAQ,GAAGH,OAAO,GAAGT,eAAe,GAAIlC,iBAAiB,GAAG0C,CAAE;MACpE,MAAMK,MAAM,GAAGT,aAAa,GAAGhB,eAAe,GAAG,CAAC;MAElD,MAAM0B,aAAa,GAAGhC,iBAAiB,CAACC,SAAS,EAAE2B,YAAY,EAAEC,UAAU,CAAC;MAC5E,MAAMI,WAAW,GAAG9C,SAAS,GAAIuC,CAAC,GAAG3C,SAAU;MAC/C,MAAMmD,KAAK,GAAG,CAAC,GAAGF,aAAa,IAAI,CAAC,GAAGC,WAAW,CAAC;MACnD,MAAME,QAAQ,GAAG/C,cAAc,GAAGsC,CAAC,GAAGtC,cAAc,GAAG4C,aAAa,GAAG,CAAC;MAExE,IAAII,IAAI,GAAG,CAAC;MACZ,IAAI/C,UAAU,EAAE;QACd,IAAIgD,YAAY,GAAG,CAAC;QACpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1C,QAAQ,CAACe,OAAO,CAACC,MAAM,EAAE0B,CAAC,EAAE,EAAE;UAChD,MAAMC,QAAQ,GAAG3C,QAAQ,CAACe,OAAO,CAAC2B,CAAC,CAAC,CAACf,SAAS;UAC9C,MAAMiB,aAAa,GAAGD,QAAQ,GAAGrB,eAAe,GAAIlC,iBAAiB,GAAGsD,CAAE;UAC1E,IAAIrC,SAAS,IAAIuC,aAAa,EAAE;YAC9BH,YAAY,GAAGC,CAAC;UAClB;QACF;QAEA,IAAIZ,CAAC,GAAGW,YAAY,EAAE;UACpB,MAAMI,YAAY,GAAGJ,YAAY,GAAGX,CAAC;UACrCU,IAAI,GAAGM,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEF,YAAY,GAAGpD,UAAU,CAAC;QAC/C;MACF;MAEA,IAAIuD,UAAU,GAAG,CAAC;MAClB,MAAMC,QAAQ,GAAG5C,SAAS,IAAI6B,QAAQ,IAAI7B,SAAS,IAAI8B,MAAM;MAE7D,IAAIc,QAAQ,EAAE;QACZD,UAAU,GAAG3C,SAAS,GAAG0B,OAAO,GAAGT,eAAe,GAAIlC,iBAAiB,GAAG0C,CAAE;MAC9E,CAAC,MAAM,IAAIzB,SAAS,GAAG8B,MAAM,EAAE;QAC7Ba,UAAU,GAAGb,MAAM,GAAGJ,OAAO,GAAGT,eAAe,GAAIlC,iBAAiB,GAAG0C,CAAE;MAC3E;MAEA,MAAMoB,YAAY,GAAG;QACnBF,UAAU,EAAEF,IAAI,CAACK,KAAK,CAACH,UAAU,GAAG,GAAG,CAAC,GAAG,GAAG;QAC9CV,KAAK,EAAEQ,IAAI,CAACK,KAAK,CAACb,KAAK,GAAG,IAAI,CAAC,GAAG,IAAI;QACtCC,QAAQ,EAAEO,IAAI,CAACK,KAAK,CAACZ,QAAQ,GAAG,GAAG,CAAC,GAAG,GAAG;QAC1CC,IAAI,EAAEM,IAAI,CAACK,KAAK,CAACX,IAAI,GAAG,GAAG,CAAC,GAAG;MACjC,CAAC;MAED,MAAMY,aAAa,GAAGnD,iBAAiB,CAACc,OAAO,CAACsC,GAAG,CAACvB,CAAC,CAAC;MACtD,MAAMwB,UAAU,GAAG,CAACF,aAAa,IAC/BN,IAAI,CAACS,GAAG,CAACH,aAAa,CAACJ,UAAU,GAAGE,YAAY,CAACF,UAAU,CAAC,GAAG,GAAG,IAClEF,IAAI,CAACS,GAAG,CAACH,aAAa,CAACd,KAAK,GAAGY,YAAY,CAACZ,KAAK,CAAC,GAAG,KAAK,IAC1DQ,IAAI,CAACS,GAAG,CAACH,aAAa,CAACb,QAAQ,GAAGW,YAAY,CAACX,QAAQ,CAAC,GAAG,GAAG,IAC9DO,IAAI,CAACS,GAAG,CAACH,aAAa,CAACZ,IAAI,GAAGU,YAAY,CAACV,IAAI,CAAC,GAAG,GAAG;MAExD,IAAIc,UAAU,EAAE;QACd,MAAME,SAAS,GAAG,kBAAkBN,YAAY,CAACF,UAAU,gBAAgBE,YAAY,CAACZ,KAAK,YAAYY,YAAY,CAACX,QAAQ,MAAM;QACpI,MAAMkB,MAAM,GAAGP,YAAY,CAACV,IAAI,GAAG,CAAC,GAAG,QAAQU,YAAY,CAACV,IAAI,KAAK,GAAG,EAAE;QAE1EX,IAAI,CAAC6B,KAAK,CAACF,SAAS,GAAGA,SAAS;QAChC3B,IAAI,CAAC6B,KAAK,CAACD,MAAM,GAAGA,MAAM;QAE1BxD,iBAAiB,CAACc,OAAO,CAAC4C,GAAG,CAAC7B,CAAC,EAAEoB,YAAY,CAAC;;QAE9C;QACA,IAAIpB,CAAC,KAAK,CAAC,EAAE;UACX8B,OAAO,CAACC,GAAG,CAAC,oBAAoB/B,CAAC,OAAOoB,YAAY,CAACF,UAAU,WAAWE,YAAY,CAACZ,KAAK,eAAejC,SAAS,CAACyD,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;QACpI;MACF;MAEA,IAAIhC,CAAC,KAAK9B,QAAQ,CAACe,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;QACrC,MAAM+C,QAAQ,GAAG1D,SAAS,IAAI6B,QAAQ,IAAI7B,SAAS,IAAI8B,MAAM;QAC7D,IAAI4B,QAAQ,IAAI,CAAClE,iBAAiB,CAACkB,OAAO,EAAE;UAC1ClB,iBAAiB,CAACkB,OAAO,GAAG,IAAI;UAChCrB,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAG,CAAC;QACrB,CAAC,MAAM,IAAI,CAACqE,QAAQ,IAAIlE,iBAAiB,CAACkB,OAAO,EAAE;UACjDlB,iBAAiB,CAACkB,OAAO,GAAG,KAAK;QACnC;MACF;IACF,CAAC,CAAC;IAEFZ,aAAa,CAACY,OAAO,GAAG,KAAK;EAC/B,CAAC,EAAE,CACD5B,SAAS,EACTC,iBAAiB,EACjBC,aAAa,EACbC,gBAAgB,EAChBC,SAAS,EACTC,cAAc,EACdC,UAAU,EACVC,eAAe,EACfU,iBAAiB,EACjBI,eAAe,CAChB,CAAC;EAEF,MAAMwD,YAAY,GAAG5F,WAAW,CAAC,MAAM;IACrCyC,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,CAACA,oBAAoB,CAAC,CAAC;EAE1B,MAAMoD,mBAAmB,GAAG7F,WAAW,CAAC,MAAM;IAC5C;IACA6C,MAAM,CAACiD,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,EAAE;MAAEG,OAAO,EAAE;IAAK,CAAC,CAAC;;IAElE;IACAtD,oBAAoB,CAAC,CAAC;IAEtB,OAAO,MAAM;MACXI,MAAM,CAACmD,mBAAmB,CAAC,QAAQ,EAAEJ,YAAY,CAAC;IACpD,CAAC;EACH,CAAC,EAAE,CAACA,YAAY,EAAEnD,oBAAoB,CAAC,CAAC;EAExC3C,eAAe,CAAC,MAAM;IACpB,MAAM4C,QAAQ,GAAGlB,WAAW,CAACmB,OAAO;IACpC,IAAI,CAACD,QAAQ,EAAE;IAEf,MAAMuD,KAAK,GAAGC,KAAK,CAACC,IAAI,CAACzD,QAAQ,CAAC0D,gBAAgB,CAAC,oBAAoB,CAAC,CAAC;IACzExE,QAAQ,CAACe,OAAO,GAAGsD,KAAK;IACxB,MAAMI,eAAe,GAAGxE,iBAAiB,CAACc,OAAO;IAEjDsD,KAAK,CAACzC,OAAO,CAAC,CAACC,IAAI,EAAEC,CAAC,KAAK;MACzB,IAAIA,CAAC,GAAGuC,KAAK,CAACrD,MAAM,GAAG,CAAC,EAAE;QACxBa,IAAI,CAAC6B,KAAK,CAACgB,YAAY,GAAG,GAAGxF,YAAY,IAAI;MAC/C;MACA2C,IAAI,CAAC6B,KAAK,CAACiB,UAAU,GAAG,mBAAmB;MAC3C9C,IAAI,CAAC6B,KAAK,CAACkB,eAAe,GAAG,YAAY;MACzC/C,IAAI,CAAC6B,KAAK,CAACmB,kBAAkB,GAAG,QAAQ;MACxChD,IAAI,CAAC6B,KAAK,CAACF,SAAS,GAAG,eAAe;MACtC3B,IAAI,CAAC6B,KAAK,CAACoB,eAAe,GAAG,eAAe;MAC5CjD,IAAI,CAAC6B,KAAK,CAACqB,WAAW,GAAG,QAAQ;MACjClD,IAAI,CAAC6B,KAAK,CAACsB,iBAAiB,GAAG,QAAQ;IACzC,CAAC,CAAC;IAEF,MAAMC,OAAO,GAAGhB,mBAAmB,CAAC,CAAC;IAErC,OAAO,MAAM;MACXgB,OAAO,CAAC,CAAC;MACT,IAAInF,iBAAiB,CAACiB,OAAO,EAAE;QAC7BmE,oBAAoB,CAACpF,iBAAiB,CAACiB,OAAO,CAAC;MACjD;MACAlB,iBAAiB,CAACkB,OAAO,GAAG,KAAK;MACjCf,QAAQ,CAACe,OAAO,GAAG,EAAE;MACrB0D,eAAe,CAACU,KAAK,CAAC,CAAC;MACvBhF,aAAa,CAACY,OAAO,GAAG,KAAK;IAC/B,CAAC;EACH,CAAC,EAAE,CACD7B,YAAY,EACZC,SAAS,EACTC,iBAAiB,EACjBC,aAAa,EACbC,gBAAgB,EAChBC,SAAS,EACTC,cAAc,EACdC,UAAU,EACVC,eAAe,EACfuE,mBAAmB,EACnBpD,oBAAoB,CACrB,CAAC;EAEF,oBACEvC,OAAA;IACEI,SAAS,EAAE,yBAAyBA,SAAS,EAAE,CAACC,IAAI,CAAC,CAAE;IACvDyG,GAAG,EAAExF,WAAY;IAAApB,QAAA,eAEjBF,OAAA;MAAKI,SAAS,EAAC,oBAAoB;MAAAF,QAAA,GAChCA,QAAQ,eACTF,OAAA;QAAKI,SAAS,EAAC;MAAkB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACY,EAAA,CAxNIV,WAAW;AAAAoG,GAAA,GAAXpG,WAAW;AA0NjB,eAAeA,WAAW;AAAC,IAAAD,EAAA,EAAAqG,GAAA;AAAAC,YAAA,CAAAtG,EAAA;AAAAsG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}