{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ScrollFloat.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport './ScrollFloat.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ScrollFloat = ({\n  children,\n  className = \"\",\n  style = {},\n  threshold = 0.1,\n  duration = 0.6,\n  delay = 0,\n  direction = 'up',\n  distance = 50\n}) => {\n  _s();\n  const [isVisible, setIsVisible] = useState(false);\n  const [hasAnimated, setHasAnimated] = useState(false);\n  const elementRef = useRef(null);\n  useEffect(() => {\n    const observer = new IntersectionObserver(([entry]) => {\n      if (entry.isIntersecting) {\n        setTimeout(() => {\n          setIsVisible(true);\n          setHasAnimated(true);\n        }, delay * 1000);\n      } else if (hasAnimated) {\n        // Reset animation when scrolling back up\n        setIsVisible(false);\n        setTimeout(() => {\n          setHasAnimated(false);\n        }, 100);\n      }\n    }, {\n      threshold: threshold,\n      rootMargin: '0px 0px -100px 0px'\n    });\n    if (elementRef.current) {\n      observer.observe(elementRef.current);\n    }\n    return () => {\n      if (elementRef.current) {\n        observer.unobserve(elementRef.current);\n      }\n    };\n  }, [threshold, delay, hasAnimated]);\n  const getTransform = () => {\n    if (isVisible) return 'translate3d(0, 0, 0)';\n    switch (direction) {\n      case 'up':\n        return `translate3d(0, ${distance}px, 0)`;\n      case 'down':\n        return `translate3d(0, -${distance}px, 0)`;\n      case 'left':\n        return `translate3d(${distance}px, 0, 0)`;\n      case 'right':\n        return `translate3d(-${distance}px, 0, 0)`;\n      default:\n        return `translate3d(0, ${distance}px, 0)`;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: elementRef,\n    className: `scroll-float ${className}`,\n    style: {\n      transform: getTransform(),\n      opacity: isVisible ? 1 : 0,\n      transition: `all ${duration}s cubic-bezier(0.25, 0.46, 0.45, 0.94)`,\n      ...style\n    },\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 69,\n    columnNumber: 5\n  }, this);\n};\n_s(ScrollFloat, \"mAxKEGUb0T67NydLbc9AVe6iVRk=\");\n_c = ScrollFloat;\nexport default ScrollFloat;\nvar _c;\n$RefreshReg$(_c, \"ScrollFloat\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "ScrollFloat", "children", "className", "style", "threshold", "duration", "delay", "direction", "distance", "_s", "isVisible", "setIsVisible", "hasAnimated", "setHasAnimated", "elementRef", "observer", "IntersectionObserver", "entry", "isIntersecting", "setTimeout", "rootMargin", "current", "observe", "unobserve", "getTransform", "ref", "transform", "opacity", "transition", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ScrollFloat.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport './ScrollFloat.css';\n\nconst ScrollFloat = ({ \n  children, \n  className = \"\", \n  style = {},\n  threshold = 0.1,\n  duration = 0.6,\n  delay = 0,\n  direction = 'up',\n  distance = 50\n}) => {\n  const [isVisible, setIsVisible] = useState(false);\n  const [hasAnimated, setHasAnimated] = useState(false);\n  const elementRef = useRef(null);\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setTimeout(() => {\n            setIsVisible(true);\n            setHasAnimated(true);\n          }, delay * 1000);\n        } else if (hasAnimated) {\n          // Reset animation when scrolling back up\n          setIsVisible(false);\n          setTimeout(() => {\n            setHasAnimated(false);\n          }, 100);\n        }\n      },\n      {\n        threshold: threshold,\n        rootMargin: '0px 0px -100px 0px'\n      }\n    );\n\n    if (elementRef.current) {\n      observer.observe(elementRef.current);\n    }\n\n    return () => {\n      if (elementRef.current) {\n        observer.unobserve(elementRef.current);\n      }\n    };\n  }, [threshold, delay, hasAnimated]);\n\n  const getTransform = () => {\n    if (isVisible) return 'translate3d(0, 0, 0)';\n    \n    switch (direction) {\n      case 'up':\n        return `translate3d(0, ${distance}px, 0)`;\n      case 'down':\n        return `translate3d(0, -${distance}px, 0)`;\n      case 'left':\n        return `translate3d(${distance}px, 0, 0)`;\n      case 'right':\n        return `translate3d(-${distance}px, 0, 0)`;\n      default:\n        return `translate3d(0, ${distance}px, 0)`;\n    }\n  };\n\n  return (\n    <div\n      ref={elementRef}\n      className={`scroll-float ${className}`}\n      style={{\n        transform: getTransform(),\n        opacity: isVisible ? 1 : 0,\n        transition: `all ${duration}s cubic-bezier(0.25, 0.46, 0.45, 0.94)`,\n        ...style\n      }}\n    >\n      {children}\n    </div>\n  );\n};\n\nexport default ScrollFloat;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,WAAW,GAAGA,CAAC;EACnBC,QAAQ;EACRC,SAAS,GAAG,EAAE;EACdC,KAAK,GAAG,CAAC,CAAC;EACVC,SAAS,GAAG,GAAG;EACfC,QAAQ,GAAG,GAAG;EACdC,KAAK,GAAG,CAAC;EACTC,SAAS,GAAG,IAAI;EAChBC,QAAQ,GAAG;AACb,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACiB,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAMmB,UAAU,GAAGjB,MAAM,CAAC,IAAI,CAAC;EAE/BD,SAAS,CAAC,MAAM;IACd,MAAMmB,QAAQ,GAAG,IAAIC,oBAAoB,CACvC,CAAC,CAACC,KAAK,CAAC,KAAK;MACX,IAAIA,KAAK,CAACC,cAAc,EAAE;QACxBC,UAAU,CAAC,MAAM;UACfR,YAAY,CAAC,IAAI,CAAC;UAClBE,cAAc,CAAC,IAAI,CAAC;QACtB,CAAC,EAAEP,KAAK,GAAG,IAAI,CAAC;MAClB,CAAC,MAAM,IAAIM,WAAW,EAAE;QACtB;QACAD,YAAY,CAAC,KAAK,CAAC;QACnBQ,UAAU,CAAC,MAAM;UACfN,cAAc,CAAC,KAAK,CAAC;QACvB,CAAC,EAAE,GAAG,CAAC;MACT;IACF,CAAC,EACD;MACET,SAAS,EAAEA,SAAS;MACpBgB,UAAU,EAAE;IACd,CACF,CAAC;IAED,IAAIN,UAAU,CAACO,OAAO,EAAE;MACtBN,QAAQ,CAACO,OAAO,CAACR,UAAU,CAACO,OAAO,CAAC;IACtC;IAEA,OAAO,MAAM;MACX,IAAIP,UAAU,CAACO,OAAO,EAAE;QACtBN,QAAQ,CAACQ,SAAS,CAACT,UAAU,CAACO,OAAO,CAAC;MACxC;IACF,CAAC;EACH,CAAC,EAAE,CAACjB,SAAS,EAAEE,KAAK,EAAEM,WAAW,CAAC,CAAC;EAEnC,MAAMY,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAId,SAAS,EAAE,OAAO,sBAAsB;IAE5C,QAAQH,SAAS;MACf,KAAK,IAAI;QACP,OAAO,kBAAkBC,QAAQ,QAAQ;MAC3C,KAAK,MAAM;QACT,OAAO,mBAAmBA,QAAQ,QAAQ;MAC5C,KAAK,MAAM;QACT,OAAO,eAAeA,QAAQ,WAAW;MAC3C,KAAK,OAAO;QACV,OAAO,gBAAgBA,QAAQ,WAAW;MAC5C;QACE,OAAO,kBAAkBA,QAAQ,QAAQ;IAC7C;EACF,CAAC;EAED,oBACET,OAAA;IACE0B,GAAG,EAAEX,UAAW;IAChBZ,SAAS,EAAE,gBAAgBA,SAAS,EAAG;IACvCC,KAAK,EAAE;MACLuB,SAAS,EAAEF,YAAY,CAAC,CAAC;MACzBG,OAAO,EAAEjB,SAAS,GAAG,CAAC,GAAG,CAAC;MAC1BkB,UAAU,EAAE,OAAOvB,QAAQ,wCAAwC;MACnE,GAAGF;IACL,CAAE;IAAAF,QAAA,EAEDA;EAAQ;IAAA4B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACvB,EAAA,CA9EIT,WAAW;AAAAiC,EAAA,GAAXjC,WAAW;AAgFjB,eAAeA,WAAW;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}