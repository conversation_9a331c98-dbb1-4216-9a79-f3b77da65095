{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ScrollStack.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport './ScrollStack.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ScrollStack = ({\n  children,\n  className = \"\",\n  style = {},\n  stackOffset = 20,\n  stackScale = 0.95,\n  threshold = 0.1\n}) => {\n  _s();\n  const [scrollProgress, setScrollProgress] = useState(0);\n  const containerRef = useRef(null);\n  useEffect(() => {\n    const handleScroll = () => {\n      if (!containerRef.current) return;\n      const rect = containerRef.current.getBoundingClientRect();\n      const windowHeight = window.innerHeight;\n      const containerTop = rect.top;\n      const containerHeight = rect.height;\n\n      // Calculate scroll progress based on container position\n      const startPoint = windowHeight * 0.8; // Start animation when 80% in view\n      const endPoint = -containerHeight * 0.2; // End when 20% out of view\n\n      let progress = 0;\n      if (containerTop <= startPoint && containerTop >= endPoint) {\n        progress = (startPoint - containerTop) / (startPoint - endPoint);\n        progress = Math.max(0, Math.min(1, progress));\n      } else if (containerTop < endPoint) {\n        progress = 1;\n      }\n      setScrollProgress(progress);\n\n      // Debug info\n      if (progress > 0 && progress < 1) {\n        console.log(`ScrollStack Progress: ${progress.toFixed(2)}, Cards should be ${progress < 0.5 ? 'stacked' : 'spreading'}`);\n      }\n    };\n    window.addEventListener('scroll', handleScroll, {\n      passive: true\n    });\n    handleScroll();\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n  const getStackStyle = (index, total) => {\n    // Initial stacked position (all cards start stacked)\n    const initialOffset = stackOffset * index;\n    const initialScale = Math.pow(stackScale, index);\n\n    // Final position (cards spread out)\n    const finalOffset = index * 100; // Spread cards vertically\n    const finalScale = 1;\n\n    // Interpolate between initial and final states\n    const currentOffset = initialOffset + scrollProgress * (finalOffset - initialOffset);\n    const currentScale = initialScale + scrollProgress * (finalScale - initialScale);\n\n    // Opacity based on scroll progress and index\n    let opacity = 1;\n    if (scrollProgress < 0.1) {\n      // Initially, only show first few cards\n      opacity = index < 2 ? 1 - index * 0.3 : 0.1;\n    } else {\n      // As we scroll, reveal more cards\n      const revealThreshold = index * 0.15;\n      opacity = scrollProgress > revealThreshold ? 1 : 0.3;\n    }\n    return {\n      transform: `\n        translate(-50%, -50%)\n        translateY(${currentOffset}px)\n        scale(${currentScale})\n        rotateX(${(1 - scrollProgress) * index * 5}deg)\n      `,\n      zIndex: total - index,\n      opacity: opacity,\n      transition: 'none',\n      // Remove transitions for smooth scroll-based animation\n      transformOrigin: 'center center'\n    };\n  };\n\n  // Convert children to array if it's not already\n  const childrenArray = React.Children.toArray(children);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: containerRef,\n    className: `scroll-stack ${className}`,\n    style: style,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: '10px',\n        right: '10px',\n        background: 'rgba(0,0,0,0.8)',\n        color: 'white',\n        padding: '10px',\n        borderRadius: '5px',\n        fontSize: '12px',\n        zIndex: 9999\n      },\n      children: [\"ScrollStack Progress: \", (scrollProgress * 100).toFixed(0), \"%\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"scroll-stack-container\",\n      children: childrenArray.map((child, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"scroll-stack-item\",\n        style: getStackStyle(index, childrenArray.length),\n        children: child\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 92,\n    columnNumber: 5\n  }, this);\n};\n_s(ScrollStack, \"s1hDQaLO+Y+X3sQDuGrxH3NJatY=\");\n_c = ScrollStack;\nexport default ScrollStack;\nvar _c;\n$RefreshReg$(_c, \"ScrollStack\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "ScrollStack", "children", "className", "style", "stackOffset", "stackScale", "threshold", "_s", "scrollProgress", "setScrollProgress", "containerRef", "handleScroll", "current", "rect", "getBoundingClientRect", "windowHeight", "window", "innerHeight", "containerTop", "top", "containerHeight", "height", "startPoint", "endPoint", "progress", "Math", "max", "min", "console", "log", "toFixed", "addEventListener", "passive", "removeEventListener", "getStackStyle", "index", "total", "initialOffset", "initialScale", "pow", "finalOffset", "finalScale", "currentOffset", "currentScale", "opacity", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transform", "zIndex", "transition", "transform<PERSON><PERSON>in", "childrenA<PERSON>y", "Children", "toArray", "ref", "position", "right", "background", "color", "padding", "borderRadius", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "child", "length", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ScrollStack.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport './ScrollStack.css';\n\nconst ScrollStack = ({\n  children,\n  className = \"\",\n  style = {},\n  stackOffset = 20,\n  stackScale = 0.95,\n  threshold = 0.1\n}) => {\n  const [scrollProgress, setScrollProgress] = useState(0);\n  const containerRef = useRef(null);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      if (!containerRef.current) return;\n\n      const rect = containerRef.current.getBoundingClientRect();\n      const windowHeight = window.innerHeight;\n      const containerTop = rect.top;\n      const containerHeight = rect.height;\n\n      // Calculate scroll progress based on container position\n      const startPoint = windowHeight * 0.8; // Start animation when 80% in view\n      const endPoint = -containerHeight * 0.2; // End when 20% out of view\n\n      let progress = 0;\n      if (containerTop <= startPoint && containerTop >= endPoint) {\n        progress = (startPoint - containerTop) / (startPoint - endPoint);\n        progress = Math.max(0, Math.min(1, progress));\n      } else if (containerTop < endPoint) {\n        progress = 1;\n      }\n\n      setScrollProgress(progress);\n\n      // Debug info\n      if (progress > 0 && progress < 1) {\n        console.log(`ScrollStack Progress: ${progress.toFixed(2)}, Cards should be ${progress < 0.5 ? 'stacked' : 'spreading'}`);\n      }\n    };\n\n    window.addEventListener('scroll', handleScroll, { passive: true });\n    handleScroll();\n\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const getStackStyle = (index, total) => {\n    // Initial stacked position (all cards start stacked)\n    const initialOffset = stackOffset * index;\n    const initialScale = Math.pow(stackScale, index);\n\n    // Final position (cards spread out)\n    const finalOffset = index * 100; // Spread cards vertically\n    const finalScale = 1;\n\n    // Interpolate between initial and final states\n    const currentOffset = initialOffset + (scrollProgress * (finalOffset - initialOffset));\n    const currentScale = initialScale + (scrollProgress * (finalScale - initialScale));\n\n    // Opacity based on scroll progress and index\n    let opacity = 1;\n    if (scrollProgress < 0.1) {\n      // Initially, only show first few cards\n      opacity = index < 2 ? 1 - (index * 0.3) : 0.1;\n    } else {\n      // As we scroll, reveal more cards\n      const revealThreshold = index * 0.15;\n      opacity = scrollProgress > revealThreshold ? 1 : 0.3;\n    }\n\n    return {\n      transform: `\n        translate(-50%, -50%)\n        translateY(${currentOffset}px)\n        scale(${currentScale})\n        rotateX(${(1 - scrollProgress) * index * 5}deg)\n      `,\n      zIndex: total - index,\n      opacity: opacity,\n      transition: 'none', // Remove transitions for smooth scroll-based animation\n      transformOrigin: 'center center'\n    };\n  };\n\n  // Convert children to array if it's not already\n  const childrenArray = React.Children.toArray(children);\n\n  return (\n    <div\n      ref={containerRef}\n      className={`scroll-stack ${className}`}\n      style={style}\n    >\n      {/* Debug indicator */}\n      <div style={{\n        position: 'fixed',\n        top: '10px',\n        right: '10px',\n        background: 'rgba(0,0,0,0.8)',\n        color: 'white',\n        padding: '10px',\n        borderRadius: '5px',\n        fontSize: '12px',\n        zIndex: 9999\n      }}>\n        ScrollStack Progress: {(scrollProgress * 100).toFixed(0)}%\n      </div>\n\n      <div className=\"scroll-stack-container\">\n        {childrenArray.map((child, index) => (\n          <div\n            key={index}\n            className=\"scroll-stack-item\"\n            style={getStackStyle(index, childrenArray.length)}\n          >\n            {child}\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n};\n\nexport default ScrollStack;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,WAAW,GAAGA,CAAC;EACnBC,QAAQ;EACRC,SAAS,GAAG,EAAE;EACdC,KAAK,GAAG,CAAC,CAAC;EACVC,WAAW,GAAG,EAAE;EAChBC,UAAU,GAAG,IAAI;EACjBC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGd,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAMe,YAAY,GAAGb,MAAM,CAAC,IAAI,CAAC;EAEjCD,SAAS,CAAC,MAAM;IACd,MAAMe,YAAY,GAAGA,CAAA,KAAM;MACzB,IAAI,CAACD,YAAY,CAACE,OAAO,EAAE;MAE3B,MAAMC,IAAI,GAAGH,YAAY,CAACE,OAAO,CAACE,qBAAqB,CAAC,CAAC;MACzD,MAAMC,YAAY,GAAGC,MAAM,CAACC,WAAW;MACvC,MAAMC,YAAY,GAAGL,IAAI,CAACM,GAAG;MAC7B,MAAMC,eAAe,GAAGP,IAAI,CAACQ,MAAM;;MAEnC;MACA,MAAMC,UAAU,GAAGP,YAAY,GAAG,GAAG,CAAC,CAAC;MACvC,MAAMQ,QAAQ,GAAG,CAACH,eAAe,GAAG,GAAG,CAAC,CAAC;;MAEzC,IAAII,QAAQ,GAAG,CAAC;MAChB,IAAIN,YAAY,IAAII,UAAU,IAAIJ,YAAY,IAAIK,QAAQ,EAAE;QAC1DC,QAAQ,GAAG,CAACF,UAAU,GAAGJ,YAAY,KAAKI,UAAU,GAAGC,QAAQ,CAAC;QAChEC,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEH,QAAQ,CAAC,CAAC;MAC/C,CAAC,MAAM,IAAIN,YAAY,GAAGK,QAAQ,EAAE;QAClCC,QAAQ,GAAG,CAAC;MACd;MAEAf,iBAAiB,CAACe,QAAQ,CAAC;;MAE3B;MACA,IAAIA,QAAQ,GAAG,CAAC,IAAIA,QAAQ,GAAG,CAAC,EAAE;QAChCI,OAAO,CAACC,GAAG,CAAC,yBAAyBL,QAAQ,CAACM,OAAO,CAAC,CAAC,CAAC,qBAAqBN,QAAQ,GAAG,GAAG,GAAG,SAAS,GAAG,WAAW,EAAE,CAAC;MAC1H;IACF,CAAC;IAEDR,MAAM,CAACe,gBAAgB,CAAC,QAAQ,EAAEpB,YAAY,EAAE;MAAEqB,OAAO,EAAE;IAAK,CAAC,CAAC;IAClErB,YAAY,CAAC,CAAC;IAEd,OAAO,MAAMK,MAAM,CAACiB,mBAAmB,CAAC,QAAQ,EAAEtB,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMuB,aAAa,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACtC;IACA,MAAMC,aAAa,GAAGjC,WAAW,GAAG+B,KAAK;IACzC,MAAMG,YAAY,GAAGb,IAAI,CAACc,GAAG,CAAClC,UAAU,EAAE8B,KAAK,CAAC;;IAEhD;IACA,MAAMK,WAAW,GAAGL,KAAK,GAAG,GAAG,CAAC,CAAC;IACjC,MAAMM,UAAU,GAAG,CAAC;;IAEpB;IACA,MAAMC,aAAa,GAAGL,aAAa,GAAI7B,cAAc,IAAIgC,WAAW,GAAGH,aAAa,CAAE;IACtF,MAAMM,YAAY,GAAGL,YAAY,GAAI9B,cAAc,IAAIiC,UAAU,GAAGH,YAAY,CAAE;;IAElF;IACA,IAAIM,OAAO,GAAG,CAAC;IACf,IAAIpC,cAAc,GAAG,GAAG,EAAE;MACxB;MACAoC,OAAO,GAAGT,KAAK,GAAG,CAAC,GAAG,CAAC,GAAIA,KAAK,GAAG,GAAI,GAAG,GAAG;IAC/C,CAAC,MAAM;MACL;MACA,MAAMU,eAAe,GAAGV,KAAK,GAAG,IAAI;MACpCS,OAAO,GAAGpC,cAAc,GAAGqC,eAAe,GAAG,CAAC,GAAG,GAAG;IACtD;IAEA,OAAO;MACLC,SAAS,EAAE;AACjB;AACA,qBAAqBJ,aAAa;AAClC,gBAAgBC,YAAY;AAC5B,kBAAkB,CAAC,CAAC,GAAGnC,cAAc,IAAI2B,KAAK,GAAG,CAAC;AAClD,OAAO;MACDY,MAAM,EAAEX,KAAK,GAAGD,KAAK;MACrBS,OAAO,EAAEA,OAAO;MAChBI,UAAU,EAAE,MAAM;MAAE;MACpBC,eAAe,EAAE;IACnB,CAAC;EACH,CAAC;;EAED;EACA,MAAMC,aAAa,GAAGxD,KAAK,CAACyD,QAAQ,CAACC,OAAO,CAACnD,QAAQ,CAAC;EAEtD,oBACEF,OAAA;IACEsD,GAAG,EAAE3C,YAAa;IAClBR,SAAS,EAAE,gBAAgBA,SAAS,EAAG;IACvCC,KAAK,EAAEA,KAAM;IAAAF,QAAA,gBAGbF,OAAA;MAAKI,KAAK,EAAE;QACVmD,QAAQ,EAAE,OAAO;QACjBnC,GAAG,EAAE,MAAM;QACXoC,KAAK,EAAE,MAAM;QACbC,UAAU,EAAE,iBAAiB;QAC7BC,KAAK,EAAE,OAAO;QACdC,OAAO,EAAE,MAAM;QACfC,YAAY,EAAE,KAAK;QACnBC,QAAQ,EAAE,MAAM;QAChBb,MAAM,EAAE;MACV,CAAE;MAAA9C,QAAA,GAAC,wBACqB,EAAC,CAACO,cAAc,GAAG,GAAG,EAAEsB,OAAO,CAAC,CAAC,CAAC,EAAC,GAC3D;IAAA;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eAENjE,OAAA;MAAKG,SAAS,EAAC,wBAAwB;MAAAD,QAAA,EACpCiD,aAAa,CAACe,GAAG,CAAC,CAACC,KAAK,EAAE/B,KAAK,kBAC9BpC,OAAA;QAEEG,SAAS,EAAC,mBAAmB;QAC7BC,KAAK,EAAE+B,aAAa,CAACC,KAAK,EAAEe,aAAa,CAACiB,MAAM,CAAE;QAAAlE,QAAA,EAEjDiE;MAAK,GAJD/B,KAAK;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKP,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzD,EAAA,CAzHIP,WAAW;AAAAoE,EAAA,GAAXpE,WAAW;AA2HjB,eAAeA,WAAW;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}