{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ScrollStack.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport './ScrollStack.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ScrollStack = ({\n  children,\n  className = \"\",\n  style = {},\n  stackOffset = 20,\n  stackScale = 0.95,\n  threshold = 0.1\n}) => {\n  _s();\n  const [scrollProgress, setScrollProgress] = useState(0);\n  const [isVisible, setIsVisible] = useState(false);\n  const containerRef = useRef(null);\n  useEffect(() => {\n    const handleScroll = () => {\n      if (!containerRef.current) return;\n      const rect = containerRef.current.getBoundingClientRect();\n      const windowHeight = window.innerHeight;\n      const elementTop = rect.top;\n      const elementHeight = rect.height;\n\n      // Calculate visibility\n      const visibleTop = Math.max(0, -elementTop);\n      const visibleBottom = Math.min(elementHeight, windowHeight - elementTop);\n      const visibleHeight = Math.max(0, visibleBottom - visibleTop);\n      const visibilityRatio = visibleHeight / elementHeight;\n      setIsVisible(visibilityRatio > threshold);\n\n      // Calculate scroll progress (0 to 1)\n      const scrollStart = elementTop + elementHeight * 0.2;\n      const scrollEnd = elementTop - windowHeight * 0.8;\n      const progress = Math.max(0, Math.min(1, (scrollStart - scrollEnd) / (scrollStart - scrollEnd + elementHeight)));\n      setScrollProgress(progress);\n    };\n    window.addEventListener('scroll', handleScroll);\n    handleScroll(); // Initial call\n\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, [threshold]);\n  const getStackStyle = (index, total) => {\n    const baseOffset = stackOffset * index;\n    const baseScale = Math.pow(stackScale, index);\n\n    // Apply scroll-based transformations\n    const scrollOffset = scrollProgress * baseOffset * 2;\n    const scrollScale = baseScale + scrollProgress * (1 - baseScale) * 0.5;\n    return {\n      transform: `\n        translateY(${baseOffset - scrollOffset}px) \n        scale(${scrollScale})\n        translateZ(${-index * 10}px)\n      `,\n      zIndex: total - index,\n      opacity: isVisible ? 1 - index * 0.1 : 0.3,\n      transition: isVisible ? 'all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)' : 'opacity 0.3s ease'\n    };\n  };\n\n  // Convert children to array if it's not already\n  const childrenArray = React.Children.toArray(children);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: containerRef,\n    className: `scroll-stack ${className}`,\n    style: style,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"scroll-stack-container\",\n      children: childrenArray.map((child, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"scroll-stack-item\",\n        style: getStackStyle(index, childrenArray.length),\n        children: child\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 71,\n    columnNumber: 5\n  }, this);\n};\n_s(ScrollStack, \"ZJ9NqbTuOD8Nvn1O9a2CJWWXESw=\");\n_c = ScrollStack;\nexport default ScrollStack;\nvar _c;\n$RefreshReg$(_c, \"ScrollStack\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "ScrollStack", "children", "className", "style", "stackOffset", "stackScale", "threshold", "_s", "scrollProgress", "setScrollProgress", "isVisible", "setIsVisible", "containerRef", "handleScroll", "current", "rect", "getBoundingClientRect", "windowHeight", "window", "innerHeight", "elementTop", "top", "elementHeight", "height", "visibleTop", "Math", "max", "visibleBottom", "min", "visibleHeight", "visibilityRatio", "scrollStart", "scrollEnd", "progress", "addEventListener", "removeEventListener", "getStackStyle", "index", "total", "baseOffset", "baseScale", "pow", "scrollOffset", "scrollScale", "transform", "zIndex", "opacity", "transition", "childrenA<PERSON>y", "Children", "toArray", "ref", "map", "child", "length", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ScrollStack.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport './ScrollStack.css';\n\nconst ScrollStack = ({ \n  children, \n  className = \"\", \n  style = {},\n  stackOffset = 20,\n  stackScale = 0.95,\n  threshold = 0.1\n}) => {\n  const [scrollProgress, setScrollProgress] = useState(0);\n  const [isVisible, setIsVisible] = useState(false);\n  const containerRef = useRef(null);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      if (!containerRef.current) return;\n\n      const rect = containerRef.current.getBoundingClientRect();\n      const windowHeight = window.innerHeight;\n      const elementTop = rect.top;\n      const elementHeight = rect.height;\n\n      // Calculate visibility\n      const visibleTop = Math.max(0, -elementTop);\n      const visibleBottom = Math.min(elementHeight, windowHeight - elementTop);\n      const visibleHeight = Math.max(0, visibleBottom - visibleTop);\n      const visibilityRatio = visibleHeight / elementHeight;\n\n      setIsVisible(visibilityRatio > threshold);\n\n      // Calculate scroll progress (0 to 1)\n      const scrollStart = elementTop + elementHeight * 0.2;\n      const scrollEnd = elementTop - windowHeight * 0.8;\n      const progress = Math.max(0, Math.min(1, (scrollStart - scrollEnd) / (scrollStart - scrollEnd + elementHeight)));\n      \n      setScrollProgress(progress);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    handleScroll(); // Initial call\n\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, [threshold]);\n\n  const getStackStyle = (index, total) => {\n    const baseOffset = stackOffset * index;\n    const baseScale = Math.pow(stackScale, index);\n    \n    // Apply scroll-based transformations\n    const scrollOffset = scrollProgress * baseOffset * 2;\n    const scrollScale = baseScale + (scrollProgress * (1 - baseScale) * 0.5);\n    \n    return {\n      transform: `\n        translateY(${baseOffset - scrollOffset}px) \n        scale(${scrollScale})\n        translateZ(${-index * 10}px)\n      `,\n      zIndex: total - index,\n      opacity: isVisible ? 1 - (index * 0.1) : 0.3,\n      transition: isVisible ? 'all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)' : 'opacity 0.3s ease'\n    };\n  };\n\n  // Convert children to array if it's not already\n  const childrenArray = React.Children.toArray(children);\n\n  return (\n    <div\n      ref={containerRef}\n      className={`scroll-stack ${className}`}\n      style={style}\n    >\n      <div className=\"scroll-stack-container\">\n        {childrenArray.map((child, index) => (\n          <div\n            key={index}\n            className=\"scroll-stack-item\"\n            style={getStackStyle(index, childrenArray.length)}\n          >\n            {child}\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n};\n\nexport default ScrollStack;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,WAAW,GAAGA,CAAC;EACnBC,QAAQ;EACRC,SAAS,GAAG,EAAE;EACdC,KAAK,GAAG,CAAC,CAAC;EACVC,WAAW,GAAG,EAAE;EAChBC,UAAU,GAAG,IAAI;EACjBC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGd,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAMiB,YAAY,GAAGf,MAAM,CAAC,IAAI,CAAC;EAEjCD,SAAS,CAAC,MAAM;IACd,MAAMiB,YAAY,GAAGA,CAAA,KAAM;MACzB,IAAI,CAACD,YAAY,CAACE,OAAO,EAAE;MAE3B,MAAMC,IAAI,GAAGH,YAAY,CAACE,OAAO,CAACE,qBAAqB,CAAC,CAAC;MACzD,MAAMC,YAAY,GAAGC,MAAM,CAACC,WAAW;MACvC,MAAMC,UAAU,GAAGL,IAAI,CAACM,GAAG;MAC3B,MAAMC,aAAa,GAAGP,IAAI,CAACQ,MAAM;;MAEjC;MACA,MAAMC,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAACN,UAAU,CAAC;MAC3C,MAAMO,aAAa,GAAGF,IAAI,CAACG,GAAG,CAACN,aAAa,EAAEL,YAAY,GAAGG,UAAU,CAAC;MACxE,MAAMS,aAAa,GAAGJ,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEC,aAAa,GAAGH,UAAU,CAAC;MAC7D,MAAMM,eAAe,GAAGD,aAAa,GAAGP,aAAa;MAErDX,YAAY,CAACmB,eAAe,GAAGxB,SAAS,CAAC;;MAEzC;MACA,MAAMyB,WAAW,GAAGX,UAAU,GAAGE,aAAa,GAAG,GAAG;MACpD,MAAMU,SAAS,GAAGZ,UAAU,GAAGH,YAAY,GAAG,GAAG;MACjD,MAAMgB,QAAQ,GAAGR,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACG,GAAG,CAAC,CAAC,EAAE,CAACG,WAAW,GAAGC,SAAS,KAAKD,WAAW,GAAGC,SAAS,GAAGV,aAAa,CAAC,CAAC,CAAC;MAEhHb,iBAAiB,CAACwB,QAAQ,CAAC;IAC7B,CAAC;IAEDf,MAAM,CAACgB,gBAAgB,CAAC,QAAQ,EAAErB,YAAY,CAAC;IAC/CA,YAAY,CAAC,CAAC,CAAC,CAAC;;IAEhB,OAAO,MAAMK,MAAM,CAACiB,mBAAmB,CAAC,QAAQ,EAAEtB,YAAY,CAAC;EACjE,CAAC,EAAE,CAACP,SAAS,CAAC,CAAC;EAEf,MAAM8B,aAAa,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACtC,MAAMC,UAAU,GAAGnC,WAAW,GAAGiC,KAAK;IACtC,MAAMG,SAAS,GAAGf,IAAI,CAACgB,GAAG,CAACpC,UAAU,EAAEgC,KAAK,CAAC;;IAE7C;IACA,MAAMK,YAAY,GAAGlC,cAAc,GAAG+B,UAAU,GAAG,CAAC;IACpD,MAAMI,WAAW,GAAGH,SAAS,GAAIhC,cAAc,IAAI,CAAC,GAAGgC,SAAS,CAAC,GAAG,GAAI;IAExE,OAAO;MACLI,SAAS,EAAE;AACjB,qBAAqBL,UAAU,GAAGG,YAAY;AAC9C,gBAAgBC,WAAW;AAC3B,qBAAqB,CAACN,KAAK,GAAG,EAAE;AAChC,OAAO;MACDQ,MAAM,EAAEP,KAAK,GAAGD,KAAK;MACrBS,OAAO,EAAEpC,SAAS,GAAG,CAAC,GAAI2B,KAAK,GAAG,GAAI,GAAG,GAAG;MAC5CU,UAAU,EAAErC,SAAS,GAAG,+CAA+C,GAAG;IAC5E,CAAC;EACH,CAAC;;EAED;EACA,MAAMsC,aAAa,GAAGtD,KAAK,CAACuD,QAAQ,CAACC,OAAO,CAACjD,QAAQ,CAAC;EAEtD,oBACEF,OAAA;IACEoD,GAAG,EAAEvC,YAAa;IAClBV,SAAS,EAAE,gBAAgBA,SAAS,EAAG;IACvCC,KAAK,EAAEA,KAAM;IAAAF,QAAA,eAEbF,OAAA;MAAKG,SAAS,EAAC,wBAAwB;MAAAD,QAAA,EACpC+C,aAAa,CAACI,GAAG,CAAC,CAACC,KAAK,EAAEhB,KAAK,kBAC9BtC,OAAA;QAEEG,SAAS,EAAC,mBAAmB;QAC7BC,KAAK,EAAEiC,aAAa,CAACC,KAAK,EAAEW,aAAa,CAACM,MAAM,CAAE;QAAArD,QAAA,EAEjDoD;MAAK,GAJDhB,KAAK;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKP,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnD,EAAA,CArFIP,WAAW;AAAA2D,EAAA,GAAX3D,WAAW;AAuFjB,eAAeA,WAAW;AAAC,IAAA2D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}