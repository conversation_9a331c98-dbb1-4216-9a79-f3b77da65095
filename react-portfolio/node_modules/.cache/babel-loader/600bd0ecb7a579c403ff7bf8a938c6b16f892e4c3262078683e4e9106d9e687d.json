{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Navigation.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport './Navigation.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Navigation = () => {\n  _s();\n  const [activeItem, setActiveItem] = useState('home');\n  const navItems = [{\n    id: 'home',\n    label: 'Home'\n  }, {\n    id: 'about',\n    label: 'About'\n  }, {\n    id: 'projects',\n    label: 'Projects'\n  }, {\n    id: 'contact',\n    label: 'Contact'\n  }, {\n    id: 'blog',\n    label: 'Blog'\n  }];\n  const handleItemClick = itemId => {\n    setActiveItem(itemId);\n\n    // Smooth scroll to section\n    const element = document.getElementById(itemId);\n    if (element) {\n      element.scrollIntoView({\n        behavior: 'smooth',\n        block: 'start'\n      });\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"navigation\",\n    children: /*#__PURE__*/_jsxDEV(\"ul\", {\n      className: \"nav-list\",\n      children: navItems.map(item => /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"nav-item\",\n        children: /*#__PURE__*/_jsxDEV(\"a\", {\n          href: `#${item.id}`,\n          className: `nav-link ${activeItem === item.id ? 'active' : ''}`,\n          onClick: e => {\n            e.preventDefault();\n            handleItemClick(item.id);\n          },\n          children: item.label\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 13\n        }, this)\n      }, item.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 5\n  }, this);\n};\n_s(Navigation, \"huPtbfXXFML2MV1ufOUtn6rcKTM=\");\n_c = Navigation;\nexport default Navigation;\nvar _c;\n$RefreshReg$(_c, \"Navigation\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "Navigation", "_s", "activeItem", "setActiveItem", "navItems", "id", "label", "handleItemClick", "itemId", "element", "document", "getElementById", "scrollIntoView", "behavior", "block", "className", "children", "map", "item", "href", "onClick", "e", "preventDefault", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Navigation.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport './Navigation.css';\n\nconst Navigation = () => {\n  const [activeItem, setActiveItem] = useState('home');\n\n  const navItems = [\n    { id: 'home', label: 'Home' },\n    { id: 'about', label: 'About' },\n    { id: 'projects', label: 'Projects' },\n    { id: 'contact', label: 'Contact' },\n    { id: 'blog', label: 'Blog' }\n  ];\n\n  const handleItemClick = (itemId) => {\n    setActiveItem(itemId);\n\n    // Smooth scroll to section\n    const element = document.getElementById(itemId);\n    if (element) {\n      element.scrollIntoView({\n        behavior: 'smooth',\n        block: 'start'\n      });\n    }\n  };\n\n  return (\n    <nav className=\"navigation\">\n      <ul className=\"nav-list\">\n        {navItems.map((item) => (\n          <li key={item.id} className=\"nav-item\">\n            <a\n              href={`#${item.id}`}\n              className={`nav-link ${activeItem === item.id ? 'active' : ''}`}\n              onClick={(e) => {\n                e.preventDefault();\n                handleItemClick(item.id);\n              }}\n            >\n              {item.label}\n            </a>\n          </li>\n        ))}\n      </ul>\n    </nav>\n  );\n};\n\nexport default Navigation;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGN,QAAQ,CAAC,MAAM,CAAC;EAEpD,MAAMO,QAAQ,GAAG,CACf;IAAEC,EAAE,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAO,CAAC,EAC7B;IAAED,EAAE,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAQ,CAAC,EAC/B;IAAED,EAAE,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAW,CAAC,EACrC;IAAED,EAAE,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAU,CAAC,EACnC;IAAED,EAAE,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAO,CAAC,CAC9B;EAED,MAAMC,eAAe,GAAIC,MAAM,IAAK;IAClCL,aAAa,CAACK,MAAM,CAAC;;IAErB;IACA,MAAMC,OAAO,GAAGC,QAAQ,CAACC,cAAc,CAACH,MAAM,CAAC;IAC/C,IAAIC,OAAO,EAAE;MACXA,OAAO,CAACG,cAAc,CAAC;QACrBC,QAAQ,EAAE,QAAQ;QAClBC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;EACF,CAAC;EAED,oBACEf,OAAA;IAAKgB,SAAS,EAAC,YAAY;IAAAC,QAAA,eACzBjB,OAAA;MAAIgB,SAAS,EAAC,UAAU;MAAAC,QAAA,EACrBZ,QAAQ,CAACa,GAAG,CAAEC,IAAI,iBACjBnB,OAAA;QAAkBgB,SAAS,EAAC,UAAU;QAAAC,QAAA,eACpCjB,OAAA;UACEoB,IAAI,EAAE,IAAID,IAAI,CAACb,EAAE,EAAG;UACpBU,SAAS,EAAE,YAAYb,UAAU,KAAKgB,IAAI,CAACb,EAAE,GAAG,QAAQ,GAAG,EAAE,EAAG;UAChEe,OAAO,EAAGC,CAAC,IAAK;YACdA,CAAC,CAACC,cAAc,CAAC,CAAC;YAClBf,eAAe,CAACW,IAAI,CAACb,EAAE,CAAC;UAC1B,CAAE;UAAAW,QAAA,EAEDE,IAAI,CAACZ;QAAK;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC,GAVGR,IAAI,CAACb,EAAE;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAWZ,CACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEV,CAAC;AAACzB,EAAA,CA5CID,UAAU;AAAA2B,EAAA,GAAV3B,UAAU;AA8ChB,eAAeA,UAAU;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}