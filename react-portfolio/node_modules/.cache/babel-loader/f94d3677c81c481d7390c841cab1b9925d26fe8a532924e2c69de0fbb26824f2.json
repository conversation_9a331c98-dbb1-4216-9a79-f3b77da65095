{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ScrollStack.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport './ScrollStack.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ScrollStack = ({\n  children,\n  className = \"\",\n  style = {},\n  stackOffset = 20,\n  stackScale = 0.95,\n  threshold = 0.1\n}) => {\n  _s();\n  const [scrollProgress, setScrollProgress] = useState(0);\n  const [isVisible, setIsVisible] = useState(false);\n  const containerRef = useRef(null);\n  useEffect(() => {\n    const handleScroll = () => {\n      if (!containerRef.current) return;\n      const rect = containerRef.current.getBoundingClientRect();\n      const windowHeight = window.innerHeight;\n      const elementTop = rect.top;\n      const elementHeight = rect.height;\n\n      // Calculate visibility\n      setIsVisible(elementTop < windowHeight && elementTop + elementHeight > 0);\n\n      // Calculate scroll progress (0 to 1)\n      // When element enters viewport (bottom) = 0\n      // When element exits viewport (top) = 1\n      const enterPoint = windowHeight;\n      const exitPoint = -elementHeight;\n      if (elementTop <= enterPoint && elementTop >= exitPoint) {\n        const progress = (enterPoint - elementTop) / (enterPoint - exitPoint);\n        setScrollProgress(Math.max(0, Math.min(1, progress)));\n      } else if (elementTop > enterPoint) {\n        setScrollProgress(0);\n      } else {\n        setScrollProgress(1);\n      }\n    };\n    window.addEventListener('scroll', handleScroll, {\n      passive: true\n    });\n    handleScroll(); // Initial call\n\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, [threshold]);\n  const getStackStyle = (index, total) => {\n    const baseOffset = stackOffset * index;\n    const baseScale = Math.pow(stackScale, index);\n\n    // Apply scroll-based transformations\n    const scrollOffset = scrollProgress * baseOffset * 3;\n    const scrollScale = baseScale + scrollProgress * (1 - baseScale);\n    const scrollRotation = scrollProgress * index * 2;\n    return {\n      transform: `\n        translateY(${baseOffset - scrollOffset}px)\n        scale(${scrollScale})\n        rotateX(${scrollRotation}deg)\n        translateZ(${-index * 20}px)\n      `,\n      zIndex: total - index,\n      opacity: isVisible ? Math.max(0.3, 1 - index * 0.15) : 0.1,\n      transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',\n      transformOrigin: 'center center'\n    };\n  };\n\n  // Convert children to array if it's not already\n  const childrenArray = React.Children.toArray(children);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: containerRef,\n    className: `scroll-stack ${className}`,\n    style: style,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"scroll-stack-container\",\n      children: childrenArray.map((child, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"scroll-stack-item\",\n        style: getStackStyle(index, childrenArray.length),\n        children: child\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 77,\n    columnNumber: 5\n  }, this);\n};\n_s(ScrollStack, \"ZJ9NqbTuOD8Nvn1O9a2CJWWXESw=\");\n_c = ScrollStack;\nexport default ScrollStack;\nvar _c;\n$RefreshReg$(_c, \"ScrollStack\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "ScrollStack", "children", "className", "style", "stackOffset", "stackScale", "threshold", "_s", "scrollProgress", "setScrollProgress", "isVisible", "setIsVisible", "containerRef", "handleScroll", "current", "rect", "getBoundingClientRect", "windowHeight", "window", "innerHeight", "elementTop", "top", "elementHeight", "height", "enterPoint", "exitPoint", "progress", "Math", "max", "min", "addEventListener", "passive", "removeEventListener", "getStackStyle", "index", "total", "baseOffset", "baseScale", "pow", "scrollOffset", "scrollScale", "scrollRotation", "transform", "zIndex", "opacity", "transition", "transform<PERSON><PERSON>in", "childrenA<PERSON>y", "Children", "toArray", "ref", "map", "child", "length", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ScrollStack.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport './ScrollStack.css';\n\nconst ScrollStack = ({\n  children,\n  className = \"\",\n  style = {},\n  stackOffset = 20,\n  stackScale = 0.95,\n  threshold = 0.1\n}) => {\n  const [scrollProgress, setScrollProgress] = useState(0);\n  const [isVisible, setIsVisible] = useState(false);\n  const containerRef = useRef(null);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      if (!containerRef.current) return;\n\n      const rect = containerRef.current.getBoundingClientRect();\n      const windowHeight = window.innerHeight;\n      const elementTop = rect.top;\n      const elementHeight = rect.height;\n\n      // Calculate visibility\n      setIsVisible(elementTop < windowHeight && elementTop + elementHeight > 0);\n\n      // Calculate scroll progress (0 to 1)\n      // When element enters viewport (bottom) = 0\n      // When element exits viewport (top) = 1\n      const enterPoint = windowHeight;\n      const exitPoint = -elementHeight;\n\n      if (elementTop <= enterPoint && elementTop >= exitPoint) {\n        const progress = (enterPoint - elementTop) / (enterPoint - exitPoint);\n        setScrollProgress(Math.max(0, Math.min(1, progress)));\n      } else if (elementTop > enterPoint) {\n        setScrollProgress(0);\n      } else {\n        setScrollProgress(1);\n      }\n    };\n\n    window.addEventListener('scroll', handleScroll, { passive: true });\n    handleScroll(); // Initial call\n\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, [threshold]);\n\n  const getStackStyle = (index, total) => {\n    const baseOffset = stackOffset * index;\n    const baseScale = Math.pow(stackScale, index);\n\n    // Apply scroll-based transformations\n    const scrollOffset = scrollProgress * baseOffset * 3;\n    const scrollScale = baseScale + (scrollProgress * (1 - baseScale));\n    const scrollRotation = scrollProgress * index * 2;\n\n    return {\n      transform: `\n        translateY(${baseOffset - scrollOffset}px)\n        scale(${scrollScale})\n        rotateX(${scrollRotation}deg)\n        translateZ(${-index * 20}px)\n      `,\n      zIndex: total - index,\n      opacity: isVisible ? Math.max(0.3, 1 - (index * 0.15)) : 0.1,\n      transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',\n      transformOrigin: 'center center'\n    };\n  };\n\n  // Convert children to array if it's not already\n  const childrenArray = React.Children.toArray(children);\n\n  return (\n    <div\n      ref={containerRef}\n      className={`scroll-stack ${className}`}\n      style={style}\n    >\n      <div className=\"scroll-stack-container\">\n        {childrenArray.map((child, index) => (\n          <div\n            key={index}\n            className=\"scroll-stack-item\"\n            style={getStackStyle(index, childrenArray.length)}\n          >\n            {child}\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n};\n\nexport default ScrollStack;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,WAAW,GAAGA,CAAC;EACnBC,QAAQ;EACRC,SAAS,GAAG,EAAE;EACdC,KAAK,GAAG,CAAC,CAAC;EACVC,WAAW,GAAG,EAAE;EAChBC,UAAU,GAAG,IAAI;EACjBC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGd,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAMiB,YAAY,GAAGf,MAAM,CAAC,IAAI,CAAC;EAEjCD,SAAS,CAAC,MAAM;IACd,MAAMiB,YAAY,GAAGA,CAAA,KAAM;MACzB,IAAI,CAACD,YAAY,CAACE,OAAO,EAAE;MAE3B,MAAMC,IAAI,GAAGH,YAAY,CAACE,OAAO,CAACE,qBAAqB,CAAC,CAAC;MACzD,MAAMC,YAAY,GAAGC,MAAM,CAACC,WAAW;MACvC,MAAMC,UAAU,GAAGL,IAAI,CAACM,GAAG;MAC3B,MAAMC,aAAa,GAAGP,IAAI,CAACQ,MAAM;;MAEjC;MACAZ,YAAY,CAACS,UAAU,GAAGH,YAAY,IAAIG,UAAU,GAAGE,aAAa,GAAG,CAAC,CAAC;;MAEzE;MACA;MACA;MACA,MAAME,UAAU,GAAGP,YAAY;MAC/B,MAAMQ,SAAS,GAAG,CAACH,aAAa;MAEhC,IAAIF,UAAU,IAAII,UAAU,IAAIJ,UAAU,IAAIK,SAAS,EAAE;QACvD,MAAMC,QAAQ,GAAG,CAACF,UAAU,GAAGJ,UAAU,KAAKI,UAAU,GAAGC,SAAS,CAAC;QACrEhB,iBAAiB,CAACkB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEH,QAAQ,CAAC,CAAC,CAAC;MACvD,CAAC,MAAM,IAAIN,UAAU,GAAGI,UAAU,EAAE;QAClCf,iBAAiB,CAAC,CAAC,CAAC;MACtB,CAAC,MAAM;QACLA,iBAAiB,CAAC,CAAC,CAAC;MACtB;IACF,CAAC;IAEDS,MAAM,CAACY,gBAAgB,CAAC,QAAQ,EAAEjB,YAAY,EAAE;MAAEkB,OAAO,EAAE;IAAK,CAAC,CAAC;IAClElB,YAAY,CAAC,CAAC,CAAC,CAAC;;IAEhB,OAAO,MAAMK,MAAM,CAACc,mBAAmB,CAAC,QAAQ,EAAEnB,YAAY,CAAC;EACjE,CAAC,EAAE,CAACP,SAAS,CAAC,CAAC;EAEf,MAAM2B,aAAa,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACtC,MAAMC,UAAU,GAAGhC,WAAW,GAAG8B,KAAK;IACtC,MAAMG,SAAS,GAAGV,IAAI,CAACW,GAAG,CAACjC,UAAU,EAAE6B,KAAK,CAAC;;IAE7C;IACA,MAAMK,YAAY,GAAG/B,cAAc,GAAG4B,UAAU,GAAG,CAAC;IACpD,MAAMI,WAAW,GAAGH,SAAS,GAAI7B,cAAc,IAAI,CAAC,GAAG6B,SAAS,CAAE;IAClE,MAAMI,cAAc,GAAGjC,cAAc,GAAG0B,KAAK,GAAG,CAAC;IAEjD,OAAO;MACLQ,SAAS,EAAE;AACjB,qBAAqBN,UAAU,GAAGG,YAAY;AAC9C,gBAAgBC,WAAW;AAC3B,kBAAkBC,cAAc;AAChC,qBAAqB,CAACP,KAAK,GAAG,EAAE;AAChC,OAAO;MACDS,MAAM,EAAER,KAAK,GAAGD,KAAK;MACrBU,OAAO,EAAElC,SAAS,GAAGiB,IAAI,CAACC,GAAG,CAAC,GAAG,EAAE,CAAC,GAAIM,KAAK,GAAG,IAAK,CAAC,GAAG,GAAG;MAC5DW,UAAU,EAAE,+CAA+C;MAC3DC,eAAe,EAAE;IACnB,CAAC;EACH,CAAC;;EAED;EACA,MAAMC,aAAa,GAAGrD,KAAK,CAACsD,QAAQ,CAACC,OAAO,CAAChD,QAAQ,CAAC;EAEtD,oBACEF,OAAA;IACEmD,GAAG,EAAEtC,YAAa;IAClBV,SAAS,EAAE,gBAAgBA,SAAS,EAAG;IACvCC,KAAK,EAAEA,KAAM;IAAAF,QAAA,eAEbF,OAAA;MAAKG,SAAS,EAAC,wBAAwB;MAAAD,QAAA,EACpC8C,aAAa,CAACI,GAAG,CAAC,CAACC,KAAK,EAAElB,KAAK,kBAC9BnC,OAAA;QAEEG,SAAS,EAAC,mBAAmB;QAC7BC,KAAK,EAAE8B,aAAa,CAACC,KAAK,EAAEa,aAAa,CAACM,MAAM,CAAE;QAAApD,QAAA,EAEjDmD;MAAK,GAJDlB,KAAK;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKP,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClD,EAAA,CA3FIP,WAAW;AAAA0D,EAAA,GAAX1D,WAAW;AA6FjB,eAAeA,WAAW;AAAC,IAAA0D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}