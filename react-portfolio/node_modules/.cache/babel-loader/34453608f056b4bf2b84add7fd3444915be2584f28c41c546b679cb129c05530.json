{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Ribbons.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport './Ribbons.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Ribbons = ({\n  children,\n  className = \"\",\n  style = {},\n  ribbonColor = \"rgba(78, 205, 196, 0.2)\",\n  ribbonWidth = 2,\n  ribbonSpeed = 2000\n}) => {\n  _s();\n  const [mousePosition, setMousePosition] = useState({\n    x: 0,\n    y: 0\n  });\n  const [isHovering, setIsHovering] = useState(false);\n  const containerRef = useRef(null);\n  useEffect(() => {\n    const handleMouseMove = e => {\n      if (containerRef.current && isHovering) {\n        const rect = containerRef.current.getBoundingClientRect();\n        const x = e.clientX - rect.left;\n        const y = e.clientY - rect.top;\n        setMousePosition({\n          x,\n          y\n        });\n      }\n    };\n    document.addEventListener('mousemove', handleMouseMove);\n    return () => document.removeEventListener('mousemove', handleMouseMove);\n  }, [isHovering]);\n  const handleMouseEnter = () => {\n    setIsHovering(true);\n  };\n  const handleMouseLeave = () => {\n    setIsHovering(false);\n    setMousePosition({\n      x: 0,\n      y: 0\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: containerRef,\n    className: `ribbons-container ${className}`,\n    style: style,\n    onMouseEnter: handleMouseEnter,\n    onMouseLeave: handleMouseLeave,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ribbons-wrapper\",\n      children: [...Array(5)].map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"ribbon\",\n        style: {\n          '--ribbon-delay': `${index * 0.5}s`,\n          '--ribbon-color': ribbonColor,\n          '--ribbon-width': `${ribbonWidth}px`,\n          '--ribbon-speed': `${ribbonSpeed}ms`,\n          opacity: isHovering ? 0.8 : 0.3,\n          transform: isHovering ? `translate(${mousePosition.x * 0.1}px, ${mousePosition.y * 0.1}px) rotate(${index * 15}deg)` : `rotate(${index * 15}deg)`\n        }\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ribbons-content\",\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 5\n  }, this);\n};\n_s(Ribbons, \"4l6ztoc+CnxzOJRez4d+AEJzzLU=\");\n_c = Ribbons;\nexport default Ribbons;\nvar _c;\n$RefreshReg$(_c, \"Ribbons\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "jsxDEV", "_jsxDEV", "Ribbons", "children", "className", "style", "ribbonColor", "ribbonWidth", "ribbonSpeed", "_s", "mousePosition", "setMousePosition", "x", "y", "isHovering", "setIsHovering", "containerRef", "handleMouseMove", "e", "current", "rect", "getBoundingClientRect", "clientX", "left", "clientY", "top", "document", "addEventListener", "removeEventListener", "handleMouseEnter", "handleMouseLeave", "ref", "onMouseEnter", "onMouseLeave", "Array", "map", "_", "index", "opacity", "transform", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Ribbons.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport './Ribbons.css';\n\nconst Ribbons = ({\n  children,\n  className = \"\",\n  style = {},\n  ribbonColor = \"rgba(78, 205, 196, 0.2)\",\n  ribbonWidth = 2,\n  ribbonSpeed = 2000\n}) => {\n  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });\n  const [isHovering, setIsHovering] = useState(false);\n  const containerRef = useRef(null);\n\n  useEffect(() => {\n    const handleMouseMove = (e) => {\n      if (containerRef.current && isHovering) {\n        const rect = containerRef.current.getBoundingClientRect();\n        const x = e.clientX - rect.left;\n        const y = e.clientY - rect.top;\n\n        setMousePosition({ x, y });\n      }\n    };\n\n    document.addEventListener('mousemove', handleMouseMove);\n    return () => document.removeEventListener('mousemove', handleMouseMove);\n  }, [isHovering]);\n\n  const handleMouseEnter = () => {\n    setIsHovering(true);\n  };\n\n  const handleMouseLeave = () => {\n    setIsHovering(false);\n    setMousePosition({ x: 0, y: 0 });\n  };\n\n  return (\n    <div\n      ref={containerRef}\n      className={`ribbons-container ${className}`}\n      style={style}\n      onMouseEnter={handleMouseEnter}\n      onMouseLeave={handleMouseLeave}\n    >\n      {/* Animated ribbons */}\n      <div className=\"ribbons-wrapper\">\n        {[...Array(5)].map((_, index) => (\n          <div\n            key={index}\n            className=\"ribbon\"\n            style={{\n              '--ribbon-delay': `${index * 0.5}s`,\n              '--ribbon-color': ribbonColor,\n              '--ribbon-width': `${ribbonWidth}px`,\n              '--ribbon-speed': `${ribbonSpeed}ms`,\n              opacity: isHovering ? 0.8 : 0.3,\n              transform: isHovering\n                ? `translate(${mousePosition.x * 0.1}px, ${mousePosition.y * 0.1}px) rotate(${index * 15}deg)`\n                : `rotate(${index * 15}deg)`\n            }}\n          />\n        ))}\n      </div>\n\n      {/* Content */}\n      <div className=\"ribbons-content\">\n        {children}\n      </div>\n    </div>\n  );\n};\n\nexport default Ribbons;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,OAAO,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvB,MAAMC,OAAO,GAAGA,CAAC;EACfC,QAAQ;EACRC,SAAS,GAAG,EAAE;EACdC,KAAK,GAAG,CAAC,CAAC;EACVC,WAAW,GAAG,yBAAyB;EACvCC,WAAW,GAAG,CAAC;EACfC,WAAW,GAAG;AAChB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGd,QAAQ,CAAC;IAAEe,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAClE,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMmB,YAAY,GAAGlB,MAAM,CAAC,IAAI,CAAC;EAEjCC,SAAS,CAAC,MAAM;IACd,MAAMkB,eAAe,GAAIC,CAAC,IAAK;MAC7B,IAAIF,YAAY,CAACG,OAAO,IAAIL,UAAU,EAAE;QACtC,MAAMM,IAAI,GAAGJ,YAAY,CAACG,OAAO,CAACE,qBAAqB,CAAC,CAAC;QACzD,MAAMT,CAAC,GAAGM,CAAC,CAACI,OAAO,GAAGF,IAAI,CAACG,IAAI;QAC/B,MAAMV,CAAC,GAAGK,CAAC,CAACM,OAAO,GAAGJ,IAAI,CAACK,GAAG;QAE9Bd,gBAAgB,CAAC;UAAEC,CAAC;UAAEC;QAAE,CAAC,CAAC;MAC5B;IACF,CAAC;IAEDa,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEV,eAAe,CAAC;IACvD,OAAO,MAAMS,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEX,eAAe,CAAC;EACzE,CAAC,EAAE,CAACH,UAAU,CAAC,CAAC;EAEhB,MAAMe,gBAAgB,GAAGA,CAAA,KAAM;IAC7Bd,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMe,gBAAgB,GAAGA,CAAA,KAAM;IAC7Bf,aAAa,CAAC,KAAK,CAAC;IACpBJ,gBAAgB,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC,CAAC;EAClC,CAAC;EAED,oBACEZ,OAAA;IACE8B,GAAG,EAAEf,YAAa;IAClBZ,SAAS,EAAE,qBAAqBA,SAAS,EAAG;IAC5CC,KAAK,EAAEA,KAAM;IACb2B,YAAY,EAAEH,gBAAiB;IAC/BI,YAAY,EAAEH,gBAAiB;IAAA3B,QAAA,gBAG/BF,OAAA;MAAKG,SAAS,EAAC,iBAAiB;MAAAD,QAAA,EAC7B,CAAC,GAAG+B,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,kBAC1BpC,OAAA;QAEEG,SAAS,EAAC,QAAQ;QAClBC,KAAK,EAAE;UACL,gBAAgB,EAAE,GAAGgC,KAAK,GAAG,GAAG,GAAG;UACnC,gBAAgB,EAAE/B,WAAW;UAC7B,gBAAgB,EAAE,GAAGC,WAAW,IAAI;UACpC,gBAAgB,EAAE,GAAGC,WAAW,IAAI;UACpC8B,OAAO,EAAExB,UAAU,GAAG,GAAG,GAAG,GAAG;UAC/ByB,SAAS,EAAEzB,UAAU,GACjB,aAAaJ,aAAa,CAACE,CAAC,GAAG,GAAG,OAAOF,aAAa,CAACG,CAAC,GAAG,GAAG,cAAcwB,KAAK,GAAG,EAAE,MAAM,GAC5F,UAAUA,KAAK,GAAG,EAAE;QAC1B;MAAE,GAXGA,KAAK;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAYX,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN1C,OAAA;MAAKG,SAAS,EAAC,iBAAiB;MAAAD,QAAA,EAC7BA;IAAQ;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClC,EAAA,CAtEIP,OAAO;AAAA0C,EAAA,GAAP1C,OAAO;AAwEb,eAAeA,OAAO;AAAC,IAAA0C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}