{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ScrollStack.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport \"./ScrollStack.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const ScrollStackItem = ({\n  children,\n  itemClassName = \"\"\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: `scroll-stack-card ${itemClassName}`.trim(),\n  children: children\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 5,\n  columnNumber: 3\n}, this);\n_c = ScrollStackItem;\nconst ScrollStack = ({\n  children,\n  className = \"\"\n}) => {\n  _s();\n  const [scrollProgress, setScrollProgress] = useState(0);\n  const containerRef = useRef(null);\n  useEffect(() => {\n    const handleScroll = () => {\n      if (!containerRef.current) return;\n      const rect = containerRef.current.getBoundingClientRect();\n      const windowHeight = window.innerHeight;\n\n      // Calculate progress based on container position\n      let progress = 0;\n      if (rect.top <= windowHeight && rect.bottom >= 0) {\n        const visibleHeight = Math.min(rect.bottom, windowHeight) - Math.max(rect.top, 0);\n        const totalHeight = rect.height;\n        progress = Math.max(0, Math.min(1, (windowHeight - rect.top) / (windowHeight + totalHeight)));\n      }\n      setScrollProgress(progress);\n\n      // Apply transforms to cards\n      const cards = containerRef.current.querySelectorAll('.scroll-stack-card');\n      cards.forEach((card, index) => {\n        const stackOffset = index * 20;\n        const spreadDistance = index * 150;\n        const currentY = stackOffset + progress * (spreadDistance - stackOffset);\n        const stackScale = Math.pow(0.95, index);\n        const currentScale = stackScale + progress * (1 - stackScale);\n        const rotation = (1 - progress) * index * 5;\n        const opacity = 0.7 + progress * 0.3;\n        card.style.transform = `translate(-50%, -50%) translateY(${currentY}px) scale(${currentScale}) rotateX(${rotation}deg)`;\n        card.style.opacity = Math.min(opacity, 1);\n        card.style.zIndex = cards.length - index;\n      });\n    };\n    window.addEventListener('scroll', handleScroll, {\n      passive: true\n    });\n    handleScroll();\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: containerRef,\n    className: `scroll-stack ${className}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"scroll-stack-container\",\n      children: React.Children.map(children, (child, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"scroll-stack-item\",\n        children: child\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 55,\n    columnNumber: 5\n  }, this);\n};\n_s(ScrollStack, \"s1hDQaLO+Y+X3sQDuGrxH3NJatY=\");\n_c2 = ScrollStack;\nexport default ScrollStack;\nvar _c, _c2;\n$RefreshReg$(_c, \"ScrollStackItem\");\n$RefreshReg$(_c2, \"ScrollStack\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "ScrollStackItem", "children", "itemClassName", "className", "trim", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "ScrollStack", "_s", "scrollProgress", "setScrollProgress", "containerRef", "handleScroll", "current", "rect", "getBoundingClientRect", "windowHeight", "window", "innerHeight", "progress", "top", "bottom", "visibleHeight", "Math", "min", "max", "totalHeight", "height", "cards", "querySelectorAll", "for<PERSON>ach", "card", "index", "stackOffset", "spreadDistance", "currentY", "stackScale", "pow", "currentScale", "rotation", "opacity", "style", "transform", "zIndex", "length", "addEventListener", "passive", "removeEventListener", "ref", "Children", "map", "child", "_c2", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ScrollStack.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport \"./ScrollStack.css\";\n\nexport const ScrollStackItem = ({ children, itemClassName = \"\" }) => (\n  <div className={`scroll-stack-card ${itemClassName}`.trim()}>{children}</div>\n);\n\nconst ScrollStack = ({ children, className = \"\" }) => {\n  const [scrollProgress, setScrollProgress] = useState(0);\n  const containerRef = useRef(null);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      if (!containerRef.current) return;\n\n      const rect = containerRef.current.getBoundingClientRect();\n      const windowHeight = window.innerHeight;\n\n      // Calculate progress based on container position\n      let progress = 0;\n      if (rect.top <= windowHeight && rect.bottom >= 0) {\n        const visibleHeight = Math.min(rect.bottom, windowHeight) - Math.max(rect.top, 0);\n        const totalHeight = rect.height;\n        progress = Math.max(0, Math.min(1, (windowHeight - rect.top) / (windowHeight + totalHeight)));\n      }\n\n      setScrollProgress(progress);\n\n      // Apply transforms to cards\n      const cards = containerRef.current.querySelectorAll('.scroll-stack-card');\n      cards.forEach((card, index) => {\n        const stackOffset = index * 20;\n        const spreadDistance = index * 150;\n        const currentY = stackOffset + (progress * (spreadDistance - stackOffset));\n\n        const stackScale = Math.pow(0.95, index);\n        const currentScale = stackScale + (progress * (1 - stackScale));\n\n        const rotation = (1 - progress) * index * 5;\n        const opacity = 0.7 + (progress * 0.3);\n\n        card.style.transform = `translate(-50%, -50%) translateY(${currentY}px) scale(${currentScale}) rotateX(${rotation}deg)`;\n        card.style.opacity = Math.min(opacity, 1);\n        card.style.zIndex = cards.length - index;\n      });\n    };\n\n    window.addEventListener('scroll', handleScroll, { passive: true });\n    handleScroll();\n\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  return (\n    <div\n      ref={containerRef}\n      className={`scroll-stack ${className}`}\n    >\n      <div className=\"scroll-stack-container\">\n        {React.Children.map(children, (child, index) => (\n          <div key={index} className=\"scroll-stack-item\">\n            {child}\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n};\n\nexport default ScrollStack;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,OAAO,MAAMC,eAAe,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,aAAa,GAAG;AAAG,CAAC,kBAC9DH,OAAA;EAAKI,SAAS,EAAE,qBAAqBD,aAAa,EAAE,CAACE,IAAI,CAAC,CAAE;EAAAH,QAAA,EAAEA;AAAQ;EAAAI,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAAM,CAC7E;AAACC,EAAA,GAFWT,eAAe;AAI5B,MAAMU,WAAW,GAAGA,CAAC;EAAET,QAAQ;EAAEE,SAAS,GAAG;AAAG,CAAC,KAAK;EAAAQ,EAAA;EACpD,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGlB,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAMmB,YAAY,GAAGjB,MAAM,CAAC,IAAI,CAAC;EAEjCD,SAAS,CAAC,MAAM;IACd,MAAMmB,YAAY,GAAGA,CAAA,KAAM;MACzB,IAAI,CAACD,YAAY,CAACE,OAAO,EAAE;MAE3B,MAAMC,IAAI,GAAGH,YAAY,CAACE,OAAO,CAACE,qBAAqB,CAAC,CAAC;MACzD,MAAMC,YAAY,GAAGC,MAAM,CAACC,WAAW;;MAEvC;MACA,IAAIC,QAAQ,GAAG,CAAC;MAChB,IAAIL,IAAI,CAACM,GAAG,IAAIJ,YAAY,IAAIF,IAAI,CAACO,MAAM,IAAI,CAAC,EAAE;QAChD,MAAMC,aAAa,GAAGC,IAAI,CAACC,GAAG,CAACV,IAAI,CAACO,MAAM,EAAEL,YAAY,CAAC,GAAGO,IAAI,CAACE,GAAG,CAACX,IAAI,CAACM,GAAG,EAAE,CAAC,CAAC;QACjF,MAAMM,WAAW,GAAGZ,IAAI,CAACa,MAAM;QAC/BR,QAAQ,GAAGI,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEF,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAACR,YAAY,GAAGF,IAAI,CAACM,GAAG,KAAKJ,YAAY,GAAGU,WAAW,CAAC,CAAC,CAAC;MAC/F;MAEAhB,iBAAiB,CAACS,QAAQ,CAAC;;MAE3B;MACA,MAAMS,KAAK,GAAGjB,YAAY,CAACE,OAAO,CAACgB,gBAAgB,CAAC,oBAAoB,CAAC;MACzED,KAAK,CAACE,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;QAC7B,MAAMC,WAAW,GAAGD,KAAK,GAAG,EAAE;QAC9B,MAAME,cAAc,GAAGF,KAAK,GAAG,GAAG;QAClC,MAAMG,QAAQ,GAAGF,WAAW,GAAId,QAAQ,IAAIe,cAAc,GAAGD,WAAW,CAAE;QAE1E,MAAMG,UAAU,GAAGb,IAAI,CAACc,GAAG,CAAC,IAAI,EAAEL,KAAK,CAAC;QACxC,MAAMM,YAAY,GAAGF,UAAU,GAAIjB,QAAQ,IAAI,CAAC,GAAGiB,UAAU,CAAE;QAE/D,MAAMG,QAAQ,GAAG,CAAC,CAAC,GAAGpB,QAAQ,IAAIa,KAAK,GAAG,CAAC;QAC3C,MAAMQ,OAAO,GAAG,GAAG,GAAIrB,QAAQ,GAAG,GAAI;QAEtCY,IAAI,CAACU,KAAK,CAACC,SAAS,GAAG,oCAAoCP,QAAQ,aAAaG,YAAY,aAAaC,QAAQ,MAAM;QACvHR,IAAI,CAACU,KAAK,CAACD,OAAO,GAAGjB,IAAI,CAACC,GAAG,CAACgB,OAAO,EAAE,CAAC,CAAC;QACzCT,IAAI,CAACU,KAAK,CAACE,MAAM,GAAGf,KAAK,CAACgB,MAAM,GAAGZ,KAAK;MAC1C,CAAC,CAAC;IACJ,CAAC;IAEDf,MAAM,CAAC4B,gBAAgB,CAAC,QAAQ,EAAEjC,YAAY,EAAE;MAAEkC,OAAO,EAAE;IAAK,CAAC,CAAC;IAClElC,YAAY,CAAC,CAAC;IAEd,OAAO,MAAMK,MAAM,CAAC8B,mBAAmB,CAAC,QAAQ,EAAEnC,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEhB,OAAA;IACEoD,GAAG,EAAErC,YAAa;IAClBX,SAAS,EAAE,gBAAgBA,SAAS,EAAG;IAAAF,QAAA,eAEvCF,OAAA;MAAKI,SAAS,EAAC,wBAAwB;MAAAF,QAAA,EACpCP,KAAK,CAAC0D,QAAQ,CAACC,GAAG,CAACpD,QAAQ,EAAE,CAACqD,KAAK,EAAEnB,KAAK,kBACzCpC,OAAA;QAAiBI,SAAS,EAAC,mBAAmB;QAAAF,QAAA,EAC3CqD;MAAK,GADEnB,KAAK;QAAA9B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEV,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACG,EAAA,CA5DID,WAAW;AAAA6C,GAAA,GAAX7C,WAAW;AA8DjB,eAAeA,WAAW;AAAC,IAAAD,EAAA,EAAA8C,GAAA;AAAAC,YAAA,CAAA/C,EAAA;AAAA+C,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}