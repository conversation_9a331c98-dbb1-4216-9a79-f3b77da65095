{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ScrollStack.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport './ScrollStack.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ScrollStack = ({\n  children,\n  className = \"\",\n  style = {}\n}) => {\n  _s();\n  const [scrollProgress, setScrollProgress] = useState(0);\n  const containerRef = useRef(null);\n  useEffect(() => {\n    const handleScroll = () => {\n      if (!containerRef.current) return;\n      const rect = containerRef.current.getBoundingClientRect();\n      const windowHeight = window.innerHeight;\n\n      // Simple progress calculation\n      // Start when container enters viewport, end when it exits\n      const progress = Math.max(0, Math.min(1, (windowHeight - rect.top) / (windowHeight + rect.height)));\n      setScrollProgress(progress);\n\n      // Debug\n      console.log(`ScrollStack: top=${rect.top.toFixed(0)}, progress=${progress.toFixed(2)}`);\n    };\n    window.addEventListener('scroll', handleScroll, {\n      passive: true\n    });\n    handleScroll();\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n  const getStackStyle = (index, total) => {\n    // Debug log for first card\n    if (index === 0) {\n      console.log(`ScrollStack Transform Debug: progress=${scrollProgress.toFixed(2)}, stackOffset=${stackOffset}`);\n    }\n\n    // Calculate positions\n    const stackedY = index * stackOffset; // Initial: 0, 20, 40, 60, 80\n    const spreadY = index * 200; // Final: 0, 200, 400, 600, 800\n    const currentY = stackedY + scrollProgress * (spreadY - stackedY);\n\n    // Calculate scales\n    const stackedScale = Math.pow(stackScale, index); // Initial: 1, 0.92, 0.85, 0.78, 0.72\n    const spreadScale = 1; // Final: all 1\n    const currentScale = stackedScale + scrollProgress * (spreadScale - stackedScale);\n\n    // Simple opacity\n    const opacity = 0.8 + scrollProgress * 0.2;\n\n    // Rotation\n    const rotation = (1 - scrollProgress) * index * 3;\n\n    // Debug log for transform values\n    if (index === 0) {\n      console.log(`Card ${index}: Y=${currentY.toFixed(1)}, Scale=${currentScale.toFixed(2)}`);\n    }\n    return {\n      transform: `translate(-50%, -50%) translateY(${currentY}px) scale(${currentScale}) rotateX(${rotation}deg)`,\n      zIndex: total - index,\n      opacity: opacity,\n      transformOrigin: 'center center'\n    };\n  };\n\n  // Convert children to array if it's not already\n  const childrenArray = React.Children.toArray(children);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: containerRef,\n    className: `scroll-stack ${className}`,\n    style: style,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: '20px',\n        right: '20px',\n        background: 'rgba(0,0,0,0.8)',\n        color: 'white',\n        padding: '10px',\n        borderRadius: '5px',\n        fontSize: '14px',\n        zIndex: 10000,\n        fontFamily: 'monospace'\n      },\n      children: [\"ScrollStack Debug:\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 27\n      }, this), \"Progress: \", (scrollProgress * 100).toFixed(0), \"%\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 55\n      }, this), \"Cards: \", childrenArray.length]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"scroll-stack-container\",\n      children: childrenArray.map((child, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"scroll-stack-item\",\n        style: getStackStyle(index, childrenArray.length),\n        children: child\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 76,\n    columnNumber: 5\n  }, this);\n};\n_s(ScrollStack, \"s1hDQaLO+Y+X3sQDuGrxH3NJatY=\");\n_c = ScrollStack;\nexport default ScrollStack;\nvar _c;\n$RefreshReg$(_c, \"ScrollStack\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "ScrollStack", "children", "className", "style", "_s", "scrollProgress", "setScrollProgress", "containerRef", "handleScroll", "current", "rect", "getBoundingClientRect", "windowHeight", "window", "innerHeight", "progress", "Math", "max", "min", "top", "height", "console", "log", "toFixed", "addEventListener", "passive", "removeEventListener", "getStackStyle", "index", "total", "stackOffset", "stackedY", "spreadY", "currentY", "stackedScale", "pow", "stackScale", "spreadScale", "currentScale", "opacity", "rotation", "transform", "zIndex", "transform<PERSON><PERSON>in", "childrenA<PERSON>y", "Children", "toArray", "ref", "position", "right", "background", "color", "padding", "borderRadius", "fontSize", "fontFamily", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "map", "child", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ScrollStack.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport './ScrollStack.css';\n\nconst ScrollStack = ({\n  children,\n  className = \"\",\n  style = {}\n}) => {\n  const [scrollProgress, setScrollProgress] = useState(0);\n  const containerRef = useRef(null);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      if (!containerRef.current) return;\n\n      const rect = containerRef.current.getBoundingClientRect();\n      const windowHeight = window.innerHeight;\n\n      // Simple progress calculation\n      // Start when container enters viewport, end when it exits\n      const progress = Math.max(0, Math.min(1,\n        (windowHeight - rect.top) / (windowHeight + rect.height)\n      ));\n\n      setScrollProgress(progress);\n\n      // Debug\n      console.log(`ScrollStack: top=${rect.top.toFixed(0)}, progress=${progress.toFixed(2)}`);\n    };\n\n    window.addEventListener('scroll', handleScroll, { passive: true });\n    handleScroll();\n\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const getStackStyle = (index, total) => {\n    // Debug log for first card\n    if (index === 0) {\n      console.log(`ScrollStack Transform Debug: progress=${scrollProgress.toFixed(2)}, stackOffset=${stackOffset}`);\n    }\n\n    // Calculate positions\n    const stackedY = index * stackOffset; // Initial: 0, 20, 40, 60, 80\n    const spreadY = index * 200; // Final: 0, 200, 400, 600, 800\n    const currentY = stackedY + (scrollProgress * (spreadY - stackedY));\n\n    // Calculate scales\n    const stackedScale = Math.pow(stackScale, index); // Initial: 1, 0.92, 0.85, 0.78, 0.72\n    const spreadScale = 1; // Final: all 1\n    const currentScale = stackedScale + (scrollProgress * (spreadScale - stackedScale));\n\n    // Simple opacity\n    const opacity = 0.8 + (scrollProgress * 0.2);\n\n    // Rotation\n    const rotation = (1 - scrollProgress) * index * 3;\n\n    // Debug log for transform values\n    if (index === 0) {\n      console.log(`Card ${index}: Y=${currentY.toFixed(1)}, Scale=${currentScale.toFixed(2)}`);\n    }\n\n    return {\n      transform: `translate(-50%, -50%) translateY(${currentY}px) scale(${currentScale}) rotateX(${rotation}deg)`,\n      zIndex: total - index,\n      opacity: opacity,\n      transformOrigin: 'center center'\n    };\n  };\n\n  // Convert children to array if it's not already\n  const childrenArray = React.Children.toArray(children);\n\n  return (\n    <div\n      ref={containerRef}\n      className={`scroll-stack ${className}`}\n      style={style}\n    >\n      {/* Debug indicator */}\n      <div style={{\n        position: 'fixed',\n        top: '20px',\n        right: '20px',\n        background: 'rgba(0,0,0,0.8)',\n        color: 'white',\n        padding: '10px',\n        borderRadius: '5px',\n        fontSize: '14px',\n        zIndex: 10000,\n        fontFamily: 'monospace'\n      }}>\n        ScrollStack Debug:<br/>\n        Progress: {(scrollProgress * 100).toFixed(0)}%<br/>\n        Cards: {childrenArray.length}\n      </div>\n\n      <div className=\"scroll-stack-container\">\n        {childrenArray.map((child, index) => (\n          <div\n            key={index}\n            className=\"scroll-stack-item\"\n            style={getStackStyle(index, childrenArray.length)}\n          >\n            {child}\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n};\n\nexport default ScrollStack;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,WAAW,GAAGA,CAAC;EACnBC,QAAQ;EACRC,SAAS,GAAG,EAAE;EACdC,KAAK,GAAG,CAAC;AACX,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGX,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAMY,YAAY,GAAGV,MAAM,CAAC,IAAI,CAAC;EAEjCD,SAAS,CAAC,MAAM;IACd,MAAMY,YAAY,GAAGA,CAAA,KAAM;MACzB,IAAI,CAACD,YAAY,CAACE,OAAO,EAAE;MAE3B,MAAMC,IAAI,GAAGH,YAAY,CAACE,OAAO,CAACE,qBAAqB,CAAC,CAAC;MACzD,MAAMC,YAAY,GAAGC,MAAM,CAACC,WAAW;;MAEvC;MACA;MACA,MAAMC,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EACrC,CAACN,YAAY,GAAGF,IAAI,CAACS,GAAG,KAAKP,YAAY,GAAGF,IAAI,CAACU,MAAM,CACzD,CAAC,CAAC;MAEFd,iBAAiB,CAACS,QAAQ,CAAC;;MAE3B;MACAM,OAAO,CAACC,GAAG,CAAC,oBAAoBZ,IAAI,CAACS,GAAG,CAACI,OAAO,CAAC,CAAC,CAAC,cAAcR,QAAQ,CAACQ,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;IACzF,CAAC;IAEDV,MAAM,CAACW,gBAAgB,CAAC,QAAQ,EAAEhB,YAAY,EAAE;MAAEiB,OAAO,EAAE;IAAK,CAAC,CAAC;IAClEjB,YAAY,CAAC,CAAC;IAEd,OAAO,MAAMK,MAAM,CAACa,mBAAmB,CAAC,QAAQ,EAAElB,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMmB,aAAa,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACtC;IACA,IAAID,KAAK,KAAK,CAAC,EAAE;MACfP,OAAO,CAACC,GAAG,CAAC,yCAAyCjB,cAAc,CAACkB,OAAO,CAAC,CAAC,CAAC,iBAAiBO,WAAW,EAAE,CAAC;IAC/G;;IAEA;IACA,MAAMC,QAAQ,GAAGH,KAAK,GAAGE,WAAW,CAAC,CAAC;IACtC,MAAME,OAAO,GAAGJ,KAAK,GAAG,GAAG,CAAC,CAAC;IAC7B,MAAMK,QAAQ,GAAGF,QAAQ,GAAI1B,cAAc,IAAI2B,OAAO,GAAGD,QAAQ,CAAE;;IAEnE;IACA,MAAMG,YAAY,GAAGlB,IAAI,CAACmB,GAAG,CAACC,UAAU,EAAER,KAAK,CAAC,CAAC,CAAC;IAClD,MAAMS,WAAW,GAAG,CAAC,CAAC,CAAC;IACvB,MAAMC,YAAY,GAAGJ,YAAY,GAAI7B,cAAc,IAAIgC,WAAW,GAAGH,YAAY,CAAE;;IAEnF;IACA,MAAMK,OAAO,GAAG,GAAG,GAAIlC,cAAc,GAAG,GAAI;;IAE5C;IACA,MAAMmC,QAAQ,GAAG,CAAC,CAAC,GAAGnC,cAAc,IAAIuB,KAAK,GAAG,CAAC;;IAEjD;IACA,IAAIA,KAAK,KAAK,CAAC,EAAE;MACfP,OAAO,CAACC,GAAG,CAAC,QAAQM,KAAK,OAAOK,QAAQ,CAACV,OAAO,CAAC,CAAC,CAAC,WAAWe,YAAY,CAACf,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;IAC1F;IAEA,OAAO;MACLkB,SAAS,EAAE,oCAAoCR,QAAQ,aAAaK,YAAY,aAAaE,QAAQ,MAAM;MAC3GE,MAAM,EAAEb,KAAK,GAAGD,KAAK;MACrBW,OAAO,EAAEA,OAAO;MAChBI,eAAe,EAAE;IACnB,CAAC;EACH,CAAC;;EAED;EACA,MAAMC,aAAa,GAAGlD,KAAK,CAACmD,QAAQ,CAACC,OAAO,CAAC7C,QAAQ,CAAC;EAEtD,oBACEF,OAAA;IACEgD,GAAG,EAAExC,YAAa;IAClBL,SAAS,EAAE,gBAAgBA,SAAS,EAAG;IACvCC,KAAK,EAAEA,KAAM;IAAAF,QAAA,gBAGbF,OAAA;MAAKI,KAAK,EAAE;QACV6C,QAAQ,EAAE,OAAO;QACjB7B,GAAG,EAAE,MAAM;QACX8B,KAAK,EAAE,MAAM;QACbC,UAAU,EAAE,iBAAiB;QAC7BC,KAAK,EAAE,OAAO;QACdC,OAAO,EAAE,MAAM;QACfC,YAAY,EAAE,KAAK;QACnBC,QAAQ,EAAE,MAAM;QAChBZ,MAAM,EAAE,KAAK;QACba,UAAU,EAAE;MACd,CAAE;MAAAtD,QAAA,GAAC,oBACiB,eAAAF,OAAA;QAAAyD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,cACb,EAAC,CAACtD,cAAc,GAAG,GAAG,EAAEkB,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC,eAAAxB,OAAA;QAAAyD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,WAC5C,EAACf,aAAa,CAACgB,MAAM;IAAA;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eAEN5D,OAAA;MAAKG,SAAS,EAAC,wBAAwB;MAAAD,QAAA,EACpC2C,aAAa,CAACiB,GAAG,CAAC,CAACC,KAAK,EAAElC,KAAK,kBAC9B7B,OAAA;QAEEG,SAAS,EAAC,mBAAmB;QAC7BC,KAAK,EAAEwB,aAAa,CAACC,KAAK,EAAEgB,aAAa,CAACgB,MAAM,CAAE;QAAA3D,QAAA,EAEjD6D;MAAK,GAJDlC,KAAK;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKP,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvD,EAAA,CA5GIJ,WAAW;AAAA+D,EAAA,GAAX/D,WAAW;AA8GjB,eAAeA,WAAW;AAAC,IAAA+D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}