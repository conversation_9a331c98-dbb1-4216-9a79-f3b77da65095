{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ShinyText.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport './ShinyText.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ShinyText = ({\n  text = \"Portfolio\",\n  className = \"\",\n  style = {},\n  shimmerWidth = 100,\n  shimmerDuration = 2000,\n  fontSize = \"2rem\"\n}) => {\n  _s();\n  const textRef = useRef(null);\n  useEffect(() => {\n    const element = textRef.current;\n    if (!element) return;\n    const animate = () => {\n      element.style.setProperty('--shimmer-width', `${shimmerWidth}px`);\n      element.style.setProperty('--shimmer-duration', `${shimmerDuration}ms`);\n    };\n    animate();\n  }, [shimmerWidth, shimmerDuration]);\n  return /*#__PURE__*/_jsxDEV(\"span\", {\n    ref: textRef,\n    className: `shiny-text ${className}`,\n    style: {\n      fontSize,\n      ...style\n    },\n    children: text\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 5\n  }, this);\n};\n_s(ShinyText, \"03a/W9zemG2g1Vmhz2i1tulxw6U=\");\n_c = ShinyText;\nexport default ShinyText;\nvar _c;\n$RefreshReg$(_c, \"ShinyText\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "ShinyText", "text", "className", "style", "shimmer<PERSON>idth", "shimmerDuration", "fontSize", "_s", "textRef", "element", "current", "animate", "setProperty", "ref", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ShinyText.js"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\nimport './ShinyText.css';\n\nconst ShinyText = ({ \n  text = \"Portfolio\", \n  className = \"\",\n  style = {},\n  shimmerWidth = 100,\n  shimmerDuration = 2000,\n  fontSize = \"2rem\"\n}) => {\n  const textRef = useRef(null);\n\n  useEffect(() => {\n    const element = textRef.current;\n    if (!element) return;\n\n    const animate = () => {\n      element.style.setProperty('--shimmer-width', `${shimmerWidth}px`);\n      element.style.setProperty('--shimmer-duration', `${shimmerDuration}ms`);\n    };\n\n    animate();\n  }, [shimmerWidth, shimmerDuration]);\n\n  return (\n    <span \n      ref={textRef}\n      className={`shiny-text ${className}`}\n      style={{ fontSize, ...style }}\n    >\n      {text}\n    </span>\n  );\n};\n\nexport default ShinyText;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzB,MAAMC,SAAS,GAAGA,CAAC;EACjBC,IAAI,GAAG,WAAW;EAClBC,SAAS,GAAG,EAAE;EACdC,KAAK,GAAG,CAAC,CAAC;EACVC,YAAY,GAAG,GAAG;EAClBC,eAAe,GAAG,IAAI;EACtBC,QAAQ,GAAG;AACb,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,OAAO,GAAGX,MAAM,CAAC,IAAI,CAAC;EAE5BD,SAAS,CAAC,MAAM;IACd,MAAMa,OAAO,GAAGD,OAAO,CAACE,OAAO;IAC/B,IAAI,CAACD,OAAO,EAAE;IAEd,MAAME,OAAO,GAAGA,CAAA,KAAM;MACpBF,OAAO,CAACN,KAAK,CAACS,WAAW,CAAC,iBAAiB,EAAE,GAAGR,YAAY,IAAI,CAAC;MACjEK,OAAO,CAACN,KAAK,CAACS,WAAW,CAAC,oBAAoB,EAAE,GAAGP,eAAe,IAAI,CAAC;IACzE,CAAC;IAEDM,OAAO,CAAC,CAAC;EACX,CAAC,EAAE,CAACP,YAAY,EAAEC,eAAe,CAAC,CAAC;EAEnC,oBACEN,OAAA;IACEc,GAAG,EAAEL,OAAQ;IACbN,SAAS,EAAE,cAAcA,SAAS,EAAG;IACrCC,KAAK,EAAE;MAAEG,QAAQ;MAAE,GAAGH;IAAM,CAAE;IAAAW,QAAA,EAE7Bb;EAAI;IAAAc,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEX,CAAC;AAACX,EAAA,CA/BIP,SAAS;AAAAmB,EAAA,GAATnB,SAAS;AAiCf,eAAeA,SAAS;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}