{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ScrollStack.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport \"./ScrollStack.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const ScrollStackItem = ({\n  children,\n  itemClassName = \"\"\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: `scroll-stack-card ${itemClassName}`.trim(),\n  children: children\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 5,\n  columnNumber: 3\n}, this);\n_c = ScrollStackItem;\nconst ScrollStack = ({\n  children,\n  className = \"\"\n}) => {\n  _s();\n  const [scrollProgress, setScrollProgress] = useState(0);\n  const containerRef = useRef(null);\n  const calculateProgress = useCallback((scrollTop, start, end) => {\n    if (scrollTop < start) return 0;\n    if (scrollTop > end) return 1;\n    return (scrollTop - start) / (end - start);\n  }, []);\n  const parsePercentage = useCallback((value, containerHeight) => {\n    if (typeof value === 'string' && value.includes('%')) {\n      return parseFloat(value) / 100 * containerHeight;\n    }\n    return parseFloat(value);\n  }, []);\n  const updateCardTransforms = useCallback(() => {\n    const scroller = scrollerRef.current;\n    if (!scroller || !cardsRef.current.length || isUpdatingRef.current) return;\n    isUpdatingRef.current = true;\n    const scrollTop = scroller.scrollTop;\n    const containerHeight = scroller.clientHeight;\n    const stackPositionPx = parsePercentage(stackPosition, containerHeight);\n    const scaleEndPositionPx = parsePercentage(scaleEndPosition, containerHeight);\n    const endElement = scroller.querySelector('.scroll-stack-end');\n    const endElementTop = endElement ? endElement.offsetTop : 0;\n    cardsRef.current.forEach((card, i) => {\n      if (!card) return;\n      const cardTop = card.offsetTop;\n      const triggerStart = cardTop - stackPositionPx - itemStackDistance * i;\n      const triggerEnd = cardTop - scaleEndPositionPx;\n      const pinStart = cardTop - stackPositionPx - itemStackDistance * i;\n      const pinEnd = endElementTop - containerHeight / 2;\n      const scaleProgress = calculateProgress(scrollTop, triggerStart, triggerEnd);\n      const targetScale = baseScale + i * itemScale;\n      const scale = 1 - scaleProgress * (1 - targetScale);\n      const rotation = rotationAmount ? i * rotationAmount * scaleProgress : 0;\n      let blur = 0;\n      if (blurAmount) {\n        let topCardIndex = 0;\n        for (let j = 0; j < cardsRef.current.length; j++) {\n          const jCardTop = cardsRef.current[j].offsetTop;\n          const jTriggerStart = jCardTop - stackPositionPx - itemStackDistance * j;\n          if (scrollTop >= jTriggerStart) {\n            topCardIndex = j;\n          }\n        }\n        if (i < topCardIndex) {\n          const depthInStack = topCardIndex - i;\n          blur = Math.max(0, depthInStack * blurAmount);\n        }\n      }\n      let translateY = 0;\n      const isPinned = scrollTop >= pinStart && scrollTop <= pinEnd;\n      if (isPinned) {\n        translateY = scrollTop - cardTop + stackPositionPx + itemStackDistance * i;\n      } else if (scrollTop > pinEnd) {\n        translateY = pinEnd - cardTop + stackPositionPx + itemStackDistance * i;\n      }\n      const newTransform = {\n        translateY: Math.round(translateY * 100) / 100,\n        scale: Math.round(scale * 1000) / 1000,\n        rotation: Math.round(rotation * 100) / 100,\n        blur: Math.round(blur * 100) / 100\n      };\n      const lastTransform = lastTransformsRef.current.get(i);\n      const hasChanged = !lastTransform || Math.abs(lastTransform.translateY - newTransform.translateY) > 0.1 || Math.abs(lastTransform.scale - newTransform.scale) > 0.001 || Math.abs(lastTransform.rotation - newTransform.rotation) > 0.1 || Math.abs(lastTransform.blur - newTransform.blur) > 0.1;\n      if (hasChanged) {\n        const transform = `translate3d(0, ${newTransform.translateY}px, 0) scale(${newTransform.scale}) rotate(${newTransform.rotation}deg)`;\n        const filter = newTransform.blur > 0 ? `blur(${newTransform.blur}px)` : '';\n        card.style.transform = transform;\n        card.style.filter = filter;\n        lastTransformsRef.current.set(i, newTransform);\n      }\n      if (i === cardsRef.current.length - 1) {\n        const isInView = scrollTop >= pinStart && scrollTop <= pinEnd;\n        if (isInView && !stackCompletedRef.current) {\n          var _onStackComplete;\n          stackCompletedRef.current = true;\n          (_onStackComplete = onStackComplete) === null || _onStackComplete === void 0 ? void 0 : _onStackComplete();\n        } else if (!isInView && stackCompletedRef.current) {\n          stackCompletedRef.current = false;\n        }\n      }\n    });\n    isUpdatingRef.current = false;\n  }, [itemScale, itemStackDistance, stackPosition, scaleEndPosition, baseScale, rotationAmount, blurAmount, onStackComplete, calculateProgress, parsePercentage]);\n  const handleScroll = useCallback(() => {\n    updateCardTransforms();\n  }, [updateCardTransforms]);\n  useLayoutEffect(() => {\n    const scroller = scrollerRef.current;\n    if (!scroller) return;\n    const cards = Array.from(scroller.querySelectorAll(\".scroll-stack-card\"));\n    cardsRef.current = cards;\n    const transformsCache = lastTransformsRef.current;\n    cards.forEach((card, i) => {\n      if (i < cards.length - 1) {\n        card.style.marginBottom = `${itemDistance}px`;\n      }\n      card.style.willChange = 'transform, filter';\n      card.style.transformOrigin = 'top center';\n      card.style.backfaceVisibility = 'hidden';\n      card.style.transform = 'translateZ(0)';\n      card.style.webkitTransform = 'translateZ(0)';\n      card.style.perspective = '1000px';\n      card.style.webkitPerspective = '1000px';\n    });\n\n    // Add scroll listener\n    scroller.addEventListener('scroll', handleScroll, {\n      passive: true\n    });\n    updateCardTransforms();\n    return () => {\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n      }\n      scroller.removeEventListener('scroll', handleScroll);\n      stackCompletedRef.current = false;\n      cardsRef.current = [];\n      transformsCache.clear();\n      isUpdatingRef.current = false;\n    };\n  }, [itemDistance, itemScale, itemStackDistance, stackPosition, scaleEndPosition, baseScale, scaleDuration, rotationAmount, blurAmount, onStackComplete, updateCardTransforms, handleScroll]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `scroll-stack-scroller ${className}`.trim(),\n    ref: scrollerRef,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"scroll-stack-inner\",\n      children: [children, /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"scroll-stack-end\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 186,\n    columnNumber: 5\n  }, this);\n};\n_s(ScrollStack, \"FIBh/5QKnpvBy+1LokObc2JqfSs=\");\n_c2 = ScrollStack;\nexport default ScrollStack;\nvar _c, _c2;\n$RefreshReg$(_c, \"ScrollStackItem\");\n$RefreshReg$(_c2, \"ScrollStack\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "ScrollStackItem", "children", "itemClassName", "className", "trim", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "ScrollStack", "_s", "scrollProgress", "setScrollProgress", "containerRef", "calculateProgress", "useCallback", "scrollTop", "start", "end", "parsePercentage", "value", "containerHeight", "includes", "parseFloat", "updateCardTransforms", "scroller", "scrollerRef", "current", "cardsRef", "length", "isUpdatingRef", "clientHeight", "stackPositionPx", "stackPosition", "scaleEndPositionPx", "scaleEndPosition", "endElement", "querySelector", "endElementTop", "offsetTop", "for<PERSON>ach", "card", "i", "cardTop", "triggerStart", "itemStackDistance", "triggerEnd", "pinStart", "pinEnd", "scaleProgress", "targetScale", "baseScale", "itemScale", "scale", "rotation", "rotationAmount", "blur", "blurAmount", "topCardIndex", "j", "jCardTop", "jTriggerStart", "depthInStack", "Math", "max", "translateY", "isPinned", "newTransform", "round", "lastTransform", "lastTransformsRef", "get", "has<PERSON><PERSON>ed", "abs", "transform", "filter", "style", "set", "isInView", "stackCompletedRef", "_onStackComplete", "onStackComplete", "handleScroll", "useLayoutEffect", "cards", "Array", "from", "querySelectorAll", "transforms<PERSON>ache", "marginBottom", "itemDistance", "<PERSON><PERSON><PERSON><PERSON>", "transform<PERSON><PERSON>in", "backfaceVisibility", "webkitTransform", "perspective", "webkitPerspective", "addEventListener", "passive", "animationFrameRef", "cancelAnimationFrame", "removeEventListener", "clear", "scaleDuration", "ref", "_c2", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ScrollStack.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport \"./ScrollStack.css\";\n\nexport const ScrollStackItem = ({ children, itemClassName = \"\" }) => (\n  <div className={`scroll-stack-card ${itemClassName}`.trim()}>{children}</div>\n);\n\nconst ScrollStack = ({\n  children,\n  className = \"\"\n}) => {\n  const [scrollProgress, setScrollProgress] = useState(0);\n  const containerRef = useRef(null);\n\n  const calculateProgress = useCallback((scrollTop, start, end) => {\n    if (scrollTop < start) return 0;\n    if (scrollTop > end) return 1;\n    return (scrollTop - start) / (end - start);\n  }, []);\n\n  const parsePercentage = useCallback((value, containerHeight) => {\n    if (typeof value === 'string' && value.includes('%')) {\n      return (parseFloat(value) / 100) * containerHeight;\n    }\n    return parseFloat(value);\n  }, []);\n\n  const updateCardTransforms = useCallback(() => {\n    const scroller = scrollerRef.current;\n    if (!scroller || !cardsRef.current.length || isUpdatingRef.current) return;\n\n    isUpdatingRef.current = true;\n\n    const scrollTop = scroller.scrollTop;\n    const containerHeight = scroller.clientHeight;\n    const stackPositionPx = parsePercentage(stackPosition, containerHeight);\n    const scaleEndPositionPx = parsePercentage(scaleEndPosition, containerHeight);\n    const endElement = scroller.querySelector('.scroll-stack-end');\n    const endElementTop = endElement ? endElement.offsetTop : 0;\n\n    cardsRef.current.forEach((card, i) => {\n      if (!card) return;\n\n      const cardTop = card.offsetTop;\n      const triggerStart = cardTop - stackPositionPx - (itemStackDistance * i);\n      const triggerEnd = cardTop - scaleEndPositionPx;\n      const pinStart = cardTop - stackPositionPx - (itemStackDistance * i);\n      const pinEnd = endElementTop - containerHeight / 2;\n\n      const scaleProgress = calculateProgress(scrollTop, triggerStart, triggerEnd);\n      const targetScale = baseScale + (i * itemScale);\n      const scale = 1 - scaleProgress * (1 - targetScale);\n      const rotation = rotationAmount ? i * rotationAmount * scaleProgress : 0;\n\n      let blur = 0;\n      if (blurAmount) {\n        let topCardIndex = 0;\n        for (let j = 0; j < cardsRef.current.length; j++) {\n          const jCardTop = cardsRef.current[j].offsetTop;\n          const jTriggerStart = jCardTop - stackPositionPx - (itemStackDistance * j);\n          if (scrollTop >= jTriggerStart) {\n            topCardIndex = j;\n          }\n        }\n\n        if (i < topCardIndex) {\n          const depthInStack = topCardIndex - i;\n          blur = Math.max(0, depthInStack * blurAmount);\n        }\n      }\n\n      let translateY = 0;\n      const isPinned = scrollTop >= pinStart && scrollTop <= pinEnd;\n\n      if (isPinned) {\n        translateY = scrollTop - cardTop + stackPositionPx + (itemStackDistance * i);\n      } else if (scrollTop > pinEnd) {\n        translateY = pinEnd - cardTop + stackPositionPx + (itemStackDistance * i);\n      }\n\n      const newTransform = {\n        translateY: Math.round(translateY * 100) / 100,\n        scale: Math.round(scale * 1000) / 1000,\n        rotation: Math.round(rotation * 100) / 100,\n        blur: Math.round(blur * 100) / 100\n      };\n\n      const lastTransform = lastTransformsRef.current.get(i);\n      const hasChanged = !lastTransform ||\n        Math.abs(lastTransform.translateY - newTransform.translateY) > 0.1 ||\n        Math.abs(lastTransform.scale - newTransform.scale) > 0.001 ||\n        Math.abs(lastTransform.rotation - newTransform.rotation) > 0.1 ||\n        Math.abs(lastTransform.blur - newTransform.blur) > 0.1;\n\n      if (hasChanged) {\n        const transform = `translate3d(0, ${newTransform.translateY}px, 0) scale(${newTransform.scale}) rotate(${newTransform.rotation}deg)`;\n        const filter = newTransform.blur > 0 ? `blur(${newTransform.blur}px)` : '';\n\n        card.style.transform = transform;\n        card.style.filter = filter;\n\n        lastTransformsRef.current.set(i, newTransform);\n      }\n\n      if (i === cardsRef.current.length - 1) {\n        const isInView = scrollTop >= pinStart && scrollTop <= pinEnd;\n        if (isInView && !stackCompletedRef.current) {\n          stackCompletedRef.current = true;\n          onStackComplete?.();\n        } else if (!isInView && stackCompletedRef.current) {\n          stackCompletedRef.current = false;\n        }\n      }\n    });\n\n    isUpdatingRef.current = false;\n  }, [\n    itemScale,\n    itemStackDistance,\n    stackPosition,\n    scaleEndPosition,\n    baseScale,\n    rotationAmount,\n    blurAmount,\n    onStackComplete,\n    calculateProgress,\n    parsePercentage,\n  ]);\n\n  const handleScroll = useCallback(() => {\n    updateCardTransforms();\n  }, [updateCardTransforms]);\n\n  useLayoutEffect(() => {\n    const scroller = scrollerRef.current;\n    if (!scroller) return;\n\n    const cards = Array.from(scroller.querySelectorAll(\".scroll-stack-card\"));\n    cardsRef.current = cards;\n    const transformsCache = lastTransformsRef.current;\n\n    cards.forEach((card, i) => {\n      if (i < cards.length - 1) {\n        card.style.marginBottom = `${itemDistance}px`;\n      }\n      card.style.willChange = 'transform, filter';\n      card.style.transformOrigin = 'top center';\n      card.style.backfaceVisibility = 'hidden';\n      card.style.transform = 'translateZ(0)';\n      card.style.webkitTransform = 'translateZ(0)';\n      card.style.perspective = '1000px';\n      card.style.webkitPerspective = '1000px';\n    });\n\n    // Add scroll listener\n    scroller.addEventListener('scroll', handleScroll, { passive: true });\n\n    updateCardTransforms();\n\n    return () => {\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n      }\n      scroller.removeEventListener('scroll', handleScroll);\n      stackCompletedRef.current = false;\n      cardsRef.current = [];\n      transformsCache.clear();\n      isUpdatingRef.current = false;\n    };\n  }, [\n    itemDistance,\n    itemScale,\n    itemStackDistance,\n    stackPosition,\n    scaleEndPosition,\n    baseScale,\n    scaleDuration,\n    rotationAmount,\n    blurAmount,\n    onStackComplete,\n    updateCardTransforms,\n    handleScroll,\n  ]);\n\n  return (\n    <div\n      className={`scroll-stack-scroller ${className}`.trim()}\n      ref={scrollerRef}\n    >\n      <div className=\"scroll-stack-inner\">\n        {children}\n        {/* Spacer so the last pin can release cleanly */}\n        <div className=\"scroll-stack-end\" />\n      </div>\n    </div>\n  );\n};\n\nexport default ScrollStack;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,OAAO,MAAMC,eAAe,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,aAAa,GAAG;AAAG,CAAC,kBAC9DH,OAAA;EAAKI,SAAS,EAAE,qBAAqBD,aAAa,EAAE,CAACE,IAAI,CAAC,CAAE;EAAAH,QAAA,EAAEA;AAAQ;EAAAI,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAAM,CAC7E;AAACC,EAAA,GAFWT,eAAe;AAI5B,MAAMU,WAAW,GAAGA,CAAC;EACnBT,QAAQ;EACRE,SAAS,GAAG;AACd,CAAC,KAAK;EAAAQ,EAAA;EACJ,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGlB,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAMmB,YAAY,GAAGjB,MAAM,CAAC,IAAI,CAAC;EAEjC,MAAMkB,iBAAiB,GAAGC,WAAW,CAAC,CAACC,SAAS,EAAEC,KAAK,EAAEC,GAAG,KAAK;IAC/D,IAAIF,SAAS,GAAGC,KAAK,EAAE,OAAO,CAAC;IAC/B,IAAID,SAAS,GAAGE,GAAG,EAAE,OAAO,CAAC;IAC7B,OAAO,CAACF,SAAS,GAAGC,KAAK,KAAKC,GAAG,GAAGD,KAAK,CAAC;EAC5C,CAAC,EAAE,EAAE,CAAC;EAEN,MAAME,eAAe,GAAGJ,WAAW,CAAC,CAACK,KAAK,EAAEC,eAAe,KAAK;IAC9D,IAAI,OAAOD,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACE,QAAQ,CAAC,GAAG,CAAC,EAAE;MACpD,OAAQC,UAAU,CAACH,KAAK,CAAC,GAAG,GAAG,GAAIC,eAAe;IACpD;IACA,OAAOE,UAAU,CAACH,KAAK,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,oBAAoB,GAAGT,WAAW,CAAC,MAAM;IAC7C,MAAMU,QAAQ,GAAGC,WAAW,CAACC,OAAO;IACpC,IAAI,CAACF,QAAQ,IAAI,CAACG,QAAQ,CAACD,OAAO,CAACE,MAAM,IAAIC,aAAa,CAACH,OAAO,EAAE;IAEpEG,aAAa,CAACH,OAAO,GAAG,IAAI;IAE5B,MAAMX,SAAS,GAAGS,QAAQ,CAACT,SAAS;IACpC,MAAMK,eAAe,GAAGI,QAAQ,CAACM,YAAY;IAC7C,MAAMC,eAAe,GAAGb,eAAe,CAACc,aAAa,EAAEZ,eAAe,CAAC;IACvE,MAAMa,kBAAkB,GAAGf,eAAe,CAACgB,gBAAgB,EAAEd,eAAe,CAAC;IAC7E,MAAMe,UAAU,GAAGX,QAAQ,CAACY,aAAa,CAAC,mBAAmB,CAAC;IAC9D,MAAMC,aAAa,GAAGF,UAAU,GAAGA,UAAU,CAACG,SAAS,GAAG,CAAC;IAE3DX,QAAQ,CAACD,OAAO,CAACa,OAAO,CAAC,CAACC,IAAI,EAAEC,CAAC,KAAK;MACpC,IAAI,CAACD,IAAI,EAAE;MAEX,MAAME,OAAO,GAAGF,IAAI,CAACF,SAAS;MAC9B,MAAMK,YAAY,GAAGD,OAAO,GAAGX,eAAe,GAAIa,iBAAiB,GAAGH,CAAE;MACxE,MAAMI,UAAU,GAAGH,OAAO,GAAGT,kBAAkB;MAC/C,MAAMa,QAAQ,GAAGJ,OAAO,GAAGX,eAAe,GAAIa,iBAAiB,GAAGH,CAAE;MACpE,MAAMM,MAAM,GAAGV,aAAa,GAAGjB,eAAe,GAAG,CAAC;MAElD,MAAM4B,aAAa,GAAGnC,iBAAiB,CAACE,SAAS,EAAE4B,YAAY,EAAEE,UAAU,CAAC;MAC5E,MAAMI,WAAW,GAAGC,SAAS,GAAIT,CAAC,GAAGU,SAAU;MAC/C,MAAMC,KAAK,GAAG,CAAC,GAAGJ,aAAa,IAAI,CAAC,GAAGC,WAAW,CAAC;MACnD,MAAMI,QAAQ,GAAGC,cAAc,GAAGb,CAAC,GAAGa,cAAc,GAAGN,aAAa,GAAG,CAAC;MAExE,IAAIO,IAAI,GAAG,CAAC;MACZ,IAAIC,UAAU,EAAE;QACd,IAAIC,YAAY,GAAG,CAAC;QACpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/B,QAAQ,CAACD,OAAO,CAACE,MAAM,EAAE8B,CAAC,EAAE,EAAE;UAChD,MAAMC,QAAQ,GAAGhC,QAAQ,CAACD,OAAO,CAACgC,CAAC,CAAC,CAACpB,SAAS;UAC9C,MAAMsB,aAAa,GAAGD,QAAQ,GAAG5B,eAAe,GAAIa,iBAAiB,GAAGc,CAAE;UAC1E,IAAI3C,SAAS,IAAI6C,aAAa,EAAE;YAC9BH,YAAY,GAAGC,CAAC;UAClB;QACF;QAEA,IAAIjB,CAAC,GAAGgB,YAAY,EAAE;UACpB,MAAMI,YAAY,GAAGJ,YAAY,GAAGhB,CAAC;UACrCc,IAAI,GAAGO,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEF,YAAY,GAAGL,UAAU,CAAC;QAC/C;MACF;MAEA,IAAIQ,UAAU,GAAG,CAAC;MAClB,MAAMC,QAAQ,GAAGlD,SAAS,IAAI+B,QAAQ,IAAI/B,SAAS,IAAIgC,MAAM;MAE7D,IAAIkB,QAAQ,EAAE;QACZD,UAAU,GAAGjD,SAAS,GAAG2B,OAAO,GAAGX,eAAe,GAAIa,iBAAiB,GAAGH,CAAE;MAC9E,CAAC,MAAM,IAAI1B,SAAS,GAAGgC,MAAM,EAAE;QAC7BiB,UAAU,GAAGjB,MAAM,GAAGL,OAAO,GAAGX,eAAe,GAAIa,iBAAiB,GAAGH,CAAE;MAC3E;MAEA,MAAMyB,YAAY,GAAG;QACnBF,UAAU,EAAEF,IAAI,CAACK,KAAK,CAACH,UAAU,GAAG,GAAG,CAAC,GAAG,GAAG;QAC9CZ,KAAK,EAAEU,IAAI,CAACK,KAAK,CAACf,KAAK,GAAG,IAAI,CAAC,GAAG,IAAI;QACtCC,QAAQ,EAAES,IAAI,CAACK,KAAK,CAACd,QAAQ,GAAG,GAAG,CAAC,GAAG,GAAG;QAC1CE,IAAI,EAAEO,IAAI,CAACK,KAAK,CAACZ,IAAI,GAAG,GAAG,CAAC,GAAG;MACjC,CAAC;MAED,MAAMa,aAAa,GAAGC,iBAAiB,CAAC3C,OAAO,CAAC4C,GAAG,CAAC7B,CAAC,CAAC;MACtD,MAAM8B,UAAU,GAAG,CAACH,aAAa,IAC/BN,IAAI,CAACU,GAAG,CAACJ,aAAa,CAACJ,UAAU,GAAGE,YAAY,CAACF,UAAU,CAAC,GAAG,GAAG,IAClEF,IAAI,CAACU,GAAG,CAACJ,aAAa,CAAChB,KAAK,GAAGc,YAAY,CAACd,KAAK,CAAC,GAAG,KAAK,IAC1DU,IAAI,CAACU,GAAG,CAACJ,aAAa,CAACf,QAAQ,GAAGa,YAAY,CAACb,QAAQ,CAAC,GAAG,GAAG,IAC9DS,IAAI,CAACU,GAAG,CAACJ,aAAa,CAACb,IAAI,GAAGW,YAAY,CAACX,IAAI,CAAC,GAAG,GAAG;MAExD,IAAIgB,UAAU,EAAE;QACd,MAAME,SAAS,GAAG,kBAAkBP,YAAY,CAACF,UAAU,gBAAgBE,YAAY,CAACd,KAAK,YAAYc,YAAY,CAACb,QAAQ,MAAM;QACpI,MAAMqB,MAAM,GAAGR,YAAY,CAACX,IAAI,GAAG,CAAC,GAAG,QAAQW,YAAY,CAACX,IAAI,KAAK,GAAG,EAAE;QAE1Ef,IAAI,CAACmC,KAAK,CAACF,SAAS,GAAGA,SAAS;QAChCjC,IAAI,CAACmC,KAAK,CAACD,MAAM,GAAGA,MAAM;QAE1BL,iBAAiB,CAAC3C,OAAO,CAACkD,GAAG,CAACnC,CAAC,EAAEyB,YAAY,CAAC;MAChD;MAEA,IAAIzB,CAAC,KAAKd,QAAQ,CAACD,OAAO,CAACE,MAAM,GAAG,CAAC,EAAE;QACrC,MAAMiD,QAAQ,GAAG9D,SAAS,IAAI+B,QAAQ,IAAI/B,SAAS,IAAIgC,MAAM;QAC7D,IAAI8B,QAAQ,IAAI,CAACC,iBAAiB,CAACpD,OAAO,EAAE;UAAA,IAAAqD,gBAAA;UAC1CD,iBAAiB,CAACpD,OAAO,GAAG,IAAI;UAChC,CAAAqD,gBAAA,GAAAC,eAAe,cAAAD,gBAAA,uBAAfA,gBAAA,CAAkB,CAAC;QACrB,CAAC,MAAM,IAAI,CAACF,QAAQ,IAAIC,iBAAiB,CAACpD,OAAO,EAAE;UACjDoD,iBAAiB,CAACpD,OAAO,GAAG,KAAK;QACnC;MACF;IACF,CAAC,CAAC;IAEFG,aAAa,CAACH,OAAO,GAAG,KAAK;EAC/B,CAAC,EAAE,CACDyB,SAAS,EACTP,iBAAiB,EACjBZ,aAAa,EACbE,gBAAgB,EAChBgB,SAAS,EACTI,cAAc,EACdE,UAAU,EACVwB,eAAe,EACfnE,iBAAiB,EACjBK,eAAe,CAChB,CAAC;EAEF,MAAM+D,YAAY,GAAGnE,WAAW,CAAC,MAAM;IACrCS,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,CAACA,oBAAoB,CAAC,CAAC;EAE1B2D,eAAe,CAAC,MAAM;IACpB,MAAM1D,QAAQ,GAAGC,WAAW,CAACC,OAAO;IACpC,IAAI,CAACF,QAAQ,EAAE;IAEf,MAAM2D,KAAK,GAAGC,KAAK,CAACC,IAAI,CAAC7D,QAAQ,CAAC8D,gBAAgB,CAAC,oBAAoB,CAAC,CAAC;IACzE3D,QAAQ,CAACD,OAAO,GAAGyD,KAAK;IACxB,MAAMI,eAAe,GAAGlB,iBAAiB,CAAC3C,OAAO;IAEjDyD,KAAK,CAAC5C,OAAO,CAAC,CAACC,IAAI,EAAEC,CAAC,KAAK;MACzB,IAAIA,CAAC,GAAG0C,KAAK,CAACvD,MAAM,GAAG,CAAC,EAAE;QACxBY,IAAI,CAACmC,KAAK,CAACa,YAAY,GAAG,GAAGC,YAAY,IAAI;MAC/C;MACAjD,IAAI,CAACmC,KAAK,CAACe,UAAU,GAAG,mBAAmB;MAC3ClD,IAAI,CAACmC,KAAK,CAACgB,eAAe,GAAG,YAAY;MACzCnD,IAAI,CAACmC,KAAK,CAACiB,kBAAkB,GAAG,QAAQ;MACxCpD,IAAI,CAACmC,KAAK,CAACF,SAAS,GAAG,eAAe;MACtCjC,IAAI,CAACmC,KAAK,CAACkB,eAAe,GAAG,eAAe;MAC5CrD,IAAI,CAACmC,KAAK,CAACmB,WAAW,GAAG,QAAQ;MACjCtD,IAAI,CAACmC,KAAK,CAACoB,iBAAiB,GAAG,QAAQ;IACzC,CAAC,CAAC;;IAEF;IACAvE,QAAQ,CAACwE,gBAAgB,CAAC,QAAQ,EAAEf,YAAY,EAAE;MAAEgB,OAAO,EAAE;IAAK,CAAC,CAAC;IAEpE1E,oBAAoB,CAAC,CAAC;IAEtB,OAAO,MAAM;MACX,IAAI2E,iBAAiB,CAACxE,OAAO,EAAE;QAC7ByE,oBAAoB,CAACD,iBAAiB,CAACxE,OAAO,CAAC;MACjD;MACAF,QAAQ,CAAC4E,mBAAmB,CAAC,QAAQ,EAAEnB,YAAY,CAAC;MACpDH,iBAAiB,CAACpD,OAAO,GAAG,KAAK;MACjCC,QAAQ,CAACD,OAAO,GAAG,EAAE;MACrB6D,eAAe,CAACc,KAAK,CAAC,CAAC;MACvBxE,aAAa,CAACH,OAAO,GAAG,KAAK;IAC/B,CAAC;EACH,CAAC,EAAE,CACD+D,YAAY,EACZtC,SAAS,EACTP,iBAAiB,EACjBZ,aAAa,EACbE,gBAAgB,EAChBgB,SAAS,EACToD,aAAa,EACbhD,cAAc,EACdE,UAAU,EACVwB,eAAe,EACfzD,oBAAoB,EACpB0D,YAAY,CACb,CAAC;EAEF,oBACEpF,OAAA;IACEI,SAAS,EAAE,yBAAyBA,SAAS,EAAE,CAACC,IAAI,CAAC,CAAE;IACvDqG,GAAG,EAAE9E,WAAY;IAAA1B,QAAA,eAEjBF,OAAA;MAAKI,SAAS,EAAC,oBAAoB;MAAAF,QAAA,GAChCA,QAAQ,eAETF,OAAA;QAAKI,SAAS,EAAC;MAAkB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACG,EAAA,CA7LID,WAAW;AAAAgG,GAAA,GAAXhG,WAAW;AA+LjB,eAAeA,WAAW;AAAC,IAAAD,EAAA,EAAAiG,GAAA;AAAAC,YAAA,CAAAlG,EAAA;AAAAkG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}