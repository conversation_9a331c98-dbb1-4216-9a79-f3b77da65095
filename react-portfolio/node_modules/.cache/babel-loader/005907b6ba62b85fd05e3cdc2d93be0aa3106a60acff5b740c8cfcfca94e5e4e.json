{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Ribbons.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport './Ribbons.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Ribbons = ({\n  children,\n  className = \"\",\n  style = {},\n  ribbonColor = \"rgba(78, 205, 196, 0.2)\",\n  ribbonWidth = 2,\n  ribbonSpeed = 2000\n}) => {\n  _s();\n  const [mousePosition, setMousePosition] = useState({\n    x: 0,\n    y: 0\n  });\n  const [isHovering, setIsHovering] = useState(false);\n  const containerRef = useRef(null);\n  useEffect(() => {\n    const handleMouseMove = e => {\n      if (containerRef.current && isHovering) {\n        const rect = containerRef.current.getBoundingClientRect();\n        const x = e.clientX - rect.left;\n        const y = e.clientY - rect.top;\n        setMousePosition({\n          x,\n          y\n        });\n      }\n    };\n    document.addEventListener('mousemove', handleMouseMove);\n    return () => document.removeEventListener('mousemove', handleMouseMove);\n  }, [isHovering]);\n  const handleMouseEnter = () => {\n    setIsHovering(true);\n  };\n  const handleMouseLeave = () => {\n    setIsHovering(false);\n    setMousePosition({\n      x: 0,\n      y: 0\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `ribbons-container ${className}`,\n    style: style,\n    children: [/*#__PURE__*/_jsxDEV(\"canvas\", {\n      ref: canvasRef,\n      className: \"ribbons-canvas\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ribbons-content\",\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 5\n  }, this);\n};\n_s(Ribbons, \"4l6ztoc+CnxzOJRez4d+AEJzzLU=\");\n_c = Ribbons;\nexport default Ribbons;\nvar _c;\n$RefreshReg$(_c, \"Ribbons\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "jsxDEV", "_jsxDEV", "Ribbons", "children", "className", "style", "ribbonColor", "ribbonWidth", "ribbonSpeed", "_s", "mousePosition", "setMousePosition", "x", "y", "isHovering", "setIsHovering", "containerRef", "handleMouseMove", "e", "current", "rect", "getBoundingClientRect", "clientX", "left", "clientY", "top", "document", "addEventListener", "removeEventListener", "handleMouseEnter", "handleMouseLeave", "ref", "canvasRef", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Ribbons.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport './Ribbons.css';\n\nconst Ribbons = ({\n  children,\n  className = \"\",\n  style = {},\n  ribbonColor = \"rgba(78, 205, 196, 0.2)\",\n  ribbonWidth = 2,\n  ribbonSpeed = 2000\n}) => {\n  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });\n  const [isHovering, setIsHovering] = useState(false);\n  const containerRef = useRef(null);\n\n  useEffect(() => {\n    const handleMouseMove = (e) => {\n      if (containerRef.current && isHovering) {\n        const rect = containerRef.current.getBoundingClientRect();\n        const x = e.clientX - rect.left;\n        const y = e.clientY - rect.top;\n\n        setMousePosition({ x, y });\n      }\n    };\n\n    document.addEventListener('mousemove', handleMouseMove);\n    return () => document.removeEventListener('mousemove', handleMouseMove);\n  }, [isHovering]);\n\n  const handleMouseEnter = () => {\n    setIsHovering(true);\n  };\n\n  const handleMouseLeave = () => {\n    setIsHovering(false);\n    setMousePosition({ x: 0, y: 0 });\n  };\n\n  return (\n    <div className={`ribbons-container ${className}`} style={style}>\n      <canvas\n        ref={canvasRef}\n        className=\"ribbons-canvas\"\n      />\n      <div className=\"ribbons-content\">\n        {children}\n      </div>\n    </div>\n  );\n};\n\nexport default Ribbons;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,OAAO,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvB,MAAMC,OAAO,GAAGA,CAAC;EACfC,QAAQ;EACRC,SAAS,GAAG,EAAE;EACdC,KAAK,GAAG,CAAC,CAAC;EACVC,WAAW,GAAG,yBAAyB;EACvCC,WAAW,GAAG,CAAC;EACfC,WAAW,GAAG;AAChB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGd,QAAQ,CAAC;IAAEe,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAClE,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMmB,YAAY,GAAGlB,MAAM,CAAC,IAAI,CAAC;EAEjCC,SAAS,CAAC,MAAM;IACd,MAAMkB,eAAe,GAAIC,CAAC,IAAK;MAC7B,IAAIF,YAAY,CAACG,OAAO,IAAIL,UAAU,EAAE;QACtC,MAAMM,IAAI,GAAGJ,YAAY,CAACG,OAAO,CAACE,qBAAqB,CAAC,CAAC;QACzD,MAAMT,CAAC,GAAGM,CAAC,CAACI,OAAO,GAAGF,IAAI,CAACG,IAAI;QAC/B,MAAMV,CAAC,GAAGK,CAAC,CAACM,OAAO,GAAGJ,IAAI,CAACK,GAAG;QAE9Bd,gBAAgB,CAAC;UAAEC,CAAC;UAAEC;QAAE,CAAC,CAAC;MAC5B;IACF,CAAC;IAEDa,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEV,eAAe,CAAC;IACvD,OAAO,MAAMS,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEX,eAAe,CAAC;EACzE,CAAC,EAAE,CAACH,UAAU,CAAC,CAAC;EAEhB,MAAMe,gBAAgB,GAAGA,CAAA,KAAM;IAC7Bd,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMe,gBAAgB,GAAGA,CAAA,KAAM;IAC7Bf,aAAa,CAAC,KAAK,CAAC;IACpBJ,gBAAgB,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC,CAAC;EAClC,CAAC;EAED,oBACEZ,OAAA;IAAKG,SAAS,EAAE,qBAAqBA,SAAS,EAAG;IAACC,KAAK,EAAEA,KAAM;IAAAF,QAAA,gBAC7DF,OAAA;MACE8B,GAAG,EAAEC,SAAU;MACf5B,SAAS,EAAC;IAAgB;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC,eACFnC,OAAA;MAAKG,SAAS,EAAC,iBAAiB;MAAAD,QAAA,EAC7BA;IAAQ;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3B,EAAA,CA/CIP,OAAO;AAAAmC,EAAA,GAAPnC,OAAO;AAiDb,eAAeA,OAAO;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}