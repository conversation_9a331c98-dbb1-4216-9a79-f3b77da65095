[{"/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/index.js": "1", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/App.js": "2", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Hero.js": "3", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/CircularText.js": "4", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/TextPressure.js": "5", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ShinyText.js": "6", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Navigation.js": "7", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/About.js": "8", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/MagicBento.js": "9", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ScrollFloat.js": "10", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Projects.js": "11", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Ribbons.js": "12", "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ScrollStack.js": "13"}, {"size": 254, "mtime": 1753007289437, "results": "14", "hashOfConfig": "15"}, {"size": 315, "mtime": 1753021346873, "results": "16", "hashOfConfig": "15"}, {"size": 1615, "mtime": 1753020547180, "results": "17", "hashOfConfig": "15"}, {"size": 1209, "mtime": 1753007340135, "results": "18", "hashOfConfig": "15"}, {"size": 2321, "mtime": 1753017108809, "results": "19", "hashOfConfig": "15"}, {"size": 779, "mtime": 1753007325960, "results": "20", "hashOfConfig": "15"}, {"size": 1207, "mtime": 1753020237302, "results": "21", "hashOfConfig": "15"}, {"size": 3921, "mtime": 1753020804986, "results": "22", "hashOfConfig": "15"}, {"size": 1907, "mtime": 1753020161295, "results": "23", "hashOfConfig": "15"}, {"size": 2014, "mtime": 1753020756418, "results": "24", "hashOfConfig": "15"}, {"size": 4290, "mtime": 1753038325247, "results": "25", "hashOfConfig": "15"}, {"size": 2085, "mtime": 1753025118440, "results": "26", "hashOfConfig": "15"}, {"size": 7276, "mtime": 1753038421687, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "am9nq7", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/index.js", [], [], "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/App.js", [], [], "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Hero.js", [], [], "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/CircularText.js", [], [], "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/TextPressure.js", [], [], "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ShinyText.js", [], [], "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Navigation.js", [], [], "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/About.js", [], [], "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/MagicBento.js", [], [], "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ScrollFloat.js", [], [], "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Projects.js", [], [], "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/Ribbons.js", [], [], "/Users/<USER>/Desktop/devdoses/portfolioDevDoses/react-portfolio/src/components/ScrollStack.js", [], []]